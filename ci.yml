#代码语言
Language: go
#默认的构建场景
Build:
  profile: prod
#定义所有的构建场景
Profiles:
  #专属云
  - profile:
    name: prod
    env:
    version: centos7u3
    command: sh build.sh prod
    upload_to_irep: true
  #小贷云
  - profile:
    name: prod_xd
    env:
    version: centos7u3
    command: sh build.sh prod_xd
    upload_to_irep: true
  #支付云
  - profile:
    name: prod_zf
    env:
    version: centos7u3
    command: sh build.sh prod_zf
    upload_to_irep: true
  #科技云
  - profile:
    name: prod_kj
    env:
    version: centos7u3
    command: sh build.sh prod_kj
    upload_to_irep: true
Output: output/*
