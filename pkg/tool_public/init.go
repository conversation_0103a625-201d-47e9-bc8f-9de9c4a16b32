package tool_public

import (
	"dxm/noah-sdk/noah_golang_sdk/src/libapptree"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/othirdservice/new-noah/service_management"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"time"
)

func GetProxyUrl(proxyBns string) (proxyUrl string, err error) {

	insts, err := service_management.GetInstanceByBnsSdk(proxyBns)
	if err != nil {
		olog.Error("getProxyIp by BNS:[%s] failed with err:[%+v]", proxyBns, err)
		return
	}

	if len(insts) == 0 {
		olog.Error("getProxyIp by BNS:[%s] failed, no available instance of proxyBns", proxyBns)
		err = errors.New("no available instance")
		return
	}

	//随机获取一个实例
	rand.Seed(time.Now().UnixNano())
	id := rand.Intn(len(insts))
	ip := libapptree.IpInt2String(insts[id].Ip)
	port := insts[id].Port
	proxyUrl = fmt.Sprintf("http://%s:%d", ip, port)
	olog.Info("proxyUrl is:[%s]", proxyUrl)
	return
}

// GetLocalIP 获取本机IP地址
func GetLocalIP() (string, error) {
	// 方法1: 通过连接外部地址获取本机IP
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		olog.Error("failed to dial external address, err:[%v]", err)
		// 如果方法1失败，尝试方法2
		return getLocalIPByInterface()
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	ip := localAddr.IP.String()
	olog.Info("local IP address is:[%s]", ip)
	return ip, nil
}

// getLocalIPByInterface 通过网络接口获取本机IP地址
func getLocalIPByInterface() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		olog.Error("failed to get network interfaces, err:[%v]", err)
		return "", err
	}

	for _, iface := range interfaces {
		// 跳过回环接口和未启用的接口
		if iface.Flags&net.FlagUp == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			var ip net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ip = v.IP
			case *net.IPAddr:
				ip = v.IP
			}

			// 只返回IPv4地址，排除回环地址
			if ip != nil && ip.To4() != nil && !ip.IsLoopback() {
				// 优先返回私有网络地址
				if isPrivateIP(ip) {
					olog.Info("local IP address is:[%s]", ip.String())
					return ip.String(), nil
				}
			}
		}
	}

	return "", errors.New("no valid local IP address found")
}

// isPrivateIP 判断是否为私有IP地址
func isPrivateIP(ip net.IP) bool {
	if ip.IsLoopback() || ip.IsLinkLocalMulticast() || ip.IsLinkLocalUnicast() {
		return false
	}

	// 私有网络地址范围:
	// 10.0.0.0/8
	// **********/12
	// ***********/16
	privateBlocks := []*net.IPNet{
		{IP: net.ParseIP("10.0.0.0"), Mask: net.CIDRMask(8, 32)},
		{IP: net.ParseIP("**********"), Mask: net.CIDRMask(12, 32)},
		{IP: net.ParseIP("***********"), Mask: net.CIDRMask(16, 32)},
	}

	for _, block := range privateBlocks {
		if block.Contains(ip) {
			return true
		}
	}

	return false
}
