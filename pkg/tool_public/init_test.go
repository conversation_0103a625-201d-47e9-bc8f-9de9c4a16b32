package tool_public

import (
	"testing"
)

func TestGetLocalIP(t *testing.T) {
	ip, err := GetLocalIP()
	if err != nil {
		t.<PERSON><PERSON>("GetLocalIP() failed: %v", err)
		return
	}
	
	if ip == "" {
		t.<PERSON><PERSON><PERSON>("GetLocalIP() returned empty IP address")
		return
	}
	
	t.Logf("Local IP address: %s", ip)
	
	// 验证IP格式是否正确
	if len(ip) < 7 || len(ip) > 15 {
		t.<PERSON><PERSON>("Invalid IP address format: %s", ip)
	}
}

func TestGetLocalIPByInterface(t *testing.T) {
	ip, err := getLocalIPByInterface()
	if err != nil {
		t.Logf("getLocalIPByInterface() failed (this might be expected in some environments): %v", err)
		return
	}
	
	if ip == "" {
		t.<PERSON>rror("getLocalIPByInterface() returned empty IP address")
		return
	}
	
	t.Logf("Local IP address by interface: %s", ip)
}
