package service_capacity

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/pkg/httpx"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/astaxie/beego"
)

const (
	ERROR_CODE_SUCCESS            = 0
	ERROR_CODE_NO_NEED_TO_EXECUTE = 6000
)

var (
	BaseURL    string
	Port       string
	URL        string
	ExecuteURL string
)

type ExecuteRequest struct {
	CapacityTaskID int               `json:"capacityTaskId"`
	ProductName    string            `json:"productName"`
	AppName        string            `json:"appName"`
	PlatformName   string            `json:"platformName"`
	ScaleType      string            `json:"scaleType"`
	ScaleModel     string            `json:"scaleModel"`
	NoahTag        map[string]string `json:"noahTag"`
	CustomTag      map[string]string `json:"customTag"`
	TargetNum      int               `json:"targetNum"`
}

// 响应结构体
type ExecuteResponse struct {
	ErrNo  int                `json:"errno"`  //!错误码
	ErrMsg string             `json:"errmsg"` //!提示信息
	Data   ExecuteResponeData `json:"data"`   //!数据
}

type ExecuteResponeData struct {
	CapacityTaskID   int                `json:"capacityTaskId"`
	ProductName      string             `json:"productName"`
	AppName          string             `json:"appName"`
	ScaleID          int                `json:"scaleId"`
	ScaleType        string             `json:"scaleType"`
	ScaleModel       string             `json:"scaleModel"`
	NoahTag          map[string]string  `json:"noahTag"`
	TargetNum        int                `json:"targetNum"`
	ActualOperateNum int                `json:"actualOperateNum"`
	ActualOperateIPs []string           `json:"actualOperateIPList"`
	Result           ocommon.ResultInfo `json:"result"`
}

func Init() {
	BaseURL = beego.AppConfig.String("capacity_server")
	Port = beego.AppConfig.String("capacity_port")
	URL = beego.AppConfig.String("capacity_url")
	if BaseURL == "" || Port == "" || URL == "" {
		fmt.Printf("capacity server config error, capacity_server:%s, capacity_port:%s, capacity_url:%s\n", BaseURL, Port, URL)
		os.Exit(1)
	}
	ExecuteURL = BaseURL + ":" + Port + URL
}

func (c *ExecuteRequest) Request() (response ExecuteResponse, err error) {
	res, httpCode, err := httpx.PostJSONWithHeader(ExecuteURL, time.Second*5, *c, nil, 1)
	if err != nil {
		return
	}

	if httpCode != 200 {
		err = fmt.Errorf("capacity server response error, httpCode:%d, response:%s", httpCode, res)
		return
	}

	err = json.Unmarshal([]byte(res), &response)
	return
}
