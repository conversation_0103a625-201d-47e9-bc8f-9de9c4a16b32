package service_capacity

import "dxm/siod-cloud/go-common-lib/ocommon"

type ScaleCallbackResponse struct {
	ProductName  string         `json:"productName"`
	AppName      string         `json:"appName"`
	ScaleID      int            `json:"scaleId"`
	FlowID       int            `json:"flowId"`
	ScaleType    string         `json:"scaleType"`
	ScaleNum     int            `json:"scaleNum"`
	InstanceList []InstanceInfo `json:"InstanceList"`
}

type InstanceInfo struct {
	InstanceID  int                `json:"instanceId"`
	IP          string             `json:"ip"`
	MachineName string             `json:"machineName"`
	NoahInsName string             `json:"noahInsName"`
	Stage       string             `json:"stage"`
	Status      string             `json:"status"`
	Result      ocommon.ResultInfo `json:"result"`
}
