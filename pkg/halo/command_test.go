package halo

import (
	Init "dxm/siod-cloud/go-common-lib/init"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/global"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestCreateJob(t *testing.T) {

	Convey("Test create halo job", t, func() {
		resp, r := CreateJob(JobCreateParam{
			User:        "work",
			JobType:     "COMMAND",
			CmdName:     "echo 'haha'>>/home/<USER>/a.test",
			CmdParam:    "",
			TargetType:  "HOST",
			ServiceName: "",
			TargetList:  []string{"instance-vpips0x9-07.bcc-bdbl"},
			TaskTimeout: 100,
			PlcyType:    "NUM",
			FailedOp:    "CANCEL",
			Threshold:   1,
			Concurrency: 1,
			PausePoints: []int{},
		})
		<PERSON>vey("Test create job success", func() {
			So(r.<PERSON>rr<PERSON>o, ShouldEqual, oerrno.OK)
			So(resp.ErrCode, ShouldEqual, STAGE_HALO_SUCCESS)
		})
	})

}

func TestGetJobDetail(t *testing.T) {
	Convey("Test get halo job", t, func() {
		resp, r := GetJobDetail("HOST:ef8979b6-644a-41a5-a861-d505d3b94a49")
		Convey("Test get job success", t, func() {
			So(r.ErrNo, ShouldEqual, oerrno.OK)
			So(resp.ErrCode, ShouldEqual, STAGE_HALO_SUCCESS)
		})
	})
}

func TestOperateJob(t *testing.T) {
	Convey("Test operate halo job", t, func() {
		resp, r := OperateJob("HOST:afff8a09-1368-4f7c-9be4-bc20a74f3d9c", "REDO")
		Convey("Test operate job success", t, func() {
			So(r.ErrNo, ShouldEqual, oerrno.OK)
			So(resp.ErrCode, ShouldEqual, STAGE_HALO_SUCCESS)
		})
	})
}

func TestGetTaskStdOut(t *testing.T) {
	Convey("Test get task stdout", t, func() {
		resp, r := GetTaskStdOut("HOST:2c83b5e1-08eb-409b-9558-a82c332d684f:instance-u4r3xsna.bcc-bjdd:1")
		Convey("Test get std out success", t, func() {
			So(r.ErrNo, ShouldEqual, oerrno.OK)
			So(resp.ErrCode, ShouldEqual, STAGE_HALO_SUCCESS)
		})
	})
}

var bolInitTest = false

// ！ 单纯环境初始化
func InitTest() {
	if bolInitTest == true {
		return
	}
	bolInitTest = true

	//!初始化错误码部分
	oerrno.Init()
	ocommon.Init(oerrno.ErrnoMap)

	//!单测环境，加载配置文件
	conf := []string{"conf/db.conf", "conf/app_dev.conf"} //!must be load conf/config_center_service.conf first
	Init.InitAppConfigForUnitTest(conf)
	Init.InitL4g()

	dao.InitDB()
	Init.InitDB()

	global.InitEnv()

}

func TestMain(m *testing.M) {
	Init.LoadTestInitEnv(bolInitTest, 1, "../../", InitTest)
	m.Run()
}
