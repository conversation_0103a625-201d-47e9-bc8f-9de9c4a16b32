package halo

import (
	"bytes"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/ointeraction"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/global"
	"encoding/json"
	"net/http"
)

const (
	STAGE_HALO_SUCCESS = "5000000"
	REQUEST_HALO_ERROR = "-1"
)

type JobCreateParam struct {
	User            string
	JobType         string
	CmdName         string
	CmdParam        string
	TargetType      string
	ServiceName     string
	TargetList      []string
	TaskTimeout     int
	PlcyType        string
	ConcurrencyType string
	FailedOp        string
	Threshold       int
	Concurrency     int
	PausePoints     []int
}

type JobCreateResponse struct {
	JobResponse
	Data JobCreateRespData
}

type JobCreateRespData struct {
	JobID  string
	TaskID []string
}

type JobGetRespData struct {
	Creator       string
	Cred          string
	JobID         string
	User          string
	JobType       string
	CmdName       string
	CmdParam      string
	TargetType    string
	JobTargetList []string
	FailThreshold int
	FailedOp      string
	Concurrency   int
	TaskTimeOut   int
	State         string
	StartTS       int
	EndTs         int
	TOTS          int
	Tasks         []TaskDetail
	PauseReason   string
}

type TaskDetail struct {
	Creator        string
	User           string
	Cred           string
	TaskType       string
	CmdName        string
	CmdParam       string
	TaskTargetType string
	TaskBasicData
}

type TaskBasicData struct {
	JobID       string
	TaskID      string
	TaskTarget  string
	TaskTimeOut int
	State       string
	StartTS     int
	EndTs       int
	TOTS        int
	ExitCode    int
}

type JobGetResponse struct {
	JobResponse
	Data JobGetRespData
}

type TaskStdLogResponse struct {
	JobResponse
	Data string
}

type JobResponse struct {
	ErrCode string `json:"errCode"`
	ErrMsg  string `json:"errMsg"`
}

func CreateJob(params JobCreateParam) (jobCreateResp JobCreateResponse, r ocommon.ResultInfo) {
	body, err := json.Marshal(params)
	if err != nil {
		olog.Error("Create halo job failed, serilize create halo create params:[%+v] failed, err msg:%+v", params, err.Error())
		r = ocommon.GenResultInfo(oerrno.ERR_JSON_MARSHAL, "halo-params序列化失败", nil, err)
		return
	}
	resp, r := ointeraction.SendHttpPostBodyRequest(global.HaloMasterUrl+"api/command", string(body), 3)
	if !r.IsOk() {
		olog.Error("Create halo job failed, send http post body request failed, resp:%+v, err msg:%+v", resp, r)
		return
	}

	if r.Data != nil || r.Data != "" {
		err = json.Unmarshal([]byte(resp), &jobCreateResp)
		if err != nil {
			olog.Error("Create halo job failed, unmarshal halo request resp failed, resp:%+v, err msg:%+v", resp, err.Error())
			r = ocommon.GenResultInfo(oerrno.ERR_JSON_UNMARSHAL, "halo-response反序列化失败", nil, err)
			return
		}
	}
	return
}

func GetJobDetail(jobId string) (jobGetResp JobGetResponse, r ocommon.ResultInfo) {
	resp, r := ointeraction.SendHttpGetRequest(global.HaloMasterUrl+"api/command/"+jobId, nil, 3)
	if !r.IsOk() {
		olog.Error("Get halo job detail failed, send http get request failed, resp:%+v, err msg:%+v", resp, r)
		return
	}

	if r.Data != nil || r.Data != "" {
		err := json.Unmarshal([]byte(resp), &jobGetResp)
		if err != nil {
			olog.Error("Get halo job detail failed, unmarshal halo request resp failed, resp:%+v, err msg:%+v", resp, err.Error())
			r = ocommon.GenResultInfo(oerrno.ERR_JSON_UNMARSHAL, "halo-response反序列化失败", nil, nil)
			return
		}
	}
	return
}

func OperateJob(jobId, action string) (jobOperateResp JobResponse, r ocommon.ResultInfo) {
	body, err := json.Marshal(map[string]string{
		"jobID": jobId,
		"OP":    action,
	})
	if err != nil {
		r = ocommon.GenResultInfo(oerrno.ERR_JSON_MARSHAL, "", nil, err)
		return
	}
	req, err := http.NewRequest("PUT", global.HaloMasterUrl+"api/command", bytes.NewBuffer(body))
	if err != nil {
		olog.Error("Operate halo job detail failed, new http put request failed, req:%+v, err msg:%+v", req, r)
		r = ocommon.GenResultInfo(oerrno.ERR_SYSTEM_ERR, err.Error(), nil, nil)
		return
	}

	req.Header.Set("Content-type", "application/json")
	req.Header.Set("Connection", "close")
	resp, r := ointeraction.SendHttpRequest(req, 3)
	if !r.IsOk() {
		olog.Error("Operate halo job detail failed, send http put request failed, resp:%+v, err msg:%+v", resp, r)
		return
	}
	if r.Data != nil || r.Data != "" {
		err := json.Unmarshal([]byte(resp), &jobOperateResp)
		if err != nil {
			olog.Error("Operate halo job detail failed, unmarshal halo request resp failed, resp:%+v, err msg:%+v", resp, r)
			r = ocommon.GenResultInfo(oerrno.ERR_JSON_UNMARSHAL, "halo-response反序列化失败", nil, nil)
			return
		}
	}
	return
}

func GetTaskStdOut(taskId string) (taskLogResp TaskStdLogResponse, r ocommon.ResultInfo) {
	resp, r := ointeraction.SendHttpGetRequest(global.HaloMasterUrl+"api/command/task/stdout/"+taskId, nil, 3)
	if !r.IsOk() {
		olog.Error("Get halo task stdout failed, send http get request failed, resp:%+v, err msg:%+v", resp, r)
		return
	}

	if r.Data != nil || r.Data != "" {
		err := json.Unmarshal([]byte(resp), &taskLogResp)
		if err != nil {
			olog.Error("Get halo task stderr failed, unmarshal halo request resp failed, resp:%+v, err msg:%+v", resp, err.Error())
			r = ocommon.GenResultInfo(oerrno.ERR_JSON_UNMARSHAL, "halo-response反序列化失败", nil, nil)
			return
		}
	}

	return
}

func GetTaskStdErr(taskId string) (taskLogResp TaskStdLogResponse, r ocommon.ResultInfo) {
	resp, r := ointeraction.SendHttpGetRequest(global.HaloMasterUrl+"api/command/task/stderr/"+taskId, nil, 3)
	if !r.IsOk() {
		olog.Error("Get halo task stderr failed, send http get request failed, resp:%+v, err msg:%+v", resp, r)
		return
	}

	if r.Data != nil || r.Data != "" {
		err := json.Unmarshal([]byte(resp), &taskLogResp)
		if err != nil {
			olog.Error("Get halo task stderr failed, unmarshal halo request resp failed, resp:%+v, err msg:%+v", resp, err.Error())
			r = ocommon.GenResultInfo(oerrno.ERR_JSON_UNMARSHAL, "halo-response反序列化失败", nil, nil)
			return
		}
	}

	return
}
