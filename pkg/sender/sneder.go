package sender

import (
	"crypto/sha1"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/othirdservice/new-noah/authority"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"
	"time"
)

const (
	Email    = "email"
	Sms      = "sms"
	Phone    = "phone"
	Inflow   = "inflow"
	XmsGroup = "xms_group"
)

type (
	SenderFunc func(ctx MsgCtx) error
	MsgCtx     struct {
		Receiver []string
		Dst      []string
		Channel  string
		Content  string
		AtUsers  []string
	}
)

var NotifyTable = map[string]SenderFunc{
	Email:    EmailSend,
	Sms:      SmsSend,
	Phone:    PhoneSend,
	Inflow:   InflowSend,
	XmsGroup: InflowSend,
}

func buildUser(dst []string) []string {
	var ret = make([]string, 0, len(dst))
	for i := 0; i < len(dst); i++ {
		if !strings.HasPrefix(dst[i], "z_") {
			ret = append(ret, dst[i])
			continue
		}

		users, err := GetCurrentShiftByZhibanId(dst[i])
		if err != nil {
			olog.Error("GetCurrentShiftByZhibanId error %v", err)
			continue
		}
		ret = append(ret, users...)
	}
	return ret
}

func phoneSearch(uid string) (string, error) {

	userInfo, result := authority.GetUserInfoByUserName(uid)
	if result.RawErr != nil {
		return "", result.RawErr
	}

	for _, contact := range userInfo.ContactInfoList {
		if contact.ContactType.Name == "phone" {
			return contact.Value, nil
		}
	}

	return "", errors.New("no exist")
}

func createPostForm(content, dest string, contentType int) map[string]interface{} {
	// 生成时间戳
	dt := fmt.Sprintf("%d", time.Now().Unix())
	postForm := make(map[string]interface{})

	postForm["sms_type"] = SMS_TYPE_INFORM
	postForm["dest_type"] = DEST_TYPE_PHONE
	postForm["dest"] = dest
	postForm["content"] = content
	postForm["content_type"] = contentType

	sortStr := sortValueByKey(postForm)
	singStr := signStrGet(sortStr, AppId, dt, SecretKey)
	sign := sha1SignGet(singStr)

	postForm["_sign"] = sign
	postForm["_timestamp"] = dt
	postForm["_appid"] = AppId
	return postForm
}

func sha1SignGet(signStr string) string {
	h := sha1.New()
	io.WriteString(h, signStr)
	return fmt.Sprintf("%x", h.Sum(nil))
}

// 按照字典的key排序, 例如: "k1=v1&k2=v2&k3=v3"
func sortValueByKey(params map[string]interface{}) (sortedValue string) {
	var keys []string
	for k := range params {
		if !strings.HasPrefix(k, "_") {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)
	for _, k := range keys {
		switch params[k].(type) {
		case string:
			sortedValue += fmt.Sprintf("%s=%v&", k, params[k])
		case interface{}: // Content
			bs, _ := json.Marshal(params[k])
			sortedValue += fmt.Sprintf("%s=%v&", k, string(bs))
		}
	}
	sortedValue = strings.TrimRight(sortedValue, "&")
	return
}

// 拼接出需要做sha1的字符串
func signStrGet(sortStr string, appid, t, secretKey string) string {
	return sortStr + fmt.Sprintf(".%s.%s.%s", appid, t, secretKey)
}
