package sender

import (
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/pkg/convertx"
	"errors"
	"strings"
	"time"

	"github.com/parnurzeal/gorequest"

	"github.com/astaxie/beego"
)

func SmsSend(ctx MsgCtx) error {
	users := buildUser(ctx.Receiver)
	if len(users) == 0 {
		return errors.New("user empty")
	}

	dst := extract(users)
	content := contentGen("noahee_sms_alert", "sms_content1", ctx.Content)
	body := createPostForm(content, strings.Join(dst, ","), CONTENT_TYPE_TEMPLATE)

	addr := beego.AppConfig.String("sms_addr")
	return doSendForm(addr, body, Sms)
}

type TemplateSend struct {
	TplID  string            `json:"tpl_id"`
	Params map[string]string `json:"params"`
}

const (
	// content type
	CONTENT_TYPE_TEXT     = 1 // text msg
	CONTENT_TYPE_TEMPLATE = 2 // template msg
	// sms type
	SMS_TYPE_MARKETING = 1 // marketing msg
	SMS_TYPE_INFORM    = 2 // inform msg
	// dest type
	DEST_TYPE_BID   = 1 // PassportID
	DEST_TYPE_PHONE = 2 // phone number
)

const (
	AppId     = "fsg_noah_sia"
	SecretKey = "W1fQJ4AKG55Qa43"
)

func doSendForm(url string, body interface{}, channel string) error {

	cloud := beego.AppConfig.String("cloud")
	client := gorequest.New().Post(url).Set("Content-Type", "application/x-www-form-urlencoded").Timeout(5 * time.Second).Send(body)

	if cloud == "kj" {
		client.Set("Host", "inner.sms.duxiaoman-int.com")
	} else {
		client.Set("Host", "cloud.sms.duxiaoman-int.com")
	}

	curl, _ := client.AsCurlCommand()
	_, resp, errs := client.EndBytes()
	if len(errs) != 0 {
		olog.Info("%s send url : %v , body : %v res:%v", channel, url, convertx.ToString(body), string(resp))
		return errs[0]
	}

	olog.Info("send curl: %v  res:%v", curl, string(resp))

	return nil
}
