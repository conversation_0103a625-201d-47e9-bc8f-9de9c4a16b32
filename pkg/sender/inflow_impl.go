package sender

import (
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/httpx"
	"fmt"
	"strconv"
	"time"
)

// 普通hi机器人
type HiMessageDetail struct {
	Header   MessageHeader `json:"header" description:"消息头信息"`
	BodyList []MessageBody `json:"body" description:"消息内容"`
}

type MessageHeader struct {
	Toid []int  `json:"toid" description:"发送的Hi群ID列表"`
	Type string `json:"type,omitempty"`
}

type MessageBody struct {
	Type      string   `json:"type" description:"消息类型"`
	Content   string   `json:"content" description:"消息内容"`
	Href      string   `json:"href" description:"链接"`
	AtUserIds []string `json:"atuserids"`
}

type HiSendResp struct {
	Errcode int              `json:"errcode" description:"错误码"`
	Errmsg  string           `json:"errmsg" description:"错误码"`
	Data    HiSenderRespData `json:"data" description:"错误数据"`
}

type HiSenderRespData struct {
	Fail map[string]interface{} `json:"fail" description:"失败Hi群，key为群号，value为错误码"`
}

func InflowSend(ctx MsgCtx) error {

	var atUsers []string
	if len(ctx.AtUsers) != 0 {
		atUsers = buildUser(ctx.AtUsers)
	}

	groupIds := make([]int, 0, len(ctx.Receiver))
	for _, s := range ctx.Receiver {
		sInt, err := strconv.Atoi(s)
		if err != nil {
			continue
		}
		groupIds = append(groupIds, sInt)
	}
	return XmsPush(ctx.Content, atUsers, groupIds...)
}

// XmsPush 企业机器人推送
func XmsPush(msg string, atUsers []string, gid ...int) error {

	var msgType string
	if len(atUsers) == 0 {
		msgType = "MD"
	} else {
		msgType = "TEXT"
	}

	header := MessageHeader{Toid: gid, Type: msgType}
	messageBody := MessageBody{
		Type:    msgType,
		Content: msg,
	}

	var bodies = []MessageBody{messageBody}
	if len(atUsers) != 0 {
		bodies = append(bodies, MessageBody{
			Type:      "AT",
			AtUserIds: atUsers,
		})
	}

	var (
		url   = fmt.Sprintf("http://%s/robot/api/v1/message/sendMessage", apptree.GetServicePort(global.MessageServerBns))
		token = global.MessageServerToken
	)

	detail := HiMessageDetail{BodyList: bodies, Header: header}
	res, _, err := httpx.PostJSONWithHeader(url, time.Second*3, detail, map[string]string{"token": token})
	if err != nil {
		return err
	}
	olog.Info("xms push result: %v", string(res))

	return nil
}
