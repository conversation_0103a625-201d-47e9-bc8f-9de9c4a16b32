package sender

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/astaxie/beego"
	"github.com/parnurzeal/gorequest"
)

func PhoneSend(ctx MsgCtx) error {

	users := buildUser(ctx.Receiver)
	if len(users) == 0 {
		return errors.New("user empty")
	}

	var content = ctx.Content
	if len(ctx.Content) > 199 {
		content = content[:199]
	}

	dst := extract(users)
	content = contentGen("noahee_phone_alert", "call_content", content)
	body := createPostForm(content, strings.Join(dst, ","), CONTENT_TYPE_TEMPLATE)
	addr := beego.AppConfig.String("sms_addr")
	return doSendForm(addr, body, Phone)
}

func contentGen(tpl, paramType, params string) string {
	template := TemplateSend{
		TplID: tpl,
		Params: map[string]string{
			paramType: params,
		},
	}
	bs, _ := json.Marshal(template)
	return string(bs)
}

func extract(users []string) []string {
	ats := make([]string, 0, len(users))
	for _, user := range users {
		phone, err := phoneSearch(user)
		if err != nil {
			continue
		}
		ats = append(ats, phone)
	}
	return ats
}

var (
	AK            = "e485d9cc03844a828687715c81918657"
	SK            = "ab3f44608f2c4e4d999537696d1db06d"
	sendPhoneUri  = "/api/v3/console/realtime/status/create"
	getRobotUri   = "/api/v1/robot/list"
	baiduAiobAddr = "aicc.bce.baidu.com"
)

type BaiduAiobReq struct {
	RobotId    string                 `json:"robotId"`
	Mobile     string                 `json:"mobile"`
	CallerNum  []string               `json:"callerNum"`
	SecretType int64                  `json:"secretType"`
	DialogVar  map[string]interface{} `json:"dialogVar"`
}

type BaiduRes struct {
	ErrCode int         `json:"code"`
	ErrMsg  string      `json:"msg"`
	Data    interface{} `json:"data"`
}

func RequestPhoneWithRetry(body BaiduAiobReq, retry int) ([]byte, error) {
	if retry > 0 {
		var (
			index int
			err   error
			resp  []byte
		)
		for index < retry {
			if resp, err = RequestPhone(body); err != nil {
				index += 1
				continue
			} else {
				return resp, err
			}
		}
		return resp, err
	}

	return RequestPhone(body)
}

func RequestPhone(body BaiduAiobReq) ([]byte, error) {
	robots, err := GetRobotList()
	if err != nil {
		return nil, err
	}
	if len(robots) == 0 {
		return nil, fmt.Errorf("robot empty")
	}
	body.RobotId = robots[0].RobotId
	headers, err := getHeaders(HeaderParams{
		Method: "POST",
		Host:   baiduAiobAddr,
		Uri:    sendPhoneUri,
		Ak:     AK,
		Sk:     SK,
	})
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://%s%s", baiduAiobAddr, sendPhoneUri)
	request := gorequest.New().Post(url).Send(body)
	for k, v := range headers {
		request.Header.Add(k, v)
	}
	_, resp, errs := request.EndBytes()
	if len(errs) != 0 {
		return nil, errs[0]
	}
	return resp, err
}

type RobotData struct {
	Pn    int           `json:"pn"`
	Ps    int           `json:"ps"`
	Total int           `json:"total"`
	List  []RobotDetail `json:"list"`
}
type RobotDetail struct {
	RobotId       string `json:"robotId"`
	RobotName     string `json:"robotName"`
	CallType      int    `json:"callType"`
	IndustryId    string `json:"industryId"`
	CreateUserId  int64  `json:"createUserId"`
	PublishUserId int64  `json:"publishUserId"`
	RobotType     int    `json:"robotType"`
	RobotDescribe string `json:"robotDescribe"`
}

func GetRobotList() ([]RobotDetail, error) {
	headers, err := getHeaders(HeaderParams{
		Method: "GET",
		Host:   baiduAiobAddr,
		Uri:    getRobotUri,
		Ak:     AK,
		Sk:     SK,
	})
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://%s%s", baiduAiobAddr, getRobotUri)
	request := gorequest.New().Get(url)
	for k, v := range headers {
		request.Header.Add(k, v)
	}
	_, resp, errs := request.EndBytes()
	if len(errs) != 0 {
		return nil, errs[0]
	}
	var res BaiduRes
	err = json.Unmarshal(resp, &res)
	if err != nil {
		return nil, err
	}
	if res.ErrCode != http.StatusOK {
		return nil, fmt.Errorf(res.ErrMsg)
	}
	data, err := json.Marshal(res.Data)
	if err != nil {
		return nil, err
	}
	var robotData RobotData
	if err := json.Unmarshal(data, &robotData); err != nil {
		return nil, err
	}

	return robotData.List, nil
}

func createHMACSHA256Signature(secretKey string, authStringPrefix string) string {
	key := []byte(secretKey)
	data := []byte(authStringPrefix)

	h := hmac.New(sha256.New, key)
	h.Write(data)

	hash := h.Sum(nil)
	signingKey := hex.EncodeToString(hash)

	return signingKey
}

type HeaderParams struct {
	Method string
	Host   string
	Uri    string
	Query  string
	Ak     string
	Sk     string
}

func getHeaders(params HeaderParams) (headers map[string]string, err error) {
	if strings.Contains(params.Query, "&") {
		err = fmt.Errorf("unsupport scrape!") //暂未处理更复杂场景的query
		return
	}
	canonicalHeaders := fmt.Sprintf("host:%s", strings.ToLower(params.Host))
	canonicalRequest := params.Method + "\n" + params.Uri + "\n" + params.Query + "\n" + canonicalHeaders
	signedHeaders := "Host"
	xBceDate := time.Now().UTC().Format("2006-01-02T15:04:05Z")
	authPrefix := "cc-api-auth-v1/" + AK + "/" + xBceDate + "/1800"
	signingKey := createHMACSHA256Signature(params.Sk, authPrefix)
	signature := createHMACSHA256Signature(signingKey, canonicalRequest)
	headers = map[string]string{
		"Host":          params.Host,
		"Authorization": fmt.Sprintf("%s/%s/%s", authPrefix, signedHeaders, signature),
	}
	return
}
