package sender

import (
	"dxm/siod_sre/auto-scaler/pkg/httpx"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/astaxie/beego"
)

var (
	path = "/zhiban/api/v2/r/zhibanInfo/getCurrentShiftByZhibanId"
)

type ZhibanResp struct {
	ErrCode            string `json:"errCode"`
	ErrMessage         string `json:"errMessage"`
	CurrentZhibanShift Shift  `json:"currentZhibanShift"`
}

type Shift struct {
	Number int64  `json:"number"`
	Users  []User `json:"users"`
}

type User struct {
	UserId      string `json:"userId"`
	DisplayName string `json:"displayName"`
	ShiftRole   string `json:"shiftRole"`
}

func GetCurrentShiftByZhibanId(zhibanId string) ([]string, error) {

	var addr = beego.AppConfig.String("oncall_addr")

	if addr == "" {
		return nil, fmt.Errorf("error zhiban server or token")
	}

	if !(strings.HasPrefix(addr, "http://") || strings.HasPrefix(addr, "https://")) {
		addr = "http://" + addr
	}

	body, err := httpx.GetWithHeader(addr+path, map[string]string{"zhibanId": zhibanId}, map[string]string{
		"Authorization": "zhibanAdmin",
	})
	if err != nil {
		return nil, err
	}

	var resp ZhibanResp
	if err = json.Unmarshal(body, &resp); err != nil {
		return nil, err
	}
	if resp.ErrCode != "0" {
		return nil, fmt.Errorf(resp.ErrMessage)
	}

	var users = make([]string, 0)
	for _, u := range resp.CurrentZhibanShift.Users {
		users = append(users, u.UserId)
	}

	return users, nil
}
