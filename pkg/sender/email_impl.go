package sender

import (
	"errors"
	"fmt"
	"strings"

	"github.com/astaxie/beego"

	"github.com/jordan-wright/email"
)

type MailSender struct {
	Sender   string
	Receiver []string
	Subject  string
	TimeDate string
	Mes      []byte
}

func EmailSend(ctx MsgCtx) error {

	user := buildUser(ctx.Receiver)
	if len(user) == 0 {
		return errors.New("user empty")
	}

	dst := make([]string, 0, len(user))
	for i := 0; i < len(user); i++ {
		if strings.HasSuffix(user[i], "_dxm") {
			user[i] = strings.TrimSuffix(user[i], "_dxm")
		}

		dst = append(dst, fmt.Sprintf("%<EMAIL>", user[i]))
	}

	e := email.NewEmail()
	e.From = beego.AppConfig.String("email_sender")
	e.To = dst
	e.Subject = beego.AppConfig.String("email_subject")
	e.HTML = []byte(ctx.Content)

	err := e.Send(beego.AppConfig.String("email_addr"), nil)
	if err != nil {
		return err
	}
	return nil
}
