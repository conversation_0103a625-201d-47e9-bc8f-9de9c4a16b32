package compute

import (
	"math"
	"sort"
)

// 计算平均值、标准差、最大值、最小值
func Describe(arr []float64) (max, min, mean, std float64) {
	if len(arr) == 0 {
		return
	}
	max, min = GetMaxMinValue(arr)
	mean = ComputeMean(arr)
	std = ComputeStd(arr, mean)
	return
}

// 获取最大、最小值
func GetMaxMinValue(arr []float64) (maxValue, minValue float64) {
	if len(arr) == 0 {
		return
	}
	if len(arr) == 1 {
		return arr[0], arr[0]
	}
	sort.Float64s(arr)
	return arr[len(arr)-1], arr[0]
}

// 计算平均值
func ComputeMean(arr []float64) (avg float64) {
	if len(arr) == 0 {
		return
	}
	var sum float64
	for _, v := range arr {
		sum += v
	}
	avg = sum / float64(len(arr))
	return
}

// 计算标准差
func ComputeStd(arr []float64, mean float64) (std float64) {
	if len(arr) == 0 {
		return
	}

	// 计算方差 ∑(x - μ)²
	var sumDiffSquares float64
	for _, num := range arr {
		sumDiffSquares += (num - mean) * (num - mean)
	}

	// 计算标准差
	return math.Sqrt(sumDiffSquares / float64(len(arr)))
}

// 获取数组中的最小值
func GetMinValue(arr []float64) float64 {
	if len(arr) == 0 {
		return 0
	}
	min := arr[0]
	for _, v := range arr[1:] {
		if v < min {
			min = v
		}
	}
	return min
}

// 计算给定浮点数数组的 q 分位数
// q 应该在 0 到 1 之间（包括 0 和 1）
// 如果数组为空或 nil，或 q 超出范围，返回 NaN
func Quantile(arr []float64, q float64) float64 {
	// 检查输入有效性
	if arr == nil || len(arr) == 0 || q < 0 || q > 1 {
		return 0.0
	}

	// 如果数组只有一个元素，直接返回该元素
	if len(arr) == 1 {
		return arr[0]
	}

	// 创建数组的副本并排序，避免修改原数组
	sorted := make([]float64, len(arr))
	copy(sorted, arr)
	sort.Float64s(sorted)

	// 计算分位数对应的位置
	pos := q * float64(len(sorted)-1)
	index := int(pos)
	frac := pos - float64(index)

	// 处理边界情况
	if index+1 >= len(sorted) {
		return sorted[len(sorted)-1]
	}

	// 线性插值计算分位数
	return sorted[index] + frac*(sorted[index+1]-sorted[index])
}

// 查询数组中最大值的索引
func IdxMax(slice []float64) int {
	if len(slice) == 0 {
		return -1 // 返回-1表示空切片
	}
	maxIndex := 0
	maxValue := slice[0]
	for i, value := range slice[1:] {
		if value > maxValue {
			maxValue = value
			maxIndex = i + 1
		}
	}
	return maxIndex
}
