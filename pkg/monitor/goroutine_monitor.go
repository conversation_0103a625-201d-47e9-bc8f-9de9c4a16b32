package monitor

import (
	"runtime"
	"time"

	"dxm/siod-cloud/go-common-lib/olog"
)

// GoroutineMonitor goroutine监控器
type GoroutineMonitor struct {
	maxGoroutines int
	checkInterval time.Duration
	stopCh        chan struct{}
}

// NewGoroutineMonitor 创建新的goroutine监控器
func NewGoroutineMonitor(maxGoroutines int, checkInterval time.Duration) *GoroutineMonitor {
	return &GoroutineMonitor{
		maxGoroutines: maxGoroutines,
		checkInterval: checkInterval,
		stopCh:        make(chan struct{}),
	}
}

// Start 启动监控
func (g *GoroutineMonitor) Start() {
	go func() {
		ticker := time.NewTicker(g.checkInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				g.checkGoroutines()
			case <-g.stopCh:
				return
			}
		}
	}()
}

// Stop 停止监控
func (g *GoroutineMonitor) Stop() {
	close(g.stopCh)
}

// checkGoroutines 检查goroutine数量
func (g *GoroutineMonitor) checkGoroutines() {
	numGoroutines := runtime.NumGoroutine()
	
	if numGoroutines > g.maxGoroutines {
		olog.Warn("High number of goroutines detected: %d (max: %d)", numGoroutines, g.maxGoroutines)
		
		// 打印goroutine堆栈信息用于调试
		buf := make([]byte, 1024*1024)
		stackSize := runtime.Stack(buf, true)
		olog.Debug("Goroutine stack trace:\n%s", string(buf[:stackSize]))
	} else {
		olog.Debug("Current goroutines: %d", numGoroutines)
	}
}

// GetGoroutineCount 获取当前goroutine数量
func GetGoroutineCount() int {
	return runtime.NumGoroutine()
}
