package data_process

import (
	"fmt"
	"math"
	"sort"
)

func Obtain95thPercent(data []float64) (percentileValue float64, err error) {

	// 1、排序
	sort.Float64s(data)

	// 2、计算95%分位数的位置
	n := len(data)
	percentile := 0.95
	index := percentile * float64(n-1)

	// 3. 计算95%分位数的值
	if index == float64(int(index)) {
		// 如果索引是整数，直接取该位置的值
		percentileValue = data[int(index)]
	} else {
		// 如果索引不是整数，进行线性插值
		lower := int(index)
		upper := lower + 1
		weight := index - float64(lower)
		percentileValue = data[lower]*(1-weight) + data[upper]*weight
	}

	return
}

func Sum(numbers interface{}) (interface{}, error) {
	switch v := numbers.(type) {
	case []int:
		total := 0
		for _, num := range v {
			total += num
		}
		return total, nil
	case []float64:
		total := 0.0
		for _, num := range v {
			total += num
		}
		return total, nil
	default:
		return nil, fmt.Errorf("unsupported type: %T", numbers)
	}
}

func Within10Percent(a, b int) bool {
	fa, fb := float64(a), float64(b)
	if fa == 0 && fb == 0 {
		return true
	}
	diff := math.Abs(fa - fb)
	avg := (math.Abs(fa) + math.Abs(fb)) / 2
	percentageDiff := (diff / avg) * 100
	return percentageDiff <= 10
}
