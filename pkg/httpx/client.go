package httpx

import (
	"context"
	"dxm/siod_sre/auto-scaler/pkg/convertx"

	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"github.com/parnurzeal/gorequest"
)

func Request(ctx context.Context, method, path string, timeout time.Duration, params interface{}, header map[string]string) (body []byte, err error) {

	switch strings.ToUpper(method) {
	case "POST":
		body, err = PostJSON(ctx, path, timeout, params, header)
	case "POST_JSON":
		body, err = PostJSON(ctx, path, timeout, params, header)
	case "GET":
		var val = make(map[string]string)
		switch t := reflect.ValueOf(params); t.Kind() {
		case reflect.Map:
			p, ok := params.(map[string]interface{})
			if !ok {
				return nil, fmt.Errorf("")
			}
			for s, i := range p {
				val[s] = convertx.ToString(i)
			}
		case reflect.Struct:
		}
		body, err = Get(ctx, path, timeout, val, header)
	default:
		err = fmt.Errorf("no support the method ,mehtod: %s", method)
	}

	return
}

// Get 封装GET方法
func Get(ctx context.Context, path string, timeout time.Duration, params map[string]string, header map[string]string) ([]byte, error) {
	request := getRequest(ctx, path, timeout, "GET")
	setHeader(ctx, request, header)
	for key, value := range params {
		request = request.Param(key, value)
	}
	_, body, errs := request.Retry(3, 5*time.Second, http.StatusBadRequest, http.StatusInternalServerError).EndBytes()
	if errs != nil {
		return nil, errs[0]
	}

	return body, nil
}

// Post 封装POST方法
func Post(ctx context.Context, path string, timeout time.Duration, params map[string]string, header map[string]string) ([]byte, error) {
	request := getRequest(ctx, path, timeout, "POST")
	setHeader(ctx, request, header)

	for key, value := range params {
		request = request.Param(key, value)
	}

	_, body, errs := request.End()
	if errs != nil {
		return nil, errs[0]
	}

	return []byte(body), nil
}

// Post Form
func PostForm(ctx context.Context, path string, timeout time.Duration, data interface{}, header map[string]string) ([]byte, error) {
	request := getRequest(ctx, path, timeout, "POST")
	setHeader(ctx, request, header)
	_, body, errs := request.Type("multipart").Send(data).End()
	if errs != nil {
		return nil, errs[0]
	}

	return []byte(body), nil
}

// PostJSON 封装POSTJSON方法
func PostJSON(ctx context.Context, path string, timeout time.Duration, data interface{}, header map[string]string) ([]byte, error) {
	request := getRequest(ctx, path, timeout, "POST")
	setHeader(ctx, request, header)

	request = request.Set("Content-Type", "application/json").Send(data).
		Retry(3, 5*time.Second, http.StatusBadRequest, http.StatusInternalServerError)
	curl, _ := request.AsCurlCommand()
	fmt.Println(curl)
	_, body, errs := request.End()

	if errs != nil {
		return nil, errs[0]
	}

	return []byte(body), nil
}

func getRequest(ctx context.Context, path string, timeout time.Duration, method string) *gorequest.SuperAgent {
	var request *gorequest.SuperAgent
	var url string
	if strings.HasPrefix(path, "http") {
		url = path
	} else {
		url = "http://" + path
	}

	switch method {
	case "GET":
		request = gorequest.New().Get(url)
	case "POST":
		request = gorequest.New().Post(url)
	}

	getHeader(ctx, request)
	request.Timeout(timeout)
	return request
}

// 可以在ctx中设置通用header
func getHeader(ctx context.Context, request *gorequest.SuperAgent) {

}

func setHeader(ctx context.Context, request *gorequest.SuperAgent, headers map[string]string) {

	var (
		traceHeader map[string]string
	)

	traceId := ctx.Value("")
	if traceId != nil {
		traceHeader = map[string]string{
			"header-rid": traceId.(string),
		}
	}

	for key, value := range traceHeader {
		request.Header[key] = []string{value}
	}

	for key, value := range headers {
		request.Header[key] = []string{value}
	}
}
