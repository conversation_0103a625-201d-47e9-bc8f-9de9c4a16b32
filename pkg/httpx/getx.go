package httpx

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

func GetWithHeader(url string, query map[string]string, header map[string]string) ([]byte, error) {
	if header == nil {
		header = make(map[string]string, 4)
	}
	if _, ok := header["Authorization"]; !ok {
		header["Authorization"] = "Bearer 9nvjs5bbbo1x8sidzz5yb2bn0z216hqk" // admin危险
	}

	res, _, err := getWithHeader(url, time.Second*5, query, header, 3)
	if err != nil {
		return nil, err
	}
	return res, err
}

func getWithHeader(requestUrl string, timeout time.Duration, query map[string]string, headers map[string]string, retries ...int) (response []byte, code int, err error) {
	client := http.Client{
		Timeout: timeout,
	}

	req, err := http.NewRequest("GET", requestUrl, nil)
	if err != nil {
		return
	}

	q := req.URL.Query()
	for key, value := range query {
		q.Set(key, value)
	}
	req.URL.RawQuery = q.Encode()

	fmt.Println(req.URL.RawQuery)
	for key, value := range headers {
		req.Header.Add(key, value)
	}

	var resp *http.Response

	if len(retries) > 0 {
		for i := 0; i < retries[0]; i++ {
			resp, err = client.Do(req)
			if err == nil {
				break
			}

			if i+1 < retries[0] {
				time.Sleep(time.Millisecond * 200)
			}
		}
	} else {
		resp, err = client.Do(req)
	}

	if err != nil {
		return
	}

	code = resp.StatusCode

	if resp.Body != nil {
		defer resp.Body.Close()
		response, err = io.ReadAll(resp.Body)
	}

	return
}

func GetFullUrlWithHeader(requestUrl string, timeout time.Duration, headers map[string]string, retries ...int) (response []byte, code int, err error) {
	client := http.Client{
		Timeout: timeout,
	}

	req, err := http.NewRequest("GET", requestUrl, nil)
	if err != nil {
		return
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	var resp *http.Response

	if len(retries) > 0 {
		for i := 0; i < retries[0]; i++ {
			resp, err = client.Do(req)
			if err == nil {
				break
			}

			if i+1 < retries[0] {
				time.Sleep(time.Millisecond * 200)
			}
		}
	} else {
		resp, err = client.Do(req)
	}

	if err != nil {
		return
	}

	code = resp.StatusCode

	if resp.Body != nil {
		defer resp.Body.Close()
		response, err = io.ReadAll(resp.Body)
	}

	return
}
