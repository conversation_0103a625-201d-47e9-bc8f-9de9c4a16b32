package httpx

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/ointeraction"
	"dxm/siod-cloud/go-common-lib/olog"
)

func SendHttpFormRequest(url, method string, requestForm map[string]interface{}) (responseBody string, r ocommon.ResultInfo) {
	headers := map[string]string{"Content-Type": "application/json"}
	req := ointeraction.HttpRequest{
		Address:          url,
		Method:           method,
		Headers:          headers,
		MaxRetryTimes:    3,
		DialTimeOut:      60,
		ReadWriteTimeOut: 60,
		Form:             requestForm,
	}

	responseBody, r = req.SendHttpRequest()
	if !r.IsOk() {
		olog.Warn("Failed to send post request, remote address=(%v), postInfo=(%+v) r=(%+v)",
			url, requestForm, r)
	} else {
		olog.Debug("Success to send post request, remote address=(%v), postInfo=(%+v) r=(%+v)",
			url, requestForm, r)
	}

	return
}

func SendHttpBodyRequest(url, method string, jsonBody []byte) (responseData string, r ocommon.ResultInfo) {
	req := ointeraction.HttpRequest{
		Address:       url,
		Method:        method,
		Content:       string(jsonBody),
		MaxRetryTimes: 3,
		DialTimeOut:   60,
	}
	responseData, r = req.SendHttpRequest()
	return
}
