package httpx

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

func PostFormWithHeader(requestUrl string, timeout time.Duration, params interface{}, headers map[string]string, retries ...int) (response []byte, code int, err error) {
	client := http.Client{
		Timeout: timeout,
	}
	formValues := url.Values{}
	for k, v := range params.(map[string]interface{}) {
		formValues.Set(k, fmt.Sprintf("%+v", v))
	}

	formDataStr := formValues.Encode()
	formDataBytes := []byte(formDataStr)
	formBytesReader := bytes.NewReader(formDataBytes)

	req, err := http.NewRequest("POST", requestUrl, formBytesReader)
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	for key, value := range headers {
		req.Header.Add(key, value)
	}

	var resp *http.Response

	if len(retries) > 0 {
		for i := 0; i < retries[0]; i++ {
			resp, err = client.Do(req)
			if err == nil {
				break
			}

			if i+1 < retries[0] {
				time.Sleep(time.Millisecond * 200)
			}
		}
	} else {
		resp, err = client.Do(req)
	}

	if err != nil {
		return
	}

	code = resp.StatusCode

	if resp.Body != nil {
		defer resp.Body.Close()
		response, err = io.ReadAll(resp.Body)
	}

	return
}

func PostWithHeader(url string, body interface{}, header map[string]string) ([]byte, error) {
	res, _, err := PostJSONWithHeader(url, time.Second*5, body, header, 3)
	if err != nil {
		return nil, err
	}
	return res, err
}

func PostJSONWithHeader(url string, timeout time.Duration, v interface{}, headers map[string]string, retries ...int) (response []byte, code int, err error) {
	var bs []byte

	bs, err = json.Marshal(v)
	if err != nil {
		return
	}

	bf := bytes.NewBuffer(bs)

	client := http.Client{
		Timeout: timeout,
	}

	req, err := http.NewRequest("POST", url, bf)
	if err != nil {
		return
	}
	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Add(key, value)
	}

	var resp *http.Response

	if len(retries) > 0 {
		for i := 0; i < retries[0]; i++ {
			resp, err = client.Do(req)
			if err == nil {
				break
			}

			if i+1 < retries[0] {
				time.Sleep(time.Millisecond * 200)
			}
		}
	} else {
		resp, err = client.Do(req)
	}

	if err != nil {
		return
	}

	code = resp.StatusCode

	if resp.Body != nil {
		defer resp.Body.Close()
		response, err = io.ReadAll(resp.Body)
	}

	return
}
