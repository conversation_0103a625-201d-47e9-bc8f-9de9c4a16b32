package convertx

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

func ToString(srcData interface{}) string {
	result, _ := ToStringInfo(srcData)
	return result
}

func ToStringInfo(srcData interface{}) (result string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = fmt.Errorf("ToStringInfo panic : %v", r)
		}
	}()

	if srcData == nil {
		return "", fmt.<PERSON><PERSON>("ToStringInfo() input is nil")
	}

	switch reflect.TypeOf(srcData).Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint8,
		reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Bool:
		return fmt.Sprintf("%v", srcData), nil
	case reflect.Float32:
		return strconv.FormatFloat(float64(srcData.(float32)), 'f', -1, 64), nil
	case reflect.Float64:
		return strconv.FormatFloat(srcData.(float64), 'f', -1, 64), nil
	case reflect.String:
		if jsonNumber, ok := srcData.(json.Number); ok {
			return ToStringInfo(extractJsonNumber(jsonNumber))
		}
		return fmt.Sprintf("%v", srcData), nil
	case reflect.Map, reflect.Slice, reflect.Struct, reflect.Ptr:
		stringBytes, _ := json.Marshal(srcData)
		return string(stringBytes), nil
	default:
		return "", fmt.Errorf("%v no support", reflect.TypeOf(srcData).Kind().String())
	}
}

func extractJsonNumber(jsonNum json.Number) interface{} {
	if result, err := jsonNum.Int64(); err == nil {
		return result
	}

	if result, err := jsonNum.Float64(); err == nil {
		return result
	}

	switch strings.ToLower(jsonNum.String()) {
	case "true":
		return true
	case "false":
		return false
	}

	return jsonNum.String()
}
