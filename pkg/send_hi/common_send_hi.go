package send_hi

import (
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/global"
	"fmt"
	"time"
)

func SendEventSuccess(context string) (err error) {
	messageBody := fmt.Sprintf("【状态成功】%s %s, 时间:[%s]", global.YunEnvMap[global.YunEnv], context, time.Now().Format(otool.TIME_FORMAT_STR))
	var sendHiObject = SendHI{
		Type:          "TEXT",
		Content:       messageBody,
		GroupCodeList: []int{global.SendHiGroup},
		RequestBns:    global.MessageServerBns,
		Token:         global.MessageServerToken,
	}
	err = sendHiObject.Send()
	if err != nil {
		olog.Error("failed to send message to server, err:[%v]", err)
	}
	return
}

func SendEventFailed(context string) (err error) {
	messageBody := fmt.Sprintf("【状态失败】%s %s, 时间:[%s]", global.YunEnvMap[global.YunEnv], context, time.Now().Format(otool.TIME_FORMAT_STR))
	var sendHiObject = SendHI{
		Type:          "TEXT",
		Content:       messageBody,
		GroupCodeList: []int{global.SendHiGroup},
		RequestBns:    global.MessageServerBns,
		Token:         global.MessageServerToken,
	}
	err = sendHiObject.Send()
	if err != nil {
		olog.Error("failed to send message to server, err:[%v]", err)
	}
	return
}
