package send_hi

import (
	"fmt"
	"math/rand"
	"time"

	"dxm/noah-sdk/noah_golang_sdk/src/libapptree"

	"github.com/toolkits/pkg/net/httplib"
)

var (
	Apptree               *libapptree.Apptree
	AppTreeLastUpdateTime time.Time
	AuthorizationToken    = "Bearer af9339d22e22b7d3637731bedd018a36"
	DefaultTimeout        = 100 * time.Millisecond
)

func InitApptree() {
	var err error
	AppTreeLastUpdateTime = time.Now()
	Apptree, err = libapptree.ApptreeNew(nil, nil)
	if err != nil {
		panic(err)
	}
}

type sendHiForm struct {
	Message hiMessage `json:"message"`
}

type hiMessage struct {
	Header messageHeader `json:"header,omitempty"`
	Body   []messageBody `json:"body"`
}

type messageHeader struct {
	ToId []int  `json:"toid"`
	Type string `json:"type"`
}

type messageBody struct {
	Type    string `json:"type"`
	Content string `json:"content"`
}

type infoflowReq struct {
	Body           []messageBody         `json:"body"`
	HeaderInfoflow messageHeaderInfoflow `json:"header"`
}

type messageHeaderInfoflow struct {
	ToId []int  `json:"toId"`
	Type string `json:"type"`
}

type hiRes struct {
	ErrCode int      `json:"errorcode"`
	ErrMsg  string   `json:"errmsg"`
	Data    failData `json:"data"`
}
type failData struct {
	Fail map[string]interface{} `json:"fail"`
}

type SendHI struct {
	// 文字格式类型, TEXT、MD等
	Type string

	// 内容
	Content string

	// 发送群号列表
	GroupCodeList []int

	// 请求服务端
	RequestBns string

	// 请求携带的token
	Token string
}

func (c *SendHI) Send() error {
	contentForm := messageBody{
		Type:    c.Type,
		Content: c.Content,
	}

	var messageForm hiMessage
	messageForm.Body = append(messageForm.Body, contentForm)
	messageForm.Header = messageHeader{
		ToId: c.GroupCodeList,
	}
	body := infoflowReq{
		Body:           []messageBody{contentForm},
		HeaderInfoflow: messageHeaderInfoflow{ToId: c.GroupCodeList, Type: c.Type},
	}
	url := fmt.Sprintf("http://%s/robot/api/v1/message/sendMessage", getHost(c.RequestBns, AuthorizationToken))
	var res hiRes
	req := httplib.Post(url).JSONBodyQuiet(body).Header("Content-Type", "application/json").Header("token", c.Token).SetTimeout(time.Second * 3)
	_, err := req.Response()
	if err != nil {
		return fmt.Errorf("Send POST To Hi Failed! err:%+v", err)
	}
	err = req.ToJSON(&res)
	if err != nil {
		resStr, _ := req.String()
		return fmt.Errorf("request Hi Failed! err:%+v , response:%s", err, resStr)
	}
	if res.ErrCode != 0 {
		return fmt.Errorf("request Hi Failed! errCode:%d errMsg:%s data:%+v", res.ErrCode, res.ErrMsg, res.Data)
	}
	if len(res.Data.Fail) > 0 {
		return fmt.Errorf("request Hi Failed! errCode:%d errMsg:%s data:%+v", res.ErrCode, res.ErrMsg, res.Data)
	}
	return nil
}

func getHost(serviceName, token string) string {
	if Apptree == nil {
		InitApptree()
	} else {
		threeSecondAgo := time.Now().Add(-3 * time.Second)
		if AppTreeLastUpdateTime.Before(threeSecondAgo) {
			InitApptree()
		}
	}
	insts, err := Apptree.GetInstanceByService(serviceName, token, int(DefaultTimeout))
	if err != nil {
		fmt.Println(err)
		return ""
	}
	i := insts[rand.Intn(len(insts))]
	return fmt.Sprintf("%s:%d", i.IpStr, i.Port)
}
