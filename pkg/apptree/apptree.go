package apptree

import (
	"fmt"
	"math/rand"
	"time"

	"dxm/noah-sdk/noah_golang_sdk/src/libapptree"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/engine/plog"
)

var (
	Apptree               *libapptree.Apptree
	AppTreeLastUpdateTime time.Time
	AuthorizationToken    = "Bearer af9339d22e22b7d3637731bedd018a36"
	DefaultTimeout        = 100 * time.Millisecond
)

func InitApptree() {
	var err error
	AppTreeLastUpdateTime = time.Now()
	Apptree, err = libapptree.ApptreeNew(nil, nil)
	if err != nil {
		panic(err)
	}
}

func GetHost(serviceName string) string {
	var hostnameList []string
	if Apptree == nil {
		InitApptree()
	} else {
		threeSecondAgo := time.Now().Add(-3 * time.Second)
		if AppTreeLastUpdateTime.Before(threeSecondAgo) {
			InitApptree()
		}
	}
	insts, err := Apptree.GetInstanceByService(serviceName, AuthorizationToken, int(DefaultTimeout))
	if err != nil {
		olog.Error("get instance by service error, service name:[%s], err:[%v]", serviceName, err)
		return ""
	}

	for _, v := range insts {
		if v.Status == 0 {
			hostnameList = append(hostnameList, v.HostName)
		}
	}
	if len(hostnameList) == 0 {
		olog.Error("hostname list is null, service name:[%s]", serviceName)
		return ""
	}
	hostname := hostnameList[rand.Intn(len(hostnameList))]
	return hostname
}

func GetServicePort(serviceName string) string {
	var hostnameList []string
	if Apptree == nil {
		InitApptree()
	} else {
		threeSecondAgo := time.Now().Add(-3 * time.Second)
		if AppTreeLastUpdateTime.Before(threeSecondAgo) {
			InitApptree()
		}
	}
	insts, err := Apptree.GetInstanceByService(serviceName, AuthorizationToken, int(DefaultTimeout))
	if err != nil {
		olog.Error("get instance by service error, service name:[%s], err:[%v]", serviceName, err)
		return ""
	}

	for _, v := range insts {
		if v.Status == 0 {
			hostnameList = append(hostnameList, v.HostName)
		}
	}
	if len(hostnameList) == 0 {
		olog.Error("hostname list is null, service name:[%s]", serviceName)
		return ""
	}
	i := insts[rand.Intn(len(insts))]
	return fmt.Sprintf("%s:%d", i.IpStr, i.Port)
}

// 获取实例标签
func GetInstanceTag(serviceName string, selectTag map[string]string) map[string]string {
	insts, err := GetInstanceByBNSWithDisabledV2(serviceName)
	if err != nil {
		olog.Error("get instance by service error, service name:[%s], err:[%v]", serviceName, err)
		return nil
	}

	// 首先选择健康实例非hbas hbbs的标签。如果没有，则选择hbas hbbs的标签。如果没有，则选择第一个实例的标签
	// 其次获取非健康实例标签，如果没有，则选择第一个实例的标签。若没有实例返回空

	// 获取key及对应value
	var (
		selectKey   string
		selectValue string
	)

	for k, v := range selectTag {
		selectKey = k
		selectValue = v
	}

	if len(insts) == 0 {
		plog.EngineLG.Errorf("get instance tag failed, service name:[%s], tag:[%v]", serviceName, selectTag)
		return nil
	}

	for _, v := range insts {
		if v.Status == 0 && v.Disable == 0 && v.Tags[selectKey] == selectValue {
			if v.Tags["service"] == "hbas" || v.Tags["service"] == "hbbs" {
				continue
			}
			plog.EngineLG.Infof("get instance tag success, service name:[%s], tag:[%v]", serviceName, v.Tags)
			return v.Tags
		}
	}

	for _, v := range insts {
		if v.Tags[selectKey] == selectValue && (v.Tags["service"] != "hbas" && v.Tags["service"] != "hbbs") {
			plog.EngineLG.Infof("get invailed instance tag success, service name:[%s], tag:[%v]", serviceName, v.Tags)
			return v.Tags
		}
	}

	for _, v := range insts {
		plog.EngineLG.Infof("get any instance tag success, service name:[%s], tag:[%v]", serviceName, v.Tags)
		return v.Tags
	}

	return nil
}
