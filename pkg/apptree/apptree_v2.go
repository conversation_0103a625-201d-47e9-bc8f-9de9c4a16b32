package apptree

import (
	libapptree_v2 "dxm/noah-sdk/noah_golang_sdk/src/libapptree_v2"
)

const (
	BNS_TOKEN      = "Bearer c4b5267fff153036c87aa815a10b2f45"
	BNS_TIMEOUT    = 3000
	PATH_SEPARATOR = '/'
)

func GetInstanceByBNSWithDisabledV2(serviceName string) (instanceList []libapptree_v2.InstanceInfo, err error) {
	return libapptree_v2.NewAppTree().GetInstanceByCloudServiceWithDisable(serviceName, BNS_TOKEN, BNS_TIMEOUT)
}
