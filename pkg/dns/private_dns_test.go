package private_dns

import (
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestLookupCnameList(t *testing.T) {
	<PERSON><PERSON>("TestLookupCnameList", t, func() {
		<PERSON><PERSON>("success", func() {
			typeA, typeC, err := LookupDomain("duxiaoman.com")
			So(err, ShouldBeNil)
			fmt.Println("A", typeA)
			fmt.Println("CNAME", typeC)
		})
	})
}
func Test_getLocalDnsServer(t *testing.T) {
	<PERSON><PERSON>("Test_getLocalDnsServer", t, func() {
		<PERSON><PERSON>("success", func() {
			output, err := getLocalDnsServer()
			So(err, ShouldBeNil)
			fmt.Println(len(output))
			fmt.Println(output)
		})
	})
}
