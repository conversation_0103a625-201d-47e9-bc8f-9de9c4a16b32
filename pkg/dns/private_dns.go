package private_dns

import (
	"bytes"
	"dxm/siod-cloud/go-common-lib/olog"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/miekg/dns"
)

func LookupDomain(domain string) (typeAList, typeCnameList []string, err error) {
	localDnsServerList, err := getLocalDnsServer()
	if err != nil {
		olog.Error("failed to get local dns server, domain:[%s], err:[%v]", domain, err)
		return
	}

	// 谷歌DNS：8.8.8.8:53
	// 百度DNS：180.76.76.76:53
	// 114-DNS：114.114.114.114:53
	if len(localDnsServerList) == 0 {
		localDnsServerList = append(localDnsServerList, "114.114.114.114", "8.8.8.8")
	}

	c := dns.Client{
		Timeout: 5 * time.Second,
	}

	domain = fmt.Sprintf("%s.", domain)
	m := dns.Msg{}
	m.SetQuestion(domain, dns.TypeA)
	var r *dns.Msg
	for _, localDnsServer := range localDnsServerList {
		r, _, err = c.Exchange(&m, fmt.Sprintf("%s:53", localDnsServer))
		if err != nil {
			olog.Error("failed to exchange domain dns, dnsServer:[%s], domain:[%s], err:[%v]", localDnsServer, domain, err)
			continue
		}
		break
	}

	for _, ans := range r.Answer {
		recordA, isType := ans.(*dns.A)
		if isType {
			typeAList = append(typeAList, recordA.A.String())
		}

		recordC, isType := ans.(*dns.CNAME)
		if isType {
			typeCnameList = append(typeCnameList, recordC.Target)
		}
	}
	return
}

func getLocalDnsServer() (output []string, err error) {
	cmdStr := "cat /etc/resolv.conf | grep nameserver | awk -F' ' '{print $2}'"
	outputStr, _, err := commandExec(cmdStr)
	if err != nil {
		olog.Error("failed to get local dns server, err:[%v]", err)
		return
	}
	outputStr = strings.TrimSpace(outputStr)
	output = strings.Split(outputStr, "\n")
	return
}

func commandExec(cmdStr string) (output, errStr string, err error) {
	//在指定工作路径执行命令
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		return
	}
	output = stdout.String()
	errStr = stderr.String()
	return
}
