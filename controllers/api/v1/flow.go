package controllersApi

import (
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	flow_center "dxm/siod-cloud/go-common-lib/user-center/flow-center"
	scaler_flow "dxm/siod_sre/auto-scaler/models/flow"
	"reflect"

	"github.com/astaxie/beego"

	"dxm/siod-cloud/go-common-lib/ocommon"
)

// Api测试
type ApiFlowController struct {
	beego.Controller
}

var funcsMap map[string]interface{}

// @Title GetMachine
// @Description 查询服务器资产信息API
// @Param   name     query   string true       "机器名"
// @Param   SN    query   string false       "机器SN号"
// @Param   region  query   string      false       "区域filter"
// @Param   status  query   string      false       "状态filter"
// @Param   type query   string      false       "型号filter"
// @Param   sort    query   string  false       "sort option"
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) GetMachine() {
	var r ocommon.ResultInfo

	r.Data = true
	r.Dump("")
	c.Data["json"] = r

	c.ServeJSON()
	return
}

// @Title FuncAsyncCaller
// @Description 查询服务器资产信息API
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) FuncAsyncCaller() {
	var r ocommon.ResultInfo
	var input flow_center.FuncCall
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = input.Valid()
	}
	if r.IsOk() {
		funcName, found := funcsMap[input.FuncName]
		if !found {
			olog.Warn("Can't find funcName  with funcName:%+v while call function",
				input.FuncName)
			return
		}

		f := reflect.ValueOf(funcName)
		args := make([]reflect.Value, len(input.Args))
		for index, param := range input.Args {
			args[index] = reflect.ValueOf(param)
		}
		olog.Trace("len:%+v;args:%+v", len(input.Args), args)
		otool.StartNewRoutine(func() {
			f.Call(args)
		})
	}

	c.Data["json"] = r
	c.ServeJSON()
	return
}

// @Title FuncSyncCaller
// @Description 查询服务器资产信息API
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) FuncSyncCaller() {
	var r ocommon.ResultInfo
	var input flow_center.FuncCall
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = input.Valid()
	}
	if r.IsOk() {
		funcName, found := funcsMap[input.FuncName]
		if !found {
			olog.Warn("Can't find funcName  with funcName:%+v while call function",
				input.FuncName)
			return
		}

		f := reflect.ValueOf(funcName)
		args := make([]reflect.Value, len(input.Args))
		for index, param := range input.Args {
			args[index] = reflect.ValueOf(param)
		}
		olog.Trace("len:%+v;args:%+v", len(input.Args), args)

		result := f.Call(args)
		r = result[0].Interface().(ocommon.ResultInfo)
	}

	c.Data["json"] = r
	c.ServeJSON()
	return
}

// @Title FlowFuncAsyncCaller
// @Description 查询服务器资产信息API
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) FlowFuncAsyncCaller() {
	var r ocommon.ResultInfo
	var input flow_center.FlowFuncCall
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = input.Valid()
	}
	if r.IsOk() {
		funcName, found := funcsMap[input.FuncName]
		if !found {
			olog.Warn("Can't find funcName  with funcName:%+v while call function",
				input.FuncName)
			return
		}

		f := reflect.ValueOf(funcName)
		args := make([]reflect.Value, 6)
		args[0] = reflect.ValueOf(input.Args.FlowInstanceId)
		args[1] = reflect.ValueOf(input.Args.NodeInstanceId)
		args[2] = reflect.ValueOf(input.Args.ListId)
		args[3] = reflect.ValueOf(input.Args.ListType)
		args[4] = reflect.ValueOf(input.Args.NodeSubmitHistory)
		args[5] = reflect.ValueOf(input.Args.NodeReturnHistory)
		otool.StartNewRoutine(func() {
			f.Call(args)
		})
	}

	c.Data["json"] = r
	c.ServeJSON()
	return
}

// @Title FuncSyncCaller
// @Description 查询服务器资产信息API
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) FlowFuncSyncCaller() {
	var r ocommon.ResultInfo
	var input flow_center.FlowFuncCall
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = input.Valid()
	}
	if r.IsOk() {
		funcName, found := funcsMap[input.FuncName]
		if !found {
			olog.Warn("Can't find funcName  with funcName:%+v while call function",
				input.FuncName)
			return
		}

		f := reflect.ValueOf(funcName)
		//args:= []reflect.Value{input.Args.FlowInstanceId,input.Args.NodeInstanceId}
		args := make([]reflect.Value, 6)
		args[0] = reflect.ValueOf(input.Args.FlowInstanceId)
		args[1] = reflect.ValueOf(input.Args.NodeInstanceId)
		args[2] = reflect.ValueOf(input.Args.ListId)
		args[3] = reflect.ValueOf(input.Args.ListType)
		args[4] = reflect.ValueOf(input.Args.NodeSubmitHistory)
		args[5] = reflect.ValueOf(input.Args.NodeReturnHistory)

		result := f.Call(args)
		r = result[0].Interface().(ocommon.ResultInfo)
	}

	c.Data["json"] = r
	c.ServeJSON()
	return
}

// @Title GetListDetail
// @Description 查询服务器资产信息API
// @Param   limit   query   int     false       "count limit"
// @Success 200 {errCode:0,errMsg:"success",data:true}
// @Failure 400 Invalid email supplied
func (c *ApiFlowController) GetListDetail() {
	var r ocommon.ResultInfo
	var input flow_center.ListInfo
	r = otool.ParseRequestParams(&input, &c.Controller)
	if r.IsOk() {
		r = input.Valid()
	}
	listDetail := make(map[string]interface{})
	if r.IsOk() {
		olog.Info("get list detail with listId:%+v,listType:%+v", input.ListId, input.ListType)
		switch input.ListType {
		case "model_expansion":
			listDetail, r = scaler_flow.GetModelListDetail(input.ListId, input.ListType)
		case "auto_scaler_out":
			olog.Info("auto scaler out run")
			listDetail, r = scaler_flow.GetModelListDetail(input.ListId, input.ListType)
		}
	}
	olog.Info("list detail:%+v", listDetail)
	r.Data = listDetail
	c.Data["json"] = r
	c.ServeJSON()
	return
}

func init() {
	funcsMap = map[string]interface{}{
		"AutoSkipOPRoleAuth": scaler_flow.AutoSkipOPRoleAuth,
		"AutoSkipRDRoleAuth": scaler_flow.AutoSkipRDRoleAuth,
		"ChooseScalerOPRole": scaler_flow.ChooseScalerOPRole,
		"ChooseScalerRDRole": scaler_flow.ChooseScalerRDRole,
		"AutoScalerOutRun":   scaler_flow.AutoScalerOutRun,
	}

}
