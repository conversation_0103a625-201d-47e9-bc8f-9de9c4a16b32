package controllersApi

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/models/module"

	"github.com/astaxie/beego"
)

type ApiModuleController struct {
	beego.Controller
}

func (c *ApiModuleController) CreateModelMoudle() {
	var r ocommon.ResultInfo

	otool.StartNewRoutine(func() {
		module.CreateModelMoudle()
	})
	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}

func (c *ApiModuleController) CreateServiceMoudle() {
	var r ocommon.ResultInfo
	var inputData module.ServiceInfo
	r = otool.ParseRequestParams(&inputData, &c.Controller)

	if r.Is<PERSON>k() {
		r = module.CreateServiceMoudle(inputData)
	}

	r.Dump("")
	c.Data["json"] = r
	c.<PERSON>()
}
