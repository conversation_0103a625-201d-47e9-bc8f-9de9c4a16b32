/*
 *    @package: controllers<PERSON>pi
 *    @author: <EMAIL>
 *    @Modifier:
 *    @usage:
 *    @date: 2024-06-15 20:16
 *    @Last modified: 2024-06-15 20:16
 */

package controllersApi

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/engine/executor"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/pkg/service_capacity"

	"github.com/astaxie/beego"
)

type ApiJobController struct {
	beego.Controller
}

type callbackData struct {
	service_capacity.ScaleCallbackResponse
}

func (c *ApiJobController) ServiceCallback() {
	var (
		r         ocommon.ResultInfo
		inputData callbackData
		err       error
	)

	r = otool.ParseRequestParams(&inputData.ScaleCallbackResponse, &c.Controller)
	if r.<PERSON>() {
		var object executor.ExecutorService
		err = object.HandleCallback(inputData.ScaleCallbackResponse)
		if err != nil {
			r = ocommon.GenResultInfo(errno.ERR_CALLBACK_HANDLE_FAILED, err.Error(), nil, nil)
		}
	}

	r.Dump("")
	c.Data["json"] = r
	c.ServeJSON()
}
