/*
*    @package:global
*    @author:<EMAIL>
*    @Modifier:
*    @usage:全局默认配置初始化
*    @date:2018-09-04 16:38:38
 */
package global

import (
	"dxm/siod-cloud/go-common-lib/otool"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/astaxie/beego"

	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"
)

var (
	Developer        string //!开发人员
	AdminToken       string //Noah token
	HaloMasterUrl    string //Halo master url
	DefaultRowsLimit int

	// ra api conf
	RaUrl                  string
	RaPort                 string
	RaPathGetBccInstances  string
	RaPathGetMachineListV2 string
	RaToken                string
	RaPlatform             string

	// send hi conf
	SendHiGroup        int
	MessageServerBns   string
	MessageServerToken string

	// proxy
	ProxyBns string

	// master conf
	MasterBns           string
	MasterCheckInterval int

	YunEnv    string
	YunEnvMap = map[string]string{"yun_env_xd": "小贷云", "yun_env_kj": "科技云", "yun_env_zf": "支付云"}

	ZeroTime, _ = time.ParseInLocation(otool.TIME_FORMAT_STR, "1000-01-01 00:00:00", time.Local)
)

const (
	MDC_TYPE_USE_BNS = 1
	MDC_TYPE_NO_BNS  = 0

	DBS_TYPE_USE_BNS = 1
	DBS_TYPE_NO_BNS  = 0
)

//var DirectorMap

// @Description 获取配置文件信息,用于启动时调试输出
func getConf(key string, keyType string) (value interface{}) {
	var err error
	// beego 获取配置文件时 会自动根据 run mode 获取 所以在这里不用判断
	if keyType == "string" {
		value = beego.AppConfig.String(key)
		if len(strings.TrimSpace(value.(string))) == 0 {
			fmt.Printf(key + " is empty.")
			os.Exit(oerrno.OS_EXIT_FAIL)
		}
		olog.Debug(key, "=(%+v)", value.(string))
	}
	// todo 改为反射方式
	if keyType == "bool" {
		value, err = beego.AppConfig.Bool(key)
		if err != nil {
			fmt.Printf(key + "get error ")
			os.Exit(oerrno.OS_EXIT_FAIL)
		}
		olog.Info(key, "=(%+v)", value)
	}
	if keyType == "int" {
		value, err = beego.AppConfig.Int(key)
		if err != nil {
			fmt.Printf(key + "get error ")
			os.Exit(oerrno.OS_EXIT_FAIL)
		}
		olog.Info(key, "=(%+v)", value)
	}

	return value
}

func InitEnv() {
	//var err error
	olog.Info("ready to init global")

	InitBasicConf()
	InitSendHiConf()
	InitMasterConf()

	olog.Debug("finish to init global")
}

func InitBasicConf() {
	Developer = beego.AppConfig.String("developer")
	YunEnv = getConf("yun_env", "string").(string)
	DefaultRowsLimit = 1000
}

func InitMasterConf() {
	MasterBns = beego.AppConfig.String("bns_info")
	MasterCheckInterval = getConf("master_check_interval", "int").(int)
	if len(MasterBns) == 0 {
		fmt.Printf("bns_info or master_key is empty, bns_info:[%s]", MasterBns)
		olog.Info("bns_info or master_key is empty, bns_info:[%s]", MasterBns)
		os.Exit(oerrno.OS_EXIT_FAIL)
	}
}

func InitSendHiConf() {
	SendHiGroup = getConf("send_hi_group", "int").(int)
	MessageServerBns = getConf("message_server_bns", "string").(string)
	MessageServerToken = getConf("message_server_token", "string").(string)
	olog.Info("send_hi_group:[%d], message_server_bns:[%s], message_server_token:[%s]",
		SendHiGroup, MessageServerBns, MessageServerToken)
}

// !是否为单测环境
func IsEnvTest() (isTest bool) {
	if beego.BConfig.RunMode == "test" {
		return true
	}
	return false
}

// !是否为线上环境
func IsEnvProd() (isTest bool) {
	if beego.BConfig.RunMode == "prod" {
		return true
	}
	return false
}

// !是否为开发环境
func IsEnvDev() (isTest bool) {
	if beego.BConfig.RunMode == "dev" {
		return true
	}
	return false
}
