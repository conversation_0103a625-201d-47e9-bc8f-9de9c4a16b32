FLOW:
  - TYPE: bns # 指定无损类型为bns，则部署时会屏蔽BNS
    SHORT: false  # 指定是否开启短连接检查，默认不开启，长连接检测
    EXIT: true # 指定检查流量超时是否退出，默认false不退出
START:
  - NAME: prod
    SLEEP: 20
    CMD: /home/<USER>/cdn-manager/loadcdn-manager.sh start

STOP:
  - NAME: prod
    SLEEP: 20
    CHECK: 0
    TIMEOUT: 110
    CMD: /home/<USER>/cdn-manager/loadcdn-manager.sh stop

RESTART:
  - NAME: prod
    CHECK: 0   #check 配置为0或者不配置时则是sleep模式无损，针对短连接推荐模式
    CMD: /home/<USER>/cdn-manager/loadcdn-manager.sh restart
    TIMEOUT: 110
    SLEEP: 10
