#!/bin/bash
#file :service.pid is used to store pid

#set -e

dir=`dirname $0`
cd ${dir}
cur=$(pwd)

#BRE BackupRecoveryEngine
SIOD_BRE_SERVICE_ROOT="$cur"

BIN_NAME=auto-scaler
CMD_LINE=${SIOD_BRE_SERVICE_ROOT}/${BIN_NAME}
LOG_DIR=$SIOD_BRE_SERVICE_ROOT/logs

supervise_start_stop_log=$LOG_DIR/supervise_$BIN_NAME.log
nohup_start_stop_log=$LOG_DIR/nohup_$BIN_NAME.log

#use nohup
PID_FILE=${SIOD_BRE_SERVICE_ROOT}/service.pid

#use supervise
BIN_START_STOP=$SIOD_BRE_SERVICE_ROOT/supervise
SUPERVISE_DIR=$SIOD_BRE_SERVICE_ROOT/supervise_status/$BIN_NAME

#make sure PID is belong to $CMD_LINE
#avert operate the other process
status() {
    PID=$1
    process_num=`cat /proc/$PID/cmdline 2>/dev/null|grep $CMD_LINE|wc -l`
    if [ $process_num -gt 0 ] ; then
        echo 1
    else
        echo 0
    fi
}

check() {
    PID=`serverPidBySupervise`
    if [ $PID -eq 0 ];then
        PID=`serverPidByNohup`
    fi
    if [ $PID -eq 0 ];then
        echo "service $CMD_LINE is not running"
    else
        echo "service $CMD_LINE is running. PID:$PID"
    fi
    exit 0
}

init() {
    if [ ! -f $LOG_DIR ] ; then
        mkdir -p $LOG_DIR
    fi
}

############################################################################
# use nohup 

#get servier pid
serverPidByNohup() {
    if [[ ! -f $PID_FILE ]]; then
        echo "0"
        return
    fi
    PID=`head $PID_FILE`
    if [ $PID -ne 0 ]; then
        `ls /proc/$PID >/dev/null 2>/dev/null`
        if [ $? -ne 0 ];then
            echo "0"
        else
            echo $PID
        fi
    else
        echo "0"
    fi
}


startByNohup() {
    echo  "Ready to start service $CMD_LINE: "
    PID=`serverPidByNohup`
    if [ $PID -ne 0 ]; then
        echo "server $CMD_LINE is running. Not allow to start."
        exit 1
    fi

    nohup $CMD_LINE &
    if [ $? -ne 0 ] ; then
        echo "start service $CMD_LINE failed\n"
        exit $?
    else
        PID=$!
        echo service pid $PID
        sleep 1
        ret=`status $PID`
        if [ $ret -eq "1" ] ; then
            echo $PID >$PID_FILE
            echo $PID
            echo "start service $CMD_LINE success"
            exit 0
        else
            echo "start service $CMD_LINE failed"
            exit 1
        fi
    fi
}

stopByNohup() {
    echo "Ready to stop $BIN_NAME By nohup"
    PID=`serverPidByNohup`
    if [ $PID -eq 0 ];then
        echo "server $CMD_LINE is not running"
        exit 0
    fi

    echo  "Ready to stop $CMD_LINE: $PID"
    ret=`status $PID`
    if [ $ret -gt 0 ] ; then
        kill -9 $PID
        ret=$?
    else
        echo "server $CMD_LINE is not running"
        return
    fi
    sleep 5
    ret=`status $PID`
    if [ $ret -gt 0 ] ; then
        echo 1
    else
        echo 0
    fi

}

############################################################################


############################################################################
# use supervise function


serverPidBySupervise() {
    if [ ! -f $SUPERVISE_DIR/status ] ; then
        echo 0
        return
    fi

    PID=`od -tu4 --skip-bytes=16 $SUPERVISE_DIR/status | awk '{print $2}'`
    if [[ $? -ne 0 ]]; then
        echo "0"
    fi
    if [ $PID -ne 0 ]; then
        #make sure process is alive
        ls /proc/$PID >/dev/null 2>/dev/null
        ret=`status $PID`
        if [ $ret -ne 0 ];then
            echo $PID
        else
            echo 0
        fi
    else
        echo "0"
    fi
}

getSupervisePid() {
    pid=`ps aux|grep $BIN_START_STOP|grep -v "grep"|awk '{print $2}'`
    if [ $? -ne 0 ]; then
        echo "0"
        return
    fi
    echo $pid
}

startBySupervise() {
    echo  "Ready to start service $CMD_LINE: "
    PID=`serverPidBySupervise`
    if [ $PID -ne 0 ]; then
        echo "server $CMD_LINE is running. PID=($PID). Not allow to start."
        exit 1
    fi
   
    # start process by supervise 
    if [ ! -f $SUPERVISE_DIR ] ; then
        mkdir -p $SUPERVISE_DIR
    fi
    $BIN_START_STOP -p $SUPERVISE_DIR -f "$CMD_LINE" >/dev/null 2> $supervise_start_stop_log
    sleep 1
    if [ $? -ne 0 ] ; then
        echo "Fail to start service $BIN_NAME. "
        exit 1
    fi
    #check process is running
    PID1=`serverPidBySupervise`
    sleep 1
    PID2=`serverPidBySupervise`
    sleep 1
    if [ $PID1 -ne 0 ] && [ $PID2 -ne 0 ] && [ $PID1 -eq $PID1 ]; then
        ret=`status $PID1`
        if [ $ret -eq "1" ] ; then
            echo "Sucess to start service $BIN_NAME. PID is $PID1. "
            exit 0
        else
            echo "Fail to start service $BIN_NAME. "
            supervisePid=`getSupervisePid` 
            kill -9 $supervisePid
            exit 1
        fi
    fi
    echo "Fail to start service $BIN_NAME. "
    supervisePid=`getSupervisePid` 
    if [ $supervisePid -ne 0 ]; then
        kill -9 $supervisePid
    fi
    exit 1
}


stopBySupervise() {
    echo "Ready to stop $BIN_NAME By supervise"
    PID=`serverPidBySupervise`
    if [ $PID -ne 0 ]; then
        ret=`status $PID`
        if [ $ret -ne 1 ] ; then
            echo "PID($PID) is not belong to current cmdline($CMD_LINE)"
            return 0
        fi
    else
        echo "$BIN_NAME is not running. "
        return 0
    fi

    sv_pid=`grep 'PPid' /proc/$PID/status | awk '{print $2}' 2>/dev/null`
    kill -9 $sv_pid
    sleep 1
    kill -9 $PID

    # make sure PID is killed.
    if [ $PID -ne 0 ]; then
        ret=`status $PID`
        if [ $ret -ne 0 ] ; then
            echo "PID($PID) is still running.  cmdline($CMD_LINE)"
            return 1 
        fi
    fi
    echo "Success to stop $BIN_NAME."
    return 0
}

############################################################################
#main procss

init

case "$1" in
start)
    startBySupervise
    ;;

stop)
    stopBySupervise
    if [ $? -ne 0 ] ; then
        stopByNohup
    fi
    ;;

restart)
    stopBySupervise
    if [ $? -ne 0 ] ; then
        stopByNohup
    fi
    startBySupervise
    ;;

check)
    check 
    ;;

*)
echo "Usage: $0 {start|stop|restart|check}"
exit 1
esac


############################################################################
