# auto-scaler
本服务为度小满服务管理平台后端服务

## 快速开始

+ **cd dxm/siod_sre/auto-scaler && make prepare**

+ **./bee run -gendoc=true -downdoc=true**

以上命令可以在本地一键运行，同时会生成API文档，如果后续要单独生成文档使用./bee generate docs命令

服务访问地址：http://127.0.0.1:8600 
API文档访问地址： http://127.0.0.1:8600/swagger


## 标准构建

代码请下载至$GOPATH/src下

### 编译

1. 云端编译（**推荐**）

+ **cd dxm/siod_sre/auto-scaler && bcloud build**

利用百度云编译服务器对本地代码进行构建，可以保证在代码提交后编译结果一致

2. 本地编译
+ **cd dxm/siod_sre/auto-scaler && make**

由于本地编译环境不统一，如果使用本地构建可能会出现本地编译通过，而代码提交后agile编译不过的情况

注1：两种构建方式均会自动下载所有依赖pacage，包括第三方package，无须提前手动下载至本地，直接执行上述命令即可

注2：编译期间可能会提示需要bcloud login，如果密码忘记，参见[bcloud login](http://buildcloud.baidu.com/bcloud_guide.html#31-bcloud-login)

### 运行

编译结束后所有结果都在output目录下，运行时执行如下命令即可：

+ **cd output && sh loadauto-scaler.sh start**

服务访问地址：http://127.0.0.1:8600


## 测试
如何执行自动化测试

## 如何贡献
贡献patch流程、质量要求

### 讨论
百度Hi讨论群：XXXX
