#初始化项目目录变量
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output
#初始化Go和God系统变量
export GOPATH  := $(HOMEDIR)/../../../../
export GOROOT  := /home/<USER>/soft/go_1.23.0
export GODPATH := /home/<USER>/soft/god-v0-9-0-linux-amd64
export PATH    := $(GODPATH)/bin:$(GOPATH)/bin:$(GOROOT)/bin:$(PATH)
export LIBPATH := $(HOMEDIR)/../../../dxm/siod-cloud/go-common-lib
#初始化命令变量
GO      := go
GOD     := god
GOBUILD := $(GO) build
GOTEST  := $(GO) test
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
#执行编译，可使用命令 make 或 make all 执行, 顺序执行prepare -> test -> compile -> package 几个阶段
all: prepare compile package
# prepare阶段, 使用comake下载非Go依赖，使用GOD下载Go依赖, 可单独执行命令: make prepare
prepare: prepare-dep
prepare-dep:
	git config --global http.sslVerify false #设置git， 保证github mirror能够下载
	export GOPATH=$(GOPATH)
	export GOROOT=$(GOROOT)
	export PATH=$(PATH):${PATH}
	go env -w GO111MODULE="on"
	go env -w GOPROXY=http://goproxy.duxiaoman-int.com/nexus/repository/go-public/
	go env -w GOPRIVATE="*.duxiaoman-int.com"
	go env -w GONOPROXY="**.duxiaoman-int.com**"
	go env -w GONOSUMDB="*"
	go env -w GOSUMDB=off
#complile阶段，执行编译命令,可单独执行命令: make compile
compile:build
build:
	$(HOMEDIR)/bee generate docs
	$(GOBUILD) -o $(HOMEDIR)/auto-scaler
#test阶段，进行单元测试， 可单独执行命令: make test
test: test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)
#package阶段，对编译产出进行打包，输出到output目录, 可单独执行命令: make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv auto-scaler  $(OUTDIR)/
	cp loadauto-scaler.sh supervise $(OUTDIR)/
	rsync -av conf $(OUTDIR)/
	rsync -av deploy ${OUTDIR}/
	rsync -av noahdes ${OUTDIR}/


#install阶段，编译产出放到$GOPATH/bin目录, 可单独执行命令: make install
install: install-bin
install-bin:
#	cp $(OUTDIR)/auto-scaler  $(GOPATH)/bin/
#clean阶段，清除过程中的输出, 可单独执行命令: make clean
clean:
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/auto-scaler
	rm -rf $(GOPATH)/pkg/darwin_amd64

# avoid filename conflict and speed up build
# .PHONY: all prepare compile test package install clean build
