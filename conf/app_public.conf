#应用名称
appname = auto_scaler
EnableDocs = true
copyrequestbody = true
developer = liupanpan_dxm

#静态文件目录
StaticDir = static
#静态文件进行gzip压缩，默认支持 .css 和 .js
StaticExtensionsToGzip = .css, .js
#AngularJS
TokenSignKey = siod-backends

[dev]
httpport = 8421
orm_debug=true
httpaddr = 0.0.0.0

[prod]
httpport = 8122
httpaddr = 0.0.0.0


[test]
httpport = 8700
httpaddr = 0.0.0.0


#子配置文件
include "db.conf"
include "log.conf"
include "send_hi.conf"
include "halo.conf"
include "yun_env.conf"
include "user_center.conf"
include "capacity.conf"
include "master.conf"
include "user_center.conf"