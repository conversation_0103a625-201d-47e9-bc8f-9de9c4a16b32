#数据库读超时时间
db_read_timeout = 40s
#数据库写超时时间
db_write_timeout = 40s
#数据库连接超时时间
db_connect_timeout = 5s

[prod]
#数据库主机名或BNS
db_host_name=<$idc$>
db_port=6005
db_name=cdn_manager
db_user=cdn_manager_rw
db_password=93gU0WKJ1nir49G
#打开数据库orm调试
orm_debug=false

[dev]
#弹性服务测试数据库
db_host_name=127.0.0.1
db_port=8306
db_name=auto_scaler
db_user=root
db_password=B9g3nprvwG8

#容量数据库
ocean_db_host_name=127.0.0.1
ocean_db_port=8306
ocean_db_name=ocap_xdb
ocean_db_user=root
ocean_db_password=B9g3nprvwG8