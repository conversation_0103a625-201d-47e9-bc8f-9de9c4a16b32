#数据库读超时时间
db_read_timeout = 40s
#数据库写超时时间
db_write_timeout = 40s
#数据库连接超时时间
db_connect_timeout = 5s

[prod]
#数据库主机名或BNS
db_host_name=<$idc$>
db_port=6005
db_name=cdn_manager
db_user=cdn_manager_rw
db_password=93gU0WKJ1nir49G
#打开数据库orm调试
orm_debug=false

[dev]
#数据库主机名或BNS
db_host_name=***********
db_port=8306
db_name=cdn_manager
#database user &passwd,未设置使用默认
db_user=root
db_password=B9g3nprvwG8
#打开数据库orm调试
orm_debug=false

[test]
#数据库主机名或BNS
db_host_name=
db_port=
db_name=
#database user &passwd,未设置使用默认
db_user=
db_password=
#打开数据库orm调试
orm_debug=false