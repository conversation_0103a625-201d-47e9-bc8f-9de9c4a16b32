#log
log_filename = logs/auto-scaler.log
#\d+[KMG]? Suffixes are in terms of 2**10
#log_maxsize = 1000M
# \d+[KMG]? Suffixes are in terms of thousands
#log_rotate_lines = 6K
log_rotate_daily = true
log_rotate = true
log_maxbakeup = 99

#需要accessLog的路由
accessRouters =  /*
use_log_id = true


[dev]
#log
log_console = true
#DEBUG
#TRACE
#INFO
#WARNING
#ERROR
#CRITICAL
log_print_level = debug



[prod]
#log
log_console = false
#DEBUG
#TRACE
#INFO
#WARN
#ERROR
#CRITICAL
log_print_level = debug

[test]
#log
log_console = true
log_print_level = debug

