#!/bin/bash
#移动至打包目录,并编译项目
cd ../../../ && mkdir -p gopath/src/ && mv dxm gopath/src/ && cd gopath/src/dxm/siod_sre/auto-scaler && make -f Makefile
yun_env=$1
root_dir=$(cd $(dirname $0); pwd -P)
conf_dir=$root_dir/output/conf
noahdes_dir=$root_dir/output/noahdes
echo $yun_env

replace_conf_file(){
    for value in `ls $conf_dir/$1 | grep $yun_conf_tag`
    do
        conf_name=$(echo ${value%$yun_conf_tag*}${value##*$yun_conf_tag})
        echo $conf_dir/$1/$conf_name
        mv -f $conf_dir/$1/$value $conf_dir/$conf_name
    done
    rm -rf $conf_dir/yun_conf_*
}

replace_noahdes_file(){
    for value in `ls $noahdes_dir | grep $yun_conf_tag`
    do
        conf_name=$(echo ${value%$yun_conf_tag*}${value##*$yun_conf_tag})
        echo $conf_name
        mv -f $noahdes_dir/$value $noahdes_dir/$conf_name
    done
    rm -rf $noahdes_dir/conf_template.value.*
}

if [ "$yun_env" == "prod" ]; then
    rm -rf $noahdes_dir/conf_template.value.*
    rm -rf $conf_dir/yun_conf_*
elif [ "$yun_env" == "prod_xd" ]; then
    yun_conf_tag="\\.xd"
    replace_noahdes_file
    replace_conf_file 'yun_conf_xd'
elif [ "$yun_env" == "prod_zf" ]; then
    yun_conf_tag="\\.zf"
    replace_noahdes_file
    replace_conf_file 'yun_conf_zf'
elif [ "$yun_env" == "prod_kj" ]; then
    yun_conf_tag="\\.kj"
    replace_noahdes_file
    replace_conf_file 'yun_conf_kj'
fi

#进行打包
mv ../../../dxm ../../../../../