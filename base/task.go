package base

// 伸缩模式
const (
	SCALE_MODE_SCALE   = "scale"   // 缩容扩容
	SCALE_MODE_TRAFFIC = "traffic" // 屏蔽解屏蔽
)

// 任务类型
const (
	TASK_TYPE_MANUAL    = "manual"
	TASK_TYPE_ONCE_TASK = "onceTask"
	TASK_TYPE_CRONTAB   = "crontab"
	TASK_TYPE_AUTO_TASK = "autoTask"
)

var TaskTypeMap = map[string]string{
	TASK_TYPE_MANUAL:    "手动任务",
	TASK_TYPE_ONCE_TASK: "单次定时任务",
	TASK_TYPE_CRONTAB:   "循环定时任务",
	TASK_TYPE_AUTO_TASK: "自动化任务",
}

// 任务启用状态
const (
	TASK_STATUS_ENABLE   = "enable"
	TASK_STATUS_DISABLE  = "disable"
	TASK_STATUS_TEST_RUN = "testRun"
	TASK_STATUS_OFFLINE  = "offline"
)

// 任务启用状态
const (
	TASK_MANUAL_STATUS_INIT    = "init"
	TASK_MANUAL_STATUS_RUNNING = "running"
	TASK_MANUAL_STATUS_SUCCESS = "success"
	TASK_MANUAL_STATUS_FAILED  = "failed"
)

var TaskStatusMap = map[string]string{
	"enable":  "使用中",
	"disable": "已下线",
	"testRun": "试运行",
	"offline": "已下线",
}

// 事件类型
const (
	EVENT_TYPE_MONITOR = "eventMonitor" // 监控采集生成事件
	EVENT_TYPE_OTHER   = "eventOther"   // 外部信号生成事件
	EVENT_TYPE_TIME    = "eventTime"    // 时间触发生成事件
	// EVENT_TYPE_OTHER  = "event_other"  // 外部事件
)

// 循环定时任务类型
const (
	TASK_CRONTAB_TYPE_CRON2      = "cronCron"  // 定时缩容_定时扩容
	TASK_CRONTAB_TYPE_EVENT_CRON = "eventCron" // 事件缩容_定时扩容
	TASK_CRONTAB_TYPE_CRON_EVENT = "cronEvent" // 定时缩容_事件扩容
)
