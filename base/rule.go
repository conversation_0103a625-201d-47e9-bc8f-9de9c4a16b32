package base

// 目标值，适用于目标追踪规则和预测规则【目标追踪、预测规则】
type MetricInfo struct {
	MetricName    string  `json:"metricName" description:"指标名称"`
	TargetValue   float64 `json:"targetValue" description:"目标值"`
	TargetRateMax float64 `json:"targetRateMax" description:"目标值上限rate,扩容界限"`
	TargetRateMin float64 `json:"targetRateMin" description:"目标值下限rate,缩容界限"`
}

// 伸缩规则类型
const (
	SCALING_RULE_SIMPLE          = "simple" // 简单规则
	SCALING_RULE_TIME_TRIGGER    = "time"   // 时间触发规则
	SCALING_RULE_EVENT_TRIGGER   = "event"  // 事件触发规则（如外部请求）
	SCALING_RULE_TARGET_TRACKING = "target" // 目标追踪规则
)

var ScaleRuleMap = map[string]string{
	SCALING_RULE_SIMPLE:          "简单规则",   //手动任务
	SCALING_RULE_TIME_TRIGGER:    "时间触发规则", //循环定时
	SCALING_RULE_EVENT_TRIGGER:   "事件触发规则", //循环定时任务，手动任务
	SCALING_RULE_TARGET_TRACKING: "目标追踪规则", //自动化任务
}

// 监控指标名称【目标追踪、预测规则】
const (
	METRIC_NAME_CPU_PERCENT         = "cpuPercent"
	METRIC_NAME_APP_CPU_PERCENT     = "appCpuPercent"
	METRIC_NAME_AVERAGE_COST        = "cost"
	METRIC_NAME_SINGLE_INSTANCE_QPS = "singleQPS"
	METRIC_NAME_WATER_LEVEL         = "waterLevel"
	METRIC_NAME_HTTP_CODE_NUM       = "httpCodeNum"
)

var MetricNameAliasMap = map[string]string{
	METRIC_NAME_CPU_PERCENT:         "CPU使用率",
	METRIC_NAME_APP_CPU_PERCENT:     "服务CPU使用率",
	METRIC_NAME_AVERAGE_COST:        "平均耗时",
	METRIC_NAME_SINGLE_INSTANCE_QPS: "单实例QPS",
	METRIC_NAME_WATER_LEVEL:         "容量水位",
	METRIC_NAME_HTTP_CODE_NUM:       "HTTP错误数",
}

var MetricDBfield = map[string]string{
	METRIC_NAME_CPU_PERCENT: "",
}

// 定时任务运行周期
const (
	CRON_PERIOD_DAY   = "day"
	CRON_PERIOD_MONTH = "month"
	CRON_PERIOD_WEEK  = "week"
)

// 定时任务保护类型
const (
	CRON_PROTECT_TYPE_SUGGEST = "suggest"   // 容量异常时按建议调整实例数执行扩缩容
	CRON_PROTECT_TYPE_PLAN    = "plan"      // 容量异常时按原计划调整实例数执行扩缩容
	CRON_PROTECT_TYPE_NONE    = "noScaling" // 容量异常时不执行扩缩容
)

// ------------------以下方式废弃-----------------------
// 调整方式【简单规则】
const (
	ADJUSTMENT_TYPE_INSTANCE_NUM = "instance_num" // 指定数量(全量)
	ADJUSTMENT_TYPE_INSTANCE_IP  = "instance_ip"  // 指定实例
)

// 规则配置
type ScalingRule struct {
	ScalingRuleId   int    `json:"scalingRuleId" description:"伸缩规则ID"`
	ScalingGroupId  int    `json:"scalingGroupId" description:"伸缩组ID"`
	ScalingRuleName string `json:"scalingRuleName" description:"伸缩规则名称"`

	Enabled bool `json:"enabled" description:"是否启用"`

	// 伸缩规则类型，SimpleScalingPolicy：简单规则、TargetTrackingScalingRule：目标追踪规则、PredictiveScalingRule：预测规则
	ScalingRuleType string `json:"scalingRuleType" description:"伸缩规则类型"`

	//调整方式，TotalCapacity：总量（默认） QuantityChangeInCapacity：增加或减少指定数量的实例数 SpecifyInstance：指定实例【简单规则】
	AdjustmentType string `json:"adjustmentType" description:"调整方式"`

	// 动作 up：增加 down：减少
	Action string `json:"action" description:"动作 up: 增加 down: 减少"`

	// 调整值 TotalCapacity：0~100 QuantityChangeInCapacity：0~100【简单规则】
	AdjustmentValue int `json:"adjustmentValue" description:"调整值"`

	// IP列表，适用于指定实例伸缩规则。仅当调整方式为指定实例时生效
	IpList []string `json:"ipList" description:"IP列表"`

	// 预定义监控项，适用于目标追踪规则和预测规则，且此时该项必选。【目标追踪、预测规则】
	Metric []MetricInfo

	// 自定义公式【自定义公式】
	Formula string `json:"formula" description:"自定义公式"`

	// 周期，单位 次。仅适用于目标追踪规则和预测规则 默认值: 3【目标追踪、预测规则】
	Period int `json:"period" orm:"column(period);size(4);" description:"周期"`

	// 触发次数，仅适用于目标追踪规则和预测规则。默认值：3【目标追踪、预测规则】
	TriggerTimes int `json:"triggerTimes" orm:"column(trigger_times);size(4);" description:"触发次数"`

	// 是否禁用缩容，仅适用于目标追踪规则。默认值：false【目标追踪】
	DisableScaleIn bool `json:"disableScaleIn" description:"是否禁用缩容"`

	// 冷却时间，单位：秒。仅适用于目标追踪规则和预测规则 默认值：600【目标追踪、预测规则】
	CooldownTime int `json:"cooldownTime" description:"冷却时间"`
}

/*
** 指标名称
** cpu使用率：CpuUtilization
** 服务cpu使用率：AppCpuUtilization
** 平均耗时：AverageCost
** 单实例QPS：SingleInstanceQPS
** 容量水位：WaterLevel
 */

// 监控指标公式渲染
/*
	cpu.max
	cpu.avg
	cpu.min

	cpu_service.max
	cpu_service.avg
	cpu_service.min

	cost
	qps
	water_level

	cpu_service.avg * instance_num / 40
*/
