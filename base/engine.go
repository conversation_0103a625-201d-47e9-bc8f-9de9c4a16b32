package base

// 引擎名称
const (
	ENGINE_NAME_MODEL            = "model"
	ENGINE_NAME_SERVICE_CAPACITY = "service_capacity"
)

const (
	// engine任务运行状态
	ENGINE_TASK_STATUS_READY          = 1 // 引擎任务已准备完成
	ENGINE_TASK_STATUS_RUNNING        = 2 // 引擎任务运行中
	ENGINE_TASK_STATUS_SUCCESS        = 3 // 引擎任务成功
	ENGINE_TASK_STATUS_FAILED         = 4 // 引擎任务失败
	ENGINE_TASK_STATUS_PARTIAL_FAILED = 5 // 引擎任务部分失败

	// 引擎任务最大重试次数
	ENGINE_TASK_MAX_RETRY_TIMES = 2
)

var EngineTaskStatusMap = map[int]string{
	ENGINE_TASK_STATUS_READY:          "任务初始化",
	ENGINE_TASK_STATUS_RUNNING:        "任务运行中",
	ENGINE_TASK_STATUS_SUCCESS:        "任务运行成功",
	ENGINE_TASK_STATUS_FAILED:         "任务运行失败",
	ENGINE_TASK_STATUS_PARTIAL_FAILED: "任务部分失败",
}

// 执行动作
const (
	ACTION_UP   = "up"   //扩容
	ACTION_DOWN = "down" // 缩容
)

var ActionMap = map[string]string{
	"up":   "扩容",
	"down": "缩容",
}

// 扩缩容引擎动作
var ScaleActionMap = map[string]string{
	"up":   "ScaleUp",
	"down": "ScaleDown",
}

// 扩缩容引擎动作模式
var ScaleModelMap = map[string]string{
	"scale":   "elasticRealScale",   // 真实扩容
	"traffic": "elasticFlowOperate", // 流量控制
}
