/*
 *    @package: routers
 *    @author: <EMAIL>
 *    @Modifier:
 *    @usage: 路由入口文件
 *    @date: 2019-09-26 16:48
 *    @Last modified: 2019-09-26 16:48
 */

package routers

import (
	"dxm/siod_sre/auto-scaler/routers/api"
	"dxm/siod_sre/auto-scaler/routers/ui"

	controllersUi "dxm/siod_sre/auto-scaler/controllers/ui/pporf"

	"github.com/astaxie/beego"
)

func init() {
	//pprof设置，用于性能调优
	beego.Router("/debug/pprof", &controllersUi.ProfController{})
	beego.Router("/debug/pprof/:app([\\w]+)", &controllersUi.ProfController{})

	apiNs := beego.NewNamespace("/auto-scaler/api/v1",
		api.TaskNs(),
		api.FlowNs(),
		api.ModuleNs(),
		api.JobNs(),
	)
	beego.AddNamespace(apiNs)

	// uiNs为前端路由信息
	uiNs := beego.NewNamespace("/auto-scaler/ui/",
		ui.ModuleNs(),
		ui.TaskNs(),
		ui.RuleNs(),
	)
	beego.AddNamespace(uiNs)

}
