package api

import (
	controllersApi "dxm/siod_sre/auto-scaler/controllers/api/v1"

	"github.com/astaxie/beego"
)

func FlowNs() beego.LinkNamespace {
	flowNs := beego.NSNamespace("/flow/",
		beego.NSRouter("GetListDetail",
			&controllersApi.ApiFlowController{}, "*:GetListDetail"),
		beego.NSRouter("FlowFuncAsyncCaller",
			&controllersApi.ApiFlowController{}, "*:FlowFuncAsyncCaller"),
		beego.NSRouter("FlowFuncSyncCaller",
			&controllersApi.ApiFlowController{}, "*:FlowFuncSyncCaller"),
		beego.NSRouter("FuncSyncCaller",
			&controllersApi.ApiFlowController{}, "*:FuncSyncCaller"),
	)
	return flowNs
}
