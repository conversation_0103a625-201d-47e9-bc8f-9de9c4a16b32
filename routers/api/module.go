package api

import (
	controllersApi "dxm/siod_sre/auto-scaler/controllers/api/v1"

	"github.com/astaxie/beego"
)

func ModuleNs() beego.LinkNamespace {
	moduleNs := beego.NSNamespace("/module/",
		beego.NSRouter("model/create",
			&controllersApi.ApiModuleController{}, "*:CreateModelMoudle"),
		beego.NSRouter("service/create",
			&controllersApi.ApiModuleController{}, "*:CreateServiceMoudle"),
	)
	return moduleNs
}
