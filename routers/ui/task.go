/*
 *    @package: ui
 *    @author: wangyong<PERSON><EMAIL>
 *    @Modifier:
 *    @usage:
 *    @date: 2019-12-25 14:58
 *    @Last modified: 2019-12-25 14:58
 */

package ui

import (
	"dxm/siod_sre/auto-scaler/controllers/ui/task"

	"github.com/astaxie/beego"
)

func TaskNs() beego.LinkNamespace {
	taskNs := beego.NSNamespace("/task/",
		beego.NSRouter("list", &task.TaskController{}, "*:GetTaskList"),
		beego.NSRouter("history", &task.TaskController{}, "*:GetTaskHistory"),
		beego.NSRouter("status/update", &task.TaskController{}, "*:TaskStatusUpdate"),
		beego.NSRouter("status/delete", &task.TaskController{}, "*:TaskDelete"),

		// 手动任务
		beego.NSRouter("manual/check", &task.TaskController{}, "*:ManualTaskCheck"),
		beego.NSRouter("manual/run", &task.TaskController{}, "*:ManualTaskRun"),
		beego.NSRouter("manual/rule", &task.TaskController{}, "*:GetManualRule"),
		beego.NSRouter("manual/create", &task.TaskController{}, "*:ManualTaskCreate"),
		beego.NSRouter("manual/update", &task.TaskController{}, "*:ManualTaskUpdate"),

		// 定时任务
		beego.NSRouter("crontab/rule", &task.TaskController{}, "*:GetCrontabRule"),
		beego.NSRouter("crontab/create", &task.TaskController{}, "*:CrontabTaskCreate"),
		beego.NSRouter("crontab/update", &task.TaskController{}, "*:CrontabTaskUpdate"),

		// 自动任务
		beego.NSRouter("autoTask/rule", &task.TaskController{}, "*:GetAutoTaskRule"),
		beego.NSRouter("autoTask/create", &task.TaskController{}, "*:AutoTaskCreate"),
		beego.NSRouter("autoTask/update", &task.TaskController{}, "*:AutoTaskUpdate"),
		beego.NSRouter("autoTask/metric", &task.TaskController{}, "*:GetMetricValue"),
	)

	return taskNs
}

// 	beego.NSRouter("manual/update", &task.TaskController{}, "*:MunualTaskUpdate"),
// 	beego.NSRouter("manual/delete", &task.TaskController{}, "*:MunualTaskDelete"),
// 	beego.NSRouter("manual/detailList", &task.TaskController{}, "*:MunualTaskDetailList"),

// 	// 自动任务
// 	beego.NSRouter("auto/statusSwitch", &task.TaskController{}, "*:AutoTaskStatusSwitch"),
// 	beego.NSRouter("auto/list", &task.TaskController{}, "*:AutoTaskList"),
// 	beego.NSRouter("auto/create", &task.TaskController{}, "*:AutoTaskCreate"),
// 	beego.NSRouter("auto/update", &task.TaskController{}, "*:AutoTaskUpdate"),
// 	beego.NSRouter("auto/delete", &task.TaskController{}, "*:AutoTaskDelete"),
// 	beego.NSRouter("auto/detailList", &task.TaskController{}, "*:AutoTaskDetailList"),
// )
// return taskNs
// }
