/*
 *    @package: ui
 *    @author: wangyong<PERSON><EMAIL>
 *    @Modifier:
 *    @usage:
 *    @date: 2019-12-25 14:58
 *    @Last modified: 2019-12-25 14:58
 */

package ui

import (
	"github.com/astaxie/beego"

	"dxm/siod_sre/auto-scaler/controllers/ui/module"
)

func ModuleNs() beego.LinkNamespace {
	moduleNs := beego.NSNamespace("/module/",
		beego.NSRouter("instance", &module.ModuleController{}, "*:GetOnlineIns"),
		beego.NSRouter("create", &module.ModuleController{}, "*:CreateScalableGroup"),
		beego.NSRouter("update", &module.ModuleController{}, "*:ModifyScalableGroup"),
		beego.NSRouter("list", &module.ModuleController{}, "*:GetScalableGroupList"),
		beego.NSRouter("delete", &module.ModuleController{}, "*:DeleteScalableGroup"),
		beego.NSRouter("disableAutoTask", &module.ModuleController{}, "*:DisableAutoTask"),
		beego.NSRouter("getModuleNameList", &module.ModuleController{}, "*:GetModuleNameList"),
	)
	return moduleNs
}
