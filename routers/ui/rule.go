package ui

import (
	"github.com/astaxie/beego"

	"dxm/siod_sre/auto-scaler/controllers/ui/rule"
)

func RuleNs() beego.LinkNamespace {
	ruleNs := beego.NSNamespace("/rule/",
		beego.NSRouter("model/list", &rule.RuleController{}, "*:GetRuleModel"),
		beego.NSRouter("model/create", &rule.RuleController{}, "*:CreateRuleModel"),
		beego.NSRouter("model/modify", &rule.RuleController{}, "*:ModifyRuleModel"),
		beego.NSRouter("model/delete", &rule.RuleController{}, "*:DeleteRuleModel"),
	)
	return ruleNs
}
