/*
*    @package:errno
*    @author:<EMAIL>
*    @Modifier:
*    @usage:
 *   @date: 2024-03-15 16:34
 *   @Last modified: 2024-03-15 16:34
*/
package errno

const (
	OK = 0
)
const (
	ERR_DB_QUERY_FAILED = 1000 + iota
	ERR_DB_UPDATE_FAILED
	ERR_DB_INSERT_FAILED
	ERR_DB_ROLLBACK_ERROR
)

const (
	ERR_INVALID_INPUT = 2000 + iota
	ERR_INVALID_RESULT
	ERR_JSON_UNMARSHAL_ERROR
	ERR_JSON_MARSHAL_ERROR
	ERR_PERMISSON_NOT_EXIST
)

const (
	ERR_CREATE_DUPLICATE_TASK = 3000 + iota
	ERR_TASK_PARA_INVAILED
	ERR_MODEL_ENGINE_TALK_FAILED
	ERR_MODULE_ALREADY_EXIST
	ERR_CALLBACK_HANDLE_FAILED
)

var (
	ErrnoMap = map[int]string{
		ERR_INVALID_INPUT:            "无效输入",
		ERR_INVALID_RESULT:           "无效结果",
		ERR_DB_QUERY_FAILED:          "查询数据错误",
		ERR_DB_UPDATE_FAILED:         "数据库更新失败",
		ERR_DB_INSERT_FAILED:         "数据库插入失败",
		ERR_DB_ROLLBACK_ERROR:        "数据库回滚失败",
		ERR_JSON_UNMARSHAL_ERROR:     "json解析失败",
		ERR_JSON_MARSHAL_ERROR:       "生成json字符串失败",
		ERR_CREATE_DUPLICATE_TASK:    "创建重复任务",
		ERR_TASK_PARA_INVAILED:       "任务字段无效",
		ERR_MODEL_ENGINE_TALK_FAILED: "模型引擎调用失败",
		ERR_MODULE_ALREADY_EXIST:     "模块已存在",
		ERR_CALLBACK_HANDLE_FAILED:   "回调信息处理失败",
		ERR_PERMISSON_NOT_EXIST:      "没有操作权限",
	}
)
