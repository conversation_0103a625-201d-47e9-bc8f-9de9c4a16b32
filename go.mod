module dxm/siod_sre/auto-scaler

go 1.23

require (
	bou.ke/monkey v1.0.2
	github.com/antonfisher/nested-logrus-formatter v1.3.1
	github.com/astaxie/beego v1.12.3
	github.com/jordan-wright/email v4.0.1-0.20210109023952-943e75fe5223+incompatible
	github.com/lestrrat/go-file-rotatelogs v0.0.0-20180223000712-d3151e2a480f
	github.com/parnurzeal/gorequest v0.2.16
	github.com/prometheus/client_golang v1.14.0
	github.com/prometheus/common v0.42.0
	github.com/sirupsen/logrus v1.8.1
	github.com/stretchr/testify v1.10.0
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.1.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/elazarl/goproxy v1.7.2 // indirect
	github.com/fastly/go-utils v0.0.0-20180712184237-d95a45783239 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/jehiah/go-strftime v0.0.0-20171201141054-1d33003b3869 // indirect
	github.com/jonboulle/clockwork v0.5.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/lestrrat/go-envload v0.0.0-20180220120943-6ed08b54a570 // indirect
	github.com/lestrrat/go-strftime v0.0.0-20180220042222-ba3bf9c1d042 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/procfs v0.8.0 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/tebeka/strftime v0.1.5 // indirect
	golang.org/x/crypto v0.33.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	google.golang.org/protobuf v1.33.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	moul.io/http2curl v1.0.0 // indirect
)

require (
	dxm/noah-sdk/noah_golang_sdk v0.0.0-00010101000000-000000000000
	dxm/siod-cloud/go-common-lib v0.0.0-00010101000000-000000000000
	github.com/bitly/go-simplejson v0.5.1 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/go-sql-driver/mysql v1.5.0
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/miekg/dns v1.0.14
	github.com/petermattis/goid v0.0.0-20220302125637-5f11c28912df // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/robfig/cron/v3 v3.0.1
	github.com/shiena/ansicolor v0.0.0-20200904210342-c7312218db18 // indirect
	github.com/smartystreets/goconvey v1.8.1
	github.com/toolkits/pkg v1.3.7
	golang.org/x/net v0.35.0 // indirect

)

replace dxm/siod-cloud/go-common-lib v0.0.0-00010101000000-000000000000 => gitlab.duxiaoman-int.com/siod-cloud/go-common-lib v0.0.0-20220913122512-b59c48cec708

replace dxm/noah-sdk/noah_golang_sdk v0.0.0-00010101000000-000000000000 => gitlab.duxiaoman-int.com/noah-sdk/noah_golang_sdk v0.0.4
