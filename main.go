/*
 *    @package: main
 *    @author: <EMAIL>
 *    @Modifier:
 *    @usage:
 *    @date: 2019-09-26 16:48
 *    @Last modified: 2019-09-26 16:48
 */
package main

import (
	"github.com/astaxie/beego"

	"dxm/siod_sre/auto-scaler/engine"
	"dxm/siod_sre/auto-scaler/env"
	_ "dxm/siod_sre/auto-scaler/routers"
)

func main() {
	env.InitAllModels()

	//!自动化文档
	if beego.BConfig.RunMode == "dev" {
		beego.BConfig.WebConfig.DirectoryIndex = true
		beego.BConfig.WebConfig.StaticDir["/swagger"] = "swagger"
	}

	engine.Engine.Run()

	beego.Run()
}
