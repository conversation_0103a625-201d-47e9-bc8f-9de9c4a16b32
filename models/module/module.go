package module

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/models/comm"
	"encoding/json"
	"fmt"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"

	"github.com/astaxie/beego/orm"
)

type ModuleInfo struct {
	Name     string                    `json:"name"`
	HbaNum   int                       `json:"hba_num"`
	HbbNum   int                       `json:"hbb_num"`
	IsShared string                    `json:"is_shared"`
	Version  string                    `json:"version"`
	InfoList []dao_ocean.RiskModelInfo `json:"info_list"`
}

type ModuleListInput struct {
	ID         int    `json:"id"`
	Search     string `json:"search"`
	ModuleType string `json:"type"`
	PageNum    int64  `json:"pageNum"`
	PageSize   int64  `json:"pageSize"`
}

type ModuleCreateInput struct {
	ID          int                      `json:"id"`
	Name        string                   `json:"name"`
	ServiceName string                   `json:"serviceName"`
	ModuleType  string                   `json:"type"`
	CoolDown    int                      `json:"coolDown"`
	IntanceInfo []dao.ModuleInstanceInfo `json:"intanceInfo"`
}

type ModuleDeleteInput struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	ServiceName string `json:"serviceName"`
	ModuleType  string `json:"type"`
}

type DisableAutoTaskInput struct {
	ID              int    `json:"id"`
	Name            string `json:"name"`
	ServiceName     string `json:"serviceName"`
	ModuleType      string `json:"type"`
	DisableAutoTask bool   `json:"disableAutoTask"`
}

func GetModuleNameList(name, typeT string) (r ocommon.ResultInfo) {
	var riskModelList []string
	var dataList []dao_ocean.NoahModules
	dataList, r = dao_ocean.CreateNoahModulesPtr().GetOceanModuleListByType(name, typeT)
	if !r.IsOk() {
		olog.Error("failed to get noah moudles from ocean database, err:[%v]", r)
	}

	for _, v := range dataList {
		riskModelList = append(riskModelList, v.BnsName)
	}
	r.Data = riskModelList
	return
}

// 获取伸缩组列表
func GetScalableGroupList(input ModuleListInput) (r ocommon.ResultInfo) {
	if input.ID > 0 {
		var moduleInfo dao.Module
		r = dao.CreateModulePtr().SearchByPk(&moduleInfo, input.ID)
		if !r.IsOk() {
			olog.Error("failed to get module info from database by key, id:[%d] err:[%v]", input.ID, r)
			return
		}
		r.Data = map[string]interface{}{
			"total":      1,
			"pageNum":    input.PageNum,
			"pageSize":   input.PageSize,
			"moduleList": []dao.Module{moduleInfo},
		}
		return
	}

	if input.PageNum == 0 {
		input.PageNum = 1
	}
	if input.PageSize == 0 {
		input.PageSize = 20
	}
	var searchField []string = []string{"ServiceName"}

	var cond1 *orm.Condition //模糊搜索字段cond
	var cond2 *orm.Condition //确定字段cond
	var cond3 *orm.Condition //用于结合cond1 和 cond2
	query := oquery.NewQueryStructOfTable()
	// 所有在 SearchField 的 都去搜索
	cond := orm.NewCondition()
	// 解决搜素内容为空，返回全部信息问题
	if input.Search != "" {
		for _, searchItem := range searchField {
			cond1 = cond.AndCond(cond1).OrCond(cond.And(searchItem+"__"+oquery.OP_I_CONTAINS, input.Search))
		}
		cond1 = cond.AndCond(cond1)
	}
	cond2 = cond.And("Dflag"+"__"+oquery.OP_EQUAL, 0).And("ModuleType"+"__"+oquery.OP_EQUAL, input.ModuleType)
	cond3 = cond.AndCond(cond1).AndCond(cond2)
	query.AddCustomCondition(cond3)

	dataCnt, r := dao.CreateModulePtr().GetCountByQuery(query)
	if !r.IsOk() {
		debugData, _ := json.Marshal(&input)
		olog.Error("failed to get count of module from database, input:[%s], err:[%v]", string(debugData), r)
		return
	}

	var moduleInfoList []dao.Module
	query.SetPageInfo(input.PageNum, input.PageSize)
	r = dao.CreateModulePtr().SearchByQuery(&moduleInfoList, query)
	if !r.IsOk() {
		debugData, _ := json.Marshal(&input)
		olog.Error("failed to get module list from database, input:[%s], err:[%v]", string(debugData), r)
		return
	}
	r.Data = map[string]interface{}{
		"total":      dataCnt,
		"pageNum":    input.PageNum,
		"pageSize":   input.PageSize,
		"moduleList": moduleInfoList,
	}
	return
}

// 创建伸缩组
func CreateScalableGroup(input ModuleCreateInput) (r ocommon.ResultInfo) {
	var engineName string

	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateCreateScalableGroupParams(input)
	if !r.IsOk() {
		olog.Error("create scalable group params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 根据模块类型设置名称和引擎
	switch input.ModuleType {
	case base.MODULE_TYPE_MODEL:
		input.Name = fmt.Sprintf("%s_mod", input.ServiceName)
		engineName = base.ENGINE_NAME_MODEL
	case base.MODULE_TYPE_POD:
		input.Name = fmt.Sprintf("%s_pod", input.ServiceName)
		engineName = base.ENGINE_NAME_SERVICE_CAPACITY
	default:
		olog.Warn("module type is invalid when create scalable group, input_data:[%s]", string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module type is invalid", nil, nil)
		return
	}

	// 查询是否存在相同模块
	var moduleSlice []dao.Module
	r = dao.CreateModulePtr().SearchByColumn(&moduleSlice, &dao.Module{ServiceName: input.ServiceName, ModuleType: input.ModuleType}, []string{"ServiceName", "ModuleType"})
	if !r.IsOk() {
		olog.Error("failed to get module from database, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}
	if len(moduleSlice) > 0 {
		olog.Warn("the module already exist, input_data:[%s]", string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_MODULE_ALREADY_EXIST, "the module already exist", nil, nil)
		return
	}

	// 序列化实例信息
	instanceInfoJson, err := json.Marshal(input.IntanceInfo)
	if err != nil {
		olog.Error("failed to marshal instance info when create scalable group, input_data:[%s], err:[%v]", string(inputDebug), err)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "failed to process instance info", nil, nil)
		return
	}

	// 插入模块记录
	insertId, r := dao.CreateModulePtr().Insert(&dao.Module{
		Name:            input.Name,
		ServiceName:     input.ServiceName,
		ModuleType:      input.ModuleType,
		CoolDown:        input.CoolDown,
		InstanceInfo:    string(instanceInfoJson),
		NextTime:        time.Now(),
		DisableAutoTask: false,
		DisableTime:     global.ZeroTime,
		ScaleEngine:     engineName,
		LastModifyTime:  time.Now(),
	})
	if !r.IsOk() {
		olog.Error("failed to insert module into database when create module, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	olog.Info("scalable group created successfully, moduleId:[%d], input_data:[%s]", insertId, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", insertId, nil)
	return
}

func ModifyScalableGroup(input ModuleCreateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateModifyScalableGroupParams(input)
	if !r.IsOk() {
		olog.Error("modify scalable group params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 查询模块是否存在
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, input.ID)
	if !r.IsOk() {
		olog.Error("failed to get module from database when update scalable group, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	if moduleInfo.ID == 0 {
		olog.Error("module not found when update scalable group, moduleId:[%d], input_data:[%s]", input.ID, string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module not found", nil, nil)
		return
	}

	// 序列化实例信息
	instanceInfoJson, err := json.Marshal(input.IntanceInfo)
	if err != nil {
		olog.Error("failed to marshal instance info when update scalable group, input_data:[%s], err:[%v]", string(inputDebug), err)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "failed to process instance info", nil, nil)
		return
	}

	// 更新模块信息
	updateData := &dao.Module{
		CoolDown:       input.CoolDown,
		InstanceInfo:   string(instanceInfoJson),
		LastModifyTime: time.Now(),
	}
	updateFields := []string{"CoolDown", "InstanceInfo", "LastModifyTime"}

	_, r = dao.CreateModulePtr().UpdateByPk(updateData, updateFields, input.ID)
	if !r.IsOk() {
		olog.Error("failed to update module info in database when update module, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	olog.Info("scalable group updated successfully, moduleId:[%d], input_data:[%s]", input.ID, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", input.ID, nil)
	return
}

func DeleteScalableGroup(input ModuleDeleteInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 查询是否存在该模块 (只查询未删除的模块)
	var moduleList []dao.Module
	r = dao.CreateModulePtr().SearchByColumn(&moduleList, &dao.Module{
		ServiceName: input.ServiceName,
		ModuleType:  input.ModuleType,
		Dflag:       0, // 只查询未删除的模块
	}, []string{"ServiceName", "ModuleType", "Dflag"})
	if !r.IsOk() {
		olog.Error("failed to get module from database when delete scalable group, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	if len(moduleList) == 0 {
		olog.Error("module not found when delete scalable group, input_data:[%s]", string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module not found", nil, nil)
		return
	}

	if len(moduleList) > 1 {
		msg := fmt.Sprintf("multiple modules found, count: %d", len(moduleList))
		olog.Error("%s, input_data:[%s]", msg, string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, msg, nil, nil)
		return
	}

	moduleInfo := moduleList[0]

	// 软删除模块 (设置 Dflag = 1)
	updateData := &dao.Module{
		Dflag:          1,
		LastModifyTime: time.Now(),
	}
	updateFields := []string{"Dflag", "LastModifyTime"}

	_, r = dao.CreateModulePtr().UpdateByPk(updateData, updateFields, moduleInfo.ID)
	if !r.IsOk() {
		olog.Error("failed to soft delete module from database when delete scalable group, moduleId:[%d], input_data:[%s], err:[%v]", moduleInfo.ID, string(inputDebug), r)
		return
	}

	olog.Info("scalable group soft deleted successfully, moduleId:[%d], serviceName:[%s], moduleType:[%s]",
		moduleInfo.ID, input.ServiceName, input.ModuleType)
	r = ocommon.GenResultInfo(0, "success", moduleInfo.ID, nil)
	return
}

func DisableAutoTask(input DisableAutoTaskInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateDisableAutoTaskParams(input)
	if !r.IsOk() {
		olog.Error("disable auto task params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 查询模块是否存在
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, input.ID)
	if !r.IsOk() {
		olog.Error("failed to get module from database when disable auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	if moduleInfo.ID == 0 {
		olog.Error("module not found when disable auto task, moduleId:[%d], input_data:[%s]", input.ID, string(inputDebug))
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module not found", nil, nil)
		return
	}

	// 更新模块的自动任务禁用状态
	updateData := &dao.Module{
		DisableAutoTask: input.DisableAutoTask,
		DisableTime:     global.ZeroTime,
		LastModifyTime:  time.Now(),
	}
	updateFields := []string{"DisableAutoTask", "DisableTime", "LastModifyTime"}

	_, r = dao.CreateModulePtr().UpdateByPk(updateData, updateFields, input.ID)
	if !r.IsOk() {
		olog.Error("failed to update disable auto task status in database, moduleId:[%d], input_data:[%s], err:[%v]", input.ID, string(inputDebug), r)
		return
	}

	statusText := "enabled"
	if input.DisableAutoTask {
		statusText = "disabled"
	}

	olog.Info("auto task status updated successfully, moduleId:[%d], status:[%s], input_data:[%s]",
		input.ID, statusText, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

func GetOnlineIns(service, moduleType, idcTag string) (r ocommon.ResultInfo) {
	var (
		tagInsList []comm.TagIns
	)

	tagInsList, r = comm.GetOnlineIns(service, moduleType, idcTag)
	if !r.IsOk() {
		olog.Error("failed to get online instance, service:[%s], module_type:[%s], idc_tag:[%s], err:[%v]", service, moduleType, idcTag, r)
		return
	}

	r = ocommon.GenResultInfo(errno.OK, "", tagInsList, nil)
	return
}

// validateCreateScalableGroupParams 验证创建伸缩组参数
func validateCreateScalableGroupParams(input ModuleCreateInput) (r ocommon.ResultInfo) {
	// 验证服务名称
	if input.ServiceName == "" {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "serviceName is required", nil, nil)
		return
	}

	// 验证模块类型
	if input.ModuleType == "" {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "type is required", nil, nil)
		return
	}

	// 验证模块类型是否有效
	validTypes := []string{base.MODULE_TYPE_MODEL, base.MODULE_TYPE_POD}
	if !contains(validTypes, input.ModuleType) {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "type is invalid", nil, nil)
		return
	}

	// 验证冷却时间
	if input.CoolDown < 300 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "coolDown must be at least 300 seconds", nil, nil)
		return
	}

	// 验证实例信息
	if len(input.IntanceInfo) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "intanceInfo is required", nil, nil)
		return
	}

	// 验证每个实例信息
	for i, instanceInfo := range input.IntanceInfo {
		if instanceInfo.Tag == "" {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("tag is required at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MaxNum <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("maxNum must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinNum <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minNum must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum < 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum must be greater than or equal to 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinNum > instanceInfo.MaxNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minNum cannot be greater than maxNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum > instanceInfo.MaxNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum cannot be greater than maxNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum < instanceInfo.MinNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum cannot be less than minNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MaxFactor <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("maxFactor must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinFactor <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minFactor must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinFactor > instanceInfo.MaxFactor {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minFactor cannot be greater than maxFactor at index %d", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateModifyScalableGroupParams 验证更新伸缩组参数
func validateModifyScalableGroupParams(input ModuleCreateInput) (r ocommon.ResultInfo) {
	// 验证模块ID
	if input.ID <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "id is required and must be greater than 0", nil, nil)
		return
	}

	// 验证冷却时间 (如果提供)
	if input.CoolDown != 0 && input.CoolDown < 600 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "coolDown must be at least 600 seconds", nil, nil)
		return
	}

	// 验证实例信息
	if len(input.IntanceInfo) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "intanceInfo is required", nil, nil)
		return
	}

	// 验证每个实例信息
	for i, instanceInfo := range input.IntanceInfo {
		if instanceInfo.Tag == "" {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("tag is required at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MaxNum <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("maxNum must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinNum <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minNum must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum < 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum must be greater than or equal to 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinNum > instanceInfo.MaxNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minNum cannot be greater than maxNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum > instanceInfo.MaxNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum cannot be greater than maxNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.CurrNum < instanceInfo.MinNum {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("currNum cannot be less than minNum at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MaxFactor <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("maxFactor must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinFactor <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minFactor must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if instanceInfo.MinFactor > instanceInfo.MaxFactor {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("minFactor cannot be greater than maxFactor at index %d", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateDisableAutoTaskParams 验证禁用自动任务参数
func validateDisableAutoTaskParams(input DisableAutoTaskInput) (r ocommon.ResultInfo) {
	// 验证模块ID
	if input.ID <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "id is required and must be greater than 0", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// contains 检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
