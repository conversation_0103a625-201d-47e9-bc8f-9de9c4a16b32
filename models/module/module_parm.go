package module

// const (
// 	// 模块类型
// 	MODULE_TYPE_BNS_POD = "BNS_POD"
// 	MODULE_TYPE_MODEL   = "RISK_MODEL"

// 	// 引擎名称
// 	MODULE_SCALE_ENGINE_MODEL = "model"
// 	MODULE_SCALE_ENGINE_POD   = "kubernetes"

// 	// 任务类型
// 	TASK_TYPE_MANUAL    = "task_type_manual"
// 	TASK_TYPE_ONCE_TASK = "task_type_once_task"
// 	TASK_TYPE_CRONTAB   = "task_type_crontab"
// 	TASK_TYPE_AUTO_TASK = "task_type_auto_task"

// 	// 伸缩规则类型
// 	SCALING_RULE_SIMPLE           = "SimpleScalingPolicy"       // 简单规则
// 	SCALING_RULE_SPECIFY_INSTANCE = "SpecifyInstance"           // 指定实例
// 	SCALING_RULE_TARGET_TRACKING  = "TargetTrackingScalingRule" // 目标追踪规则

// 	// 调整方式【简单规则】
// 	ADJUSTMENT_TYPE_INSTANCE_NUM = "instance_num" // 指定数量(全量)
// 	ADJUSTMENT_TYPE_INSTANCE_IP  = "instance_ip"  // 指定实例

// 	// 任务启用状态
// 	TASK_STATUS_ENABLE   = "enable"
// 	TASK_STATUS_DISABLE  = "disable"
// 	TASK_STATUS_TEST_RUN = "test_run"

// 	// 监控指标名称【目标追踪、预测规则】
// 	METRIC_NAME_CPU                 = "CpuUtilization"
// 	METRIC_NAME_APP_CPU             = "AppCpuUtilization"
// 	METRIC_NAME_AVERAGE_COST        = "AverageCost"
// 	METRIC_NAME_SINGLE_INSTANCE_QPS = "SingleInstanceQPS"
// 	METRIC_NAME_WATER_LEVEL         = "WaterLevel"
// )

// var TaskTypeMap = map[string]string{
// 	"task_type_manual":    "手动任务",
// 	"task_type_once_task": "单次定时任务",
// 	"task_type_crontab":   "循环定时任务",
// 	"task_type_auto_task": "自动化任务",
// }

// var ScaleRuleMap = map[string]string{
// 	"SimpleScalingPolicy":       "简单规则",
// 	"TargetTrackingScalingRule": "目标追踪规则",
// 	"SpecifyInstance":           "指定IP规则",
// }

// // 执行动作
// const (
// 	ACTION_UP   = "up"   //扩容
// 	ACTION_DOWN = "down" // 缩容
// )

// var ActionMap = map[string]string{
// 	"up":   "扩容",
// 	"down": "缩容",
// }

// // engine任务运行状态
// const (
// 	ENGINE_INFO_STATUS_READY   = 1
// 	ENGINE_INFO_STATUS_RUNNING = 2
// 	ENGINE_INFO_STATUS_SUCCESS = 3
// 	ENGINE_INFO_STATUS_FAILED  = 4
// )

// var TaskStatusMap = map[string]string{
// 	"enable":   "使用中",
// 	"disable":  "已下线",
// 	"test_run": "试运行",
// }

// // 目标值，适用于目标追踪规则和预测规则【目标追踪、预测规则】
// type MetricInfo struct {
// 	MetricName  string  `json:"metricName" description:"指标名称"`
// 	TargetValue float64 `json:"targetValue" description:"目标值"`
// }
