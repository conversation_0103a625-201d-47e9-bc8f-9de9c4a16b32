package module

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/base"
	base_tool "dxm/siod_sre/auto-scaler/base/tool"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/data_process"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"
)

// 模型模块管理
type ModelInfo struct {
	Name       string // appId-stateId模型名称
	AppId      int
	StateId    int
	IsShare    int
	ModuleName string
	Tag        string
	ModuleId   int
	TaskId     int
	RuleId     int
	InsInfo    dao.ModuleInstanceInfo
}

func CreateModelMoudle() (r ocommon.ResultInfo) {
	// 获取全量模型列表
	var dataList []dao_ocean.NoahModules
	dataList, r = dao_ocean.CreateNoahModulesPtr().GetRiskModelList("") // 获取所有模型
	if !r.IsOk() {
		olog.Error("failed to get noah moudles from ocean database, err:[%v]", r)
		return
	}

	for _, v := range dataList {
		var object ModelInfo
		object.Name = v.BnsName
		// 测试使用
		// if object.Name != "40280-1001" {
		// 	continue
		// }
		object.init()
		// 判断该模型是否需要接入
		if !object.checkModelNeedAccess() {
			continue
		}

		// 接入模型
		object.accessModel()
	}

	return
}

func (m *ModelInfo) init() {
	arr := strings.Split(m.Name, "-")
	if len(arr) < 2 {
		olog.Error("model name error when manage model info, name:[%s]", m.Name)
		return
	}
	appId, err1 := strconv.Atoi(arr[0])
	stateId, err2 := strconv.Atoi(arr[1])
	if err1 != nil || err2 != nil {
		olog.Error("failed to strconv string to int when manage model info, name:[%s], err1:[%v], err2:[%v]", m.Name, err1, err2)
		return
	}
	m.AppId = appId
	m.StateId = stateId

	// 获取模型版本
	riskModelInfo, r := dao_ocean.CreateRiskModelInfoPtr().GetRiskModelInfoByAppId(m.AppId, m.StateId, "")
	if !r.IsOk() {
		olog.Error("failed to get risk model info when manage model info, name:[%s], err: %v", m.Name, r)
		return
	}

	m.IsShare = riskModelInfo.IsShare
}

func (m *ModelInfo) checkModelNeedAccess() (isNeedAccess bool) {
	// 是否为独占模型，若为共享模型则不接入
	// if m.IsShare == base.MODULE_TYPE_SHARE {
	// 	return
	// }

	// 查询是否已接入，若已接入则不需要再接入
	// isAccess, err := m.isModelAccess()
	// if err != nil {
	// 	olog.Error("check model access status failed when check model need access, name:[%s], err:[%v]", m.Name, err)
	// 	return
	// }
	// if isAccess { // 如果已经接入，则不需要再接入
	// 	return
	// }

	// 查询是否存在采集数据
	isMonitorVaild, err := m.checkMonitorDataVaild()
	if err != nil {
		olog.Error("check monitor monitor vaild when check model need access, name:[%s], err:[%v]", m.Name, err)
		return
	}
	if !isMonitorVaild { // 如果采集数据为无效数据，则无需接入
		return
	}

	return true
}

func (m *ModelInfo) isModelAccess() (isAccess bool, err error) {
	var r ocommon.ResultInfo
	var daoModulePtr = dao.CreateModulePtr()
	var modelInfos []dao.Module

	r = daoModulePtr.SearchByColumn(&modelInfos, &dao.Module{
		ServiceName: m.Name,
		ModuleType:  base.MODULE_TYPE_MODEL,
	}, []string{"ServiceName", "ModuleType"})
	if !r.IsOk() {
		olog.Error("failed to get module info from database when check model access status, name:[%s], err:[%v]", m.Name, r)
		return false, errors.New("read db error")
	}
	if len(modelInfos) != 0 {
		return true, nil
	}
	return
}

func (m *ModelInfo) checkMonitorDataVaild() (isVaild bool, err error) {
	// 查询数据库检查最近几个周期数据是否均有效，有效性判断为 qps及cost不为0
	detailList := dao_ocean.OceanDetailSearchLatest20("risk_model", m.Name)
	if len(detailList) == 0 {
		olog.Warn("failed to get model data from database when check monitor data vaild, bns:[%s], err:[%v]", m.Name)
		return false, errors.New("read data from database failed")
	}

	var qpsSum, costSum float64
	for _, v := range detailList {
		qpsSum += v.QpsAvg
		costSum += v.CostAvg
	}

	// qps或cost数据不为0，表征采集有效
	if qpsSum != 0 || costSum != 0 {
		return true, nil
	}
	return
}

// 模型接入操作
func (m *ModelInfo) accessModel() {
	m.MoudleAdd()

	for _, v := range []string{"hba", "hbb"} {
		m.Tag = v
		m.TaskAdd()
	}
}

// 获取监控
func (m *ModelInfo) getMonitorInfo() (monitor []dao_ocean.OceanDetail) {
	return dao_ocean.OceanDetailSearchLatestBetweenTime(
		"risk_model",
		m.Name,
		base_tool.GetLastMinuteByTime(time.Now()).Format(otool.TIME_FORMAT_STR),
		time.Now().Format(otool.TIME_FORMAT_STR))

}

func (s *ModelInfo) MoudleAdd() (err error) {
	var (
		moduleList []dao.Module
		r          ocommon.ResultInfo
		ptr        = dao.CreateModulePtr()
		moduleId   int
	)
	r = ptr.SearchByColumn(&moduleList,
		&dao.Module{ServiceName: s.Name, ModuleType: base.MODULE_TYPE_MODEL},
		[]string{"ServiceName", "ModuleType"})

	if !r.IsOk() {
		olog.Error("failed to get module info from database, module:%s, err:%v", s.Name, r)
		err = errors.New("get module info from database")
		return
	}
	if len(moduleList) != 0 {
		s.ModuleId = moduleList[0].ID
		return
	}

	// 查询监控
	monitor := s.getMonitorInfo()
	var tagInsList []dao.ModuleInstanceInfo
	var tagList []string = []string{"hba", "hbb"}
	for _, tag := range tagList {
		for _, v := range monitor {
			if tag == v.LogicIdc {
				tagInsList = append(tagInsList, dao.ModuleInstanceInfo{
					Tag:       tag,
					MaxNum:    int(float64(v.InsCount) * 2),
					MinNum:    int(float64(v.InsCount) * 0.5),
					CurrNum:   v.InsCount,
					MaxFactor: 0.5,
					MinFactor: 2,
				})
				break
			}
		}
	}
	insJson, _ := json.Marshal(&tagInsList)

	var daoModulePtr = dao.CreateModulePtr()
	moduleInfo := dao.Module{
		Name:            fmt.Sprintf("%s_model", s.Name),
		ServiceName:     s.Name,
		ModuleType:      base.MODULE_TYPE_MODEL,
		CoolDown:        900,
		ScaleEngine:     base.ENGINE_NAME_MODEL,
		NextTime:        global.ZeroTime,
		LastModifyTime:  time.Now(),
		InstanceInfo:    string(insJson),
		DisableAutoTask: false,
		DisableTime:     global.ZeroTime,
	}
	queryModule := oquery.NewQueryStructOfTable()
	queryModule.AddConditonsByOperator("Name", oquery.OP_EQUAL, moduleInfo.Name)
	queryModule.AddConditonsByOperator("ServiceName", oquery.OP_EQUAL, moduleInfo.ServiceName)
	queryModule.AddConditonsByOperator("ModuleType", oquery.OP_EQUAL, moduleInfo.ModuleType)
	moduleCols := []string{"InstanceInfo", "LastModifyTime"}
	_, moduleId, r = daoModulePtr.UpdateOrCreateByQuery(&moduleInfo, moduleCols, queryModule)
	if !r.IsOk() {
		olog.Error("failed to insert data to module info for database, service_name:[%s], err:[%v]", moduleInfo.ServiceName, r)
		return
	}

	s.ModuleId = moduleId
	s.ModuleName = s.Name + "_model"
	return
}

func (s *ModelInfo) TaskAdd() (err error) {

	var (
		taskList []dao.TaskInfo
		taskId   int
		r        ocommon.ResultInfo
	)

	r = dao.CreateTaskInfoPtr().SearchByColumn(&taskList, &dao.TaskInfo{
		ModuleId: s.ModuleId,
		IdcTag:   s.Tag,
		TaskType: base.TASK_TYPE_AUTO_TASK,
	}, []string{"ModuleId", "IdcTag", "TaskType"})
	if !r.IsOk() {
		olog.Error("failed to get task info from database, service:%s, err:%v", s.Name, r)
		return
	}

	if len(taskList) == 0 {
		taskId, r = dao.CreateTaskInfoPtr().Insert(&dao.TaskInfo{
			ID:             taskId,
			ModuleId:       s.ModuleId,
			ModuleName:     s.ModuleName,
			TaskModuleType: base.MODULE_TYPE_MODEL,
			TaskName:       fmt.Sprintf("%s_auto_task", s.Name),
			TaskType:       base.TASK_TYPE_AUTO_TASK,
			TaskStatus:     base.TASK_STATUS_TEST_RUN,
			IdcTag:         s.Tag,
			LastModifyTime: time.Now(),
			SchedMode:      base.SCALE_MODE_SCALE,
			Dflag:          0,
		})
		if !r.IsOk() {
			olog.Error("failed to insert task info to database, service:%s, err:%v", s.Name, r)
			err = errors.New("insert task data to database failed")
			return
		}
		s.TaskId = taskId
		var daoModule dao.Module
		dao.CreateModulePtr().SearchByColumn(&daoModule, &dao.Module{ID: s.ModuleId}, []string{"ID"})
		if daoModule.TaskIdList == "" {
			taskInfoJson, _ := json.Marshal(&[]int{taskId})
			daoModule.TaskIdList = string(taskInfoJson)
		} else {
			var taskIds []int
			json.Unmarshal([]byte(daoModule.TaskIdList), &taskIds)
			taskIds = append(taskIds, taskId)
			taskJson, _ := json.Marshal(&taskIds)
			daoModule.TaskIdList = string(taskJson)
		}
		dao.CreateModulePtr().UpdateByPk(&daoModule, []string{"TaskIdList"}, s.ModuleId)
	} else {
		s.TaskId = taskList[0].ID
	}

	s.RuleAdd()

	return
}

func (s *ModelInfo) RuleAdd() (err error) {
	var (
		daoRuleList  []dao.RuleOnline
		r            ocommon.ResultInfo
		ruleId       int
		newRuleId    int
		updateRuleId int64
	)
	r = dao.CreateRuleOnlinePtr().SearchByColumn(&daoRuleList, &dao.RuleOnline{
		ModuleId: s.ModuleId, TaskId: s.TaskId,
	}, []string{"ModuleId", "TaskId"})
	if !r.IsOk() {
		olog.Error("failed to insert rule info to database, service:%s, err:%v", s.Name, r)
		err = errors.New("insert rule data to database failed")
		return
	}

	singleInsQps := s.getSingleInsQps()
	metrics := []base.MetricInfo{
		{
			MetricName:    base.METRIC_NAME_CPU_PERCENT,
			TargetValue:   30,
			TargetRateMax: 0.2,
			TargetRateMin: 0.3,
		},
		{
			MetricName:    base.METRIC_NAME_SINGLE_INSTANCE_QPS,
			TargetValue:   singleInsQps,
			TargetRateMax: 0.3,
			TargetRateMin: 0.5,
		},
	}
	metricsJson, _ := json.Marshal(&metrics)

	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, s.ModuleId)
	query.AddConditonsByOperator("TaskIds", oquery.OP_EQUAL, s.TaskId)
	query.AddConditonsByOperator("ScalingRuleType", oquery.OP_EQUAL, base.SCALING_RULE_TARGET_TRACKING)

	updateRuleId, newRuleId, r = dao.CreateRuleOnlinePtr().UpdateOrCreateByQuery(&dao.RuleOnline{
		ModuleId:           s.ModuleId,
		TaskId:             s.TaskId,
		Name:               fmt.Sprintf("%s_auto_rule", s.Name),
		ScalingRuleType:    base.SCALING_RULE_TARGET_TRACKING,
		TargetMetric:       string(metricsJson),
		TargetPeriod:       5,
		TargetTriggerTimes: 4,
		TargetCanScaling:   false,
		LastModifyTime:     time.Now(),
	}, []string{"Name", "TargetMetric", "TargetPeriod", "TargetTriggerTimes", "LastModifyTime"}, query)
	if !r.IsOk() {
		olog.Error("failed to update or create rule info to database, service:%s, err:%v", s.Name, r)
		err = errors.New("update or create rule data to database failed")
		return
	}
	if len(daoRuleList) == 0 {
		ruleId = newRuleId
	} else {
		ruleId = int(updateRuleId)
	}

	dao.CreateRuleOnlinePtr().UpdateByPk(&dao.TaskInfo{RuleId: ruleId}, []string{"RuleId"}, s.TaskId)
	return
}

func (m *ModelInfo) getSingleInsQps() (singleInstanceQPS float64) {
	// 获取1天内数据点
	var costList []float64
	var startTime = time.Now().Add(-24 * time.Hour).Format(otool.TIME_FORMAT_STR)
	var endTime = time.Now().Format(otool.TIME_FORMAT_STR)
	dataList := dao_ocean.OceanDetailSearchLatestBetweenTime("risk_model", m.Name, startTime, endTime)
	if len(dataList) == 0 {
		return
	}

	for _, v := range dataList {
		if v.CostAvg == 0 {
			continue
		}
		costList = append(costList, v.CostAvg)
	}

	cost95thPercent, _ := data_process.Obtain95thPercent(costList)
	return float64(1000) / cost95thPercent / float64(3)
}
