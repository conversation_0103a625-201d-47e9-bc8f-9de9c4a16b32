package module

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/errno"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 测试专用的模块服务
type testModuleService struct {
	noahModules       []dao_ocean.NoahModules
	modules           []dao.Module
	moduleInstances   []dao.ModuleInstanceInfo
	shouldReturnError bool
	errorCode         int
	errorMessage      string
	createdModuleId   int
}

// 创建测试服务实例
func newTestModuleService() *testModuleService {
	return &testModuleService{
		createdModuleId: 1,
	}
}

// 设置Noah模块数据
func (t *testModuleService) setNoahModules(modules []dao_ocean.NoahModules) {
	t.noahModules = modules
}

// 设置模块数据
func (t *testModuleService) setModules(modules []dao.Module) {
	t.modules = modules
}

// 设置错误返回
func (t *testModuleService) setError(code int, message string) {
	t.shouldReturnError = true
	t.errorCode = code
	t.errorMessage = message
}

// 模拟GetModuleNameList函数
func (t *testModuleService) mockGetModuleNameList(name, typeT string) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	var riskModelList []string
	for _, module := range t.noahModules {
		riskModelList = append(riskModelList, module.BnsName)
	}

	return ocommon.GenResultInfo(0, "success", riskModelList, nil)
}

// 模拟GetScalableGroupList函数
func (t *testModuleService) mockGetScalableGroupList(input ModuleListInput) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	// 设置默认分页参数
	if input.PageNum == 0 {
		input.PageNum = 1
	}
	if input.PageSize == 0 {
		input.PageSize = 20
	}

	// 模拟分页逻辑
	total := len(t.modules)
	start := int((input.PageNum - 1) * input.PageSize)
	end := int(input.PageNum * input.PageSize)
	if end > total {
		end = total
	}

	var pageModules []dao.Module
	if start < total {
		pageModules = t.modules[start:end]
	}

	type ModuleListRes struct {
		PageInfo struct {
			PageNum  int64 `json:"pageNum"`
			PageSize int64 `json:"pageSize"`
			Total    int   `json:"total"`
		} `json:"pageInfo"`
		ModuleDetail []dao.Module `json:"moduleDetail"`
	}

	result := ModuleListRes{
		ModuleDetail: pageModules,
	}
	result.PageInfo.PageNum = input.PageNum
	result.PageInfo.PageSize = input.PageSize
	result.PageInfo.Total = total

	return ocommon.GenResultInfo(0, "success", result, nil)
}

// 模拟CreateScalableGroup函数
func (t *testModuleService) mockCreateScalableGroup(input ModuleCreateInput) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	// 基本参数验证
	if input.ServiceName == "" {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "service name is empty", nil, nil)
	}

	if input.ModuleType == "" {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module type is empty", nil, nil)
	}

	// 检查是否已存在
	for _, module := range t.modules {
		if module.ServiceName == input.ServiceName && module.ModuleType == input.ModuleType {
			return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module already exists", nil, nil)
		}
	}

	return ocommon.GenResultInfo(0, "success", t.createdModuleId, nil)
}

func TestGetModuleNameList(t *testing.T) {
	tests := []struct {
		name          string
		inputName     string
		inputType     string
		noahModules   []dao_ocean.NoahModules
		shouldError   bool
		errorCode     int
		errorMessage  string
		expectedCount int
		wantErr       bool
	}{
		{
			name:      "valid module name list",
			inputName: "test",
			inputType: "web",
			noahModules: []dao_ocean.NoahModules{
				{BnsName: "test-service-1"},
				{BnsName: "test-service-2"},
			},
			shouldError:   false,
			expectedCount: 2,
			wantErr:       false,
		},
		{
			name:          "empty module list",
			inputName:     "nonexistent",
			inputType:     "web",
			noahModules:   []dao_ocean.NoahModules{},
			shouldError:   false,
			expectedCount: 0,
			wantErr:       false,
		},
		{
			name:         "database error",
			inputName:    "test",
			inputType:    "web",
			noahModules:  []dao_ocean.NoahModules{},
			shouldError:  true,
			errorCode:    errno.ERR_INVALID_RESULT,
			errorMessage: "database error",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestModuleService()
			testService.setNoahModules(tt.noahModules)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			result := testService.mockGetModuleNameList(tt.inputName, tt.inputType)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					moduleList := result.Data.([]string)
					assert.Equal(t, tt.expectedCount, len(moduleList))
				}
			}
		})
	}
}

func TestGetScalableGroupList(t *testing.T) {
	tests := []struct {
		name          string
		input         ModuleListInput
		modules       []dao.Module
		shouldError   bool
		errorCode     int
		errorMessage  string
		expectedCount int
		wantErr       bool
	}{
		{
			name: "valid scalable group list",
			input: ModuleListInput{
				Search:     "test",
				ModuleType: "web",
				PageNum:    1,
				PageSize:   10,
			},
			modules: []dao.Module{
				{ID: 1, ServiceName: "test-service-1", ModuleType: "web"},
				{ID: 2, ServiceName: "test-service-2", ModuleType: "web"},
			},
			shouldError:   false,
			expectedCount: 2,
			wantErr:       false,
		},
		{
			name: "default page parameters",
			input: ModuleListInput{
				Search:     "test",
				ModuleType: "web",
				PageNum:    0, // 应该被设置为1
				PageSize:   0, // 应该被设置为20
			},
			modules: []dao.Module{
				{ID: 1, ServiceName: "test-service-1", ModuleType: "web"},
			},
			shouldError:   false,
			expectedCount: 1,
			wantErr:       false,
		},
		{
			name: "database error",
			input: ModuleListInput{
				Search:     "test",
				ModuleType: "web",
				PageNum:    1,
				PageSize:   10,
			},
			modules:      []dao.Module{},
			shouldError:  true,
			errorCode:    errno.ERR_INVALID_RESULT,
			errorMessage: "database error",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestModuleService()
			testService.setModules(tt.modules)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			result := testService.mockGetScalableGroupList(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					// 由于返回的是interface{}，我们需要进行类型断言
					// 这里简化处理，只验证没有错误
					assert.NotNil(t, result.Data)
				}
			}
		})
	}
}

func TestCreateScalableGroup(t *testing.T) {
	tests := []struct {
		name            string
		input           ModuleCreateInput
		existingModules []dao.Module
		shouldError     bool
		errorCode       int
		errorMessage    string
		expectedId      int
		wantErr         bool
	}{
		{
			name: "valid scalable group creation",
			input: ModuleCreateInput{
				ServiceName: "test-service",
				ModuleType:  "web",
				Name:        "test-module",
				CoolDown:    300,
			},
			existingModules: []dao.Module{},
			shouldError:     false,
			expectedId:      1,
			wantErr:         false,
		},
		{
			name: "empty service name",
			input: ModuleCreateInput{
				ServiceName: "",
				ModuleType:  "web",
				Name:        "test-module",
				CoolDown:    300,
			},
			existingModules: []dao.Module{},
			shouldError:     false,
			wantErr:         true,
		},
		{
			name: "module already exists",
			input: ModuleCreateInput{
				ServiceName: "test-service",
				ModuleType:  "web",
				Name:        "test-module",
				CoolDown:    300,
			},
			existingModules: []dao.Module{
				{
					ID:          1,
					ServiceName: "test-service",
					ModuleType:  "web",
				},
			},
			shouldError: false,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestModuleService()
			testService.setModules(tt.existingModules)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			result := testService.mockCreateScalableGroup(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					createdId := result.Data.(int)
					assert.Equal(t, tt.expectedId, createdId)
				}
			}
		})
	}
}
