package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/models/comm"
	"encoding/json"
	"fmt"
	"time"
)

// ManualTaskUpdateInput 手动任务更新输入参数
type ManualTaskUpdateInput struct {
	ID            int    `json:"id"`            // 任务ID
	ManualAction  string `json:"manualAction"`  // 手动操作动作: up(增加) 或 down(减少)
	AdjustNum     int    `json:"adjustNum"`     // 调整实例数量
	BlockAutoTask bool   `json:"blockAutoTask"` // 是否禁止自动任务
	CooldownTime  int    `json:"cooldownTime"`  // 冷却时间(分钟)
}

// ManualTaskUpdateResult 手动任务更新结果
type ManualTaskUpdateResult struct {
	TaskId int `json:"taskId"` // 更新的任务ID
}

// CrontabTaskUpdateInput 定时任务更新输入参数
type CrontabTaskUpdateInput struct {
	ID              int             `json:"id"`              // 任务ID
	AdjustNum       int             `json:"adjustNum"`       // 调整实例数
	SchedType       string          `json:"schedType"`       // 定时任务模式
	CronProtectType string          `json:"cronProtectType"` // 保护类型
	StepCount       int             `json:"stepCount"`       // 步进次数
	StepInterval    int             `json:"stepInterval"`    // 步进间隔时间(分钟)
	ScaleOutRule    CrontabRuleInfo `json:"scaleOutRule"`    // 扩容规则
	ScaleInRule     CrontabRuleInfo `json:"scaleInRule"`     // 缩容规则
}

// CrontabTaskUpdateResult 定时任务更新结果
type CrontabTaskUpdateResult struct {
	TaskId int `json:"taskId"` // 更新的任务ID
}

// AutoTaskUpdateInput 自动化任务更新输入参数
type AutoTaskUpdateInput struct {
	ID                 int               `json:"id"`                 // 任务ID
	StepCount          int               `json:"stepCount"`          // 步进次数
	StepInterval       int               `json:"stepInterval"`       // 步进间隔时间(分钟)
	TargetPeriod       int               `json:"targetPeriod"`       // 周期
	TargetTriggerTimes int               `json:"targetTriggerTimes"` // 触发次数
	TargetCanScaling   bool              `json:"targetCanScaling"`   // 是否可以缩容
	TargetMetric       []base.MetricInfo `json:"targetMetric"`       // 指标列表
}

// AutoTaskUpdateResult 自动化任务更新结果
type AutoTaskUpdateResult struct {
	TaskId int `json:"taskId"` // 更新的任务ID
}

// TaskStatusUpdateInput 任务状态更新输入参数
type TaskStatusUpdateInput struct {
	IDs    []int  `json:"ids"`    // 任务ID组
	Status string `json:"status"` // 任务状态
}

// TaskStatusUpdateResult 任务状态更新结果
type TaskStatusUpdateResult struct {
	UpdatedCount int `json:"updatedCount"` // 更新的任务数量
}

// TaskDeleteInput 任务删除输入参数
type TaskDeleteInput struct {
	IDs []int `json:"ids"` // 任务ID组
}

// TaskDeleteResult 任务删除结果
type TaskDeleteResult struct {
	DeletedCount int `json:"deletedCount"` // 删除的任务数量
}

// TaskUpdateInput 任务更新通用接口
type TaskUpdateInput interface {
	GetID() int
	GetTaskType() string
}

// TaskUpdateContext 任务更新上下文
type TaskUpdateContext struct {
	TaskInfo   dao.TaskInfo
	ModuleInfo dao.Module
	TagInsInfo comm.TagIns
}

// 实现接口方法
func (input ManualTaskUpdateInput) GetID() int {
	return input.ID
}

func (input ManualTaskUpdateInput) GetTaskType() string {
	return base.TASK_TYPE_MANUAL
}

func (input CrontabTaskUpdateInput) GetID() int {
	return input.ID
}

func (input CrontabTaskUpdateInput) GetTaskType() string {
	return base.TASK_TYPE_CRONTAB
}

func (input AutoTaskUpdateInput) GetID() int {
	return input.ID
}

func (input AutoTaskUpdateInput) GetTaskType() string {
	return base.TASK_TYPE_AUTO_TASK
}

func UpdateManualTask(input ManualTaskUpdateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateManualTaskUpdateParams(input)
	if !r.IsOk() {
		olog.Error("manual task update params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证任务并准备更新上下文
	context, r := validateAndPrepareTaskUpdate(input, "manual")
	if !r.IsOk() {
		return
	}

	// 查询关联的规则信息
	var ruleInfo dao.RuleOnline
	r = dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, context.TaskInfo.RuleId)
	if !r.IsOk() {
		olog.Error("rule not found when update manual task, taskId:[%d], ruleId:[%d], err:[%v]", input.ID, context.TaskInfo.RuleId, r)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "rule not found", nil, nil)
		return
	}

	// 更新规则信息
	updateRuleData := &dao.RuleOnline{
		CurrentNum:     context.TagInsInfo.TotalNum,
		AvailNum:       context.TagInsInfo.AvailNum,
		ManualAction:   input.ManualAction,
		AdjustNum:      input.AdjustNum,
		BlockAutoTask:  input.BlockAutoTask,
		CooldownTime:   input.CooldownTime,
		LastModifyTime: time.Now(),
	}

	updateRuleFields := []string{"CurrentNum", "AvailNum", "ManualAction", "AdjustNum", "BlockAutoTask", "CooldownTime", "LastModifyTime"}
	_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, context.TaskInfo.RuleId)
	if !r.IsOk() {
		olog.Error("failed to update rule when update manual task, taskId:[%d], ruleId:[%d], err:[%v]", input.ID, context.TaskInfo.RuleId, r)
		return
	}

	// 更新任务的最后修改时间
	r = updateTaskModifyTime(input.ID, "manual")
	if !r.IsOk() {
		return
	}
	// 更新任务状态
	r = updateTaskStatus(input.ID, "manual", base.TASK_MANUAL_STATUS_INIT)
	if !r.IsOk() {
		return
	}

	result := ManualTaskUpdateResult{
		TaskId: input.ID,
	}

	olog.Info("manual task updated successfully, taskId:[%d], input_data:[%s]", input.ID, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

func UpdateCrontabTask(input CrontabTaskUpdateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateCrontabTaskUpdateParams(input)
	if !r.IsOk() {
		olog.Error("crontab task update params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证任务并准备更新上下文
	context, r := validateAndPrepareTaskUpdate(input, "crontab")
	if !r.IsOk() {
		return
	}

	// 更新定时任务规则
	r = updateCrontabTaskRules(input, context.TaskInfo, context.TagInsInfo)
	if !r.IsOk() {
		olog.Error("failed to update crontab task rules, taskId:[%d], err:[%v]", input.ID, r)
		return
	}

	// 更新任务的最后修改时间和调度类型
	updateTaskData := &dao.TaskInfo{
		SchedType:      input.SchedType,
		LastModifyTime: time.Now(),
	}
	updateTaskFields := []string{"SchedType", "LastModifyTime"}
	_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateTaskData, updateTaskFields, input.ID)
	if !r.IsOk() {
		olog.Error("failed to update task info when update crontab task, taskId:[%d], err:[%v]", input.ID, r)
		return
	}

	result := CrontabTaskUpdateResult{
		TaskId: input.ID,
	}

	olog.Info("crontab task updated successfully, taskId:[%d], input_data:[%s]", input.ID, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

func UpdateAutoTask(input AutoTaskUpdateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateAutoTaskUpdateParams(input)
	if !r.IsOk() {
		olog.Error("auto task update params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证任务并准备更新上下文
	context, r := validateAndPrepareTaskUpdate(input, "auto")
	if !r.IsOk() {
		return
	}

	// 将指标信息转换为JSON字符串
	targetMetricJson, err := json.Marshal(input.TargetMetric)
	if err != nil {
		olog.Error("failed to marshal target metric when update auto task, input_data:[%s], err:[%v]", string(inputDebug), err)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "failed to process target metric", nil, nil)
		return
	}

	// 更新规则信息
	updateRuleData := &dao.RuleOnline{
		CurrentNum:         context.TagInsInfo.TotalNum,
		AvailNum:           context.TagInsInfo.AvailNum,
		StepCount:          input.StepCount,
		StepInterval:       input.StepInterval,
		TargetPeriod:       input.TargetPeriod,
		TargetTriggerTimes: input.TargetTriggerTimes,
		TargetCanScaling:   input.TargetCanScaling,
		TargetMetric:       string(targetMetricJson),
		LastModifyTime:     time.Now(),
	}

	updateRuleFields := []string{"CurrentNum", "AvailNum", "StepCount", "StepInterval", "TargetPeriod", "TargetTriggerTimes", "TargetCanScaling", "TargetMetric", "LastModifyTime"}
	_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, context.TaskInfo.RuleId)
	if !r.IsOk() {
		olog.Error("failed to update rule when update auto task, taskId:[%d], ruleId:[%d], err:[%v]", input.ID, context.TaskInfo.RuleId, r)
		return
	}

	// 更新任务的最后修改时间
	r = updateTaskModifyTime(input.ID, "auto")
	if !r.IsOk() {
		return
	}

	result := AutoTaskUpdateResult{
		TaskId: input.ID,
	}

	olog.Info("auto task updated successfully, taskId:[%d], input_data:[%s]", input.ID, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

// validateAndPrepareTaskUpdate 验证任务并准备更新上下文
func validateAndPrepareTaskUpdate(input TaskUpdateInput, taskTypeName string) (context TaskUpdateContext, r ocommon.ResultInfo) {
	// 验证任务是否存在
	var taskInfo dao.TaskInfo
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, input.GetID())
	if !r.IsOk() {
		olog.Error("task not found when update %s task, taskId:[%d], err:[%v]", taskTypeName, input.GetID(), r)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "task not found", nil, nil)
		return
	}

	if taskInfo.ID <= 0 {
		olog.Error("task id is invalid when update %s task, taskId:[%d]", taskTypeName, input.GetID())
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "task id is invalid", nil, nil)
		return
	}

	// 验证任务类型
	if taskInfo.TaskType != input.GetTaskType() {
		olog.Error("task type is not %s when update %s task, taskId:[%d], taskType:[%s]", input.GetTaskType(), taskTypeName, input.GetID(), taskInfo.TaskType)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("task type is not %s", input.GetTaskType()), nil, nil)
		return
	}

	// 获取模块信息
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, taskInfo.ModuleId)
	if !r.IsOk() {
		olog.Error("failed to get module info when update %s task, taskId:[%d], err:[%v]", taskTypeName, input.GetID(), r)
		return
	}

	// 获取实例数量
	tagInsList, r := comm.GetOnlineIns(moduleInfo.ServiceName, taskInfo.TaskModuleType, taskInfo.IdcTag)
	if !r.IsOk() {
		olog.Error("failed to get instance count when update %s task, taskId:[%d], err:[%v]", taskTypeName, input.GetID(), r)
		return
	}

	if len(tagInsList) <= 0 {
		olog.Error("failed to get instance count when update %s task, taskId:[%d]", taskTypeName, input.GetID())
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "failed to get instance count", nil, nil)
		return
	}

	context = TaskUpdateContext{
		TaskInfo:   taskInfo,
		ModuleInfo: moduleInfo,
		TagInsInfo: tagInsList[0],
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// updateTaskModifyTime 更新任务的最后修改时间
func updateTaskModifyTime(taskId int, taskTypeName string) (r ocommon.ResultInfo) {
	updateTaskData := &dao.TaskInfo{
		LastModifyTime: time.Now(),
	}
	updateTaskFields := []string{"LastModifyTime"}
	_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateTaskData, updateTaskFields, taskId)
	if !r.IsOk() {
		olog.Error("failed to update task modify time when update %s task, taskId:[%d], err:[%v]", taskTypeName, taskId, r)
		return
	}
	return
}

// validateManualTaskUpdateParams 验证手动任务更新参数
func validateManualTaskUpdateParams(input ManualTaskUpdateInput) (r ocommon.ResultInfo) {
	if input.ID <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "id is invalid", nil, nil)
		return
	}

	if input.ManualAction != base.ACTION_UP && input.ManualAction != base.ACTION_DOWN {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "manualAction must be 'up' or 'down'", nil, nil)
		return
	}

	if input.AdjustNum <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "adjustNum must be greater than 0", nil, nil)
		return
	}

	if input.CooldownTime < 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "cooldownTime must be greater than or equal to 0", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateCrontabTaskUpdateParams 验证定时任务更新参数
func validateCrontabTaskUpdateParams(input CrontabTaskUpdateInput) (r ocommon.ResultInfo) {
	if input.ID <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "id is invalid", nil, nil)
		return
	}

	if input.AdjustNum <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "adjustNum must be greater than 0", nil, nil)
		return
	}

	// 验证定时任务模式
	validSchedTypes := []string{base.TASK_CRONTAB_TYPE_CRON2, base.TASK_CRONTAB_TYPE_EVENT_CRON, base.TASK_CRONTAB_TYPE_CRON_EVENT}
	if !contains(validSchedTypes, input.SchedType) {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "schedType is invalid", nil, nil)
		return
	}

	// 验证保护类型
	if input.CronProtectType != "" {
		validProtectTypes := []string{base.CRON_PROTECT_TYPE_SUGGEST, base.CRON_PROTECT_TYPE_PLAN, base.CRON_PROTECT_TYPE_NONE}
		if !contains(validProtectTypes, input.CronProtectType) {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "cronProtectType is invalid", nil, nil)
			return
		}
	}

	if input.StepCount <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "stepCount must be greater than 0", nil, nil)
		return
	}

	if input.StepInterval < 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "stepInterval must be greater than or equal to 0", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// updateCrontabTaskRules 更新定时任务规则
func updateCrontabTaskRules(input CrontabTaskUpdateInput, taskInfo dao.TaskInfo, tagInsInfo comm.TagIns) (r ocommon.ResultInfo) {
	// 根据schedType更新不同的规则
	switch input.SchedType {
	case base.TASK_CRONTAB_TYPE_CRON2:
		// cronCron: 定时缩容_定时扩容 - 两个都是时间触发规则
		r = updateTimeTriggeredRule(input, taskInfo.SchedScaleOutRule, input.ScaleOutRule, tagInsInfo)
		if !r.IsOk() {
			return
		}
		r = updateTimeTriggeredRule(input, taskInfo.SchedScaleInRule, input.ScaleInRule, tagInsInfo)
		if !r.IsOk() {
			return
		}

	case base.TASK_CRONTAB_TYPE_EVENT_CRON:
		// eventCron: 事件缩容_定时扩容 - 扩容是时间触发，缩容是事件触发
		r = updateTimeTriggeredRule(input, taskInfo.SchedScaleOutRule, input.ScaleOutRule, tagInsInfo)
		if !r.IsOk() {
			return
		}
		r = updateEventTriggeredRule(input, taskInfo.SchedScaleInRule, tagInsInfo)
		if !r.IsOk() {
			return
		}

	case base.TASK_CRONTAB_TYPE_CRON_EVENT:
		// cronEvent: 定时缩容_事件扩容 - 缩容是时间触发，扩容是事件触发
		r = updateEventTriggeredRule(input, taskInfo.SchedScaleOutRule, tagInsInfo)
		if !r.IsOk() {
			return
		}
		r = updateTimeTriggeredRule(input, taskInfo.SchedScaleInRule, input.ScaleInRule, tagInsInfo)
		if !r.IsOk() {
			return
		}

	default:
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "invalid schedType", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// updateTimeTriggeredRule 更新时间触发规则
func updateTimeTriggeredRule(input CrontabTaskUpdateInput, ruleId int, ruleInfo CrontabRuleInfo, tagInsInfo comm.TagIns) (r ocommon.ResultInfo) {
	updateRuleData := &dao.RuleOnline{
		ScalingRuleType: base.SCALING_RULE_TIME_TRIGGER,
		CurrentNum:      tagInsInfo.TotalNum,
		AvailNum:        tagInsInfo.AvailNum,
		AdjustNum:       input.AdjustNum,
		StepCount:       input.StepCount,
		StepInterval:    input.StepInterval,
		CronPeriod:      ruleInfo.CronPeriod,
		CronDay:         ruleInfo.CronDay,
		CronTime:        ruleInfo.CronTime,
		CronProtectType: input.CronProtectType,
		LastModifyTime:  time.Now(),
	}

	updateRuleFields := []string{"ScalingRuleType", "CurrentNum", "AvailNum", "AdjustNum", "StepCount", "StepInterval", "CronPeriod", "CronDay", "CronTime", "CronProtectType", "LastModifyTime"}
	_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, ruleId)
	if !r.IsOk() {
		olog.Error("failed to update time triggered rule, ruleId:[%d], err:[%v]", ruleId, r)
		return
	}

	olog.Info("time triggered rule updated successfully, ruleId:[%d]", ruleId)
	return
}

// updateEventTriggeredRule 更新事件触发规则
func updateEventTriggeredRule(input CrontabTaskUpdateInput, ruleId int, tagInsInfo comm.TagIns) (r ocommon.ResultInfo) {
	updateRuleData := &dao.RuleOnline{
		ScalingRuleType: base.SCALING_RULE_EVENT_TRIGGER,
		CurrentNum:      tagInsInfo.TotalNum,
		AvailNum:        tagInsInfo.AvailNum,
		AdjustNum:       input.AdjustNum,
		StepCount:       input.StepCount,
		StepInterval:    input.StepInterval,
		CronProtectType: input.CronProtectType,
		LastModifyTime:  time.Now(),
	}

	updateRuleFields := []string{"ScalingRuleType", "CurrentNum", "AvailNum", "AdjustNum", "StepCount", "StepInterval", "CronProtectType", "LastModifyTime"}
	_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, ruleId)
	if !r.IsOk() {
		olog.Error("failed to update event triggered rule, ruleId:[%d], err:[%v]", ruleId, r)
		return
	}

	olog.Info("event triggered rule updated successfully, ruleId:[%d]", ruleId)
	return
}

// validateAutoTaskUpdateParams 验证自动化任务更新参数
func validateAutoTaskUpdateParams(input AutoTaskUpdateInput) (r ocommon.ResultInfo) {
	if input.ID <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "id is invalid", nil, nil)
		return
	}

	if input.StepCount <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "stepCount must be greater than 0", nil, nil)
		return
	}

	if input.StepInterval < 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "stepInterval must be greater than or equal to 0", nil, nil)
		return
	}

	if input.TargetPeriod <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetPeriod must be greater than 0", nil, nil)
		return
	}

	if input.TargetTriggerTimes <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetTriggerTimes must be greater than 0", nil, nil)
		return
	}

	if len(input.TargetMetric) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetMetric is required", nil, nil)
		return
	}

	// 验证指标信息
	for i, metric := range input.TargetMetric {
		if metric.MetricName == "" {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("metricName is empty at index %d", i), nil, nil)
			return
		}

		// 验证指标名称是否有效
		validMetrics := []string{
			base.METRIC_NAME_CPU_PERCENT,
			base.METRIC_NAME_APP_CPU_PERCENT,
			base.METRIC_NAME_AVERAGE_COST,
			base.METRIC_NAME_SINGLE_INSTANCE_QPS,
			base.METRIC_NAME_WATER_LEVEL,
			base.METRIC_NAME_HTTP_CODE_NUM,
		}
		if !contains(validMetrics, metric.MetricName) {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("metricName '%s' is invalid", metric.MetricName), nil, nil)
			return
		}

		if metric.TargetValue <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetValue must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if metric.TargetRateMax <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetRateMax must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if metric.TargetRateMin <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetRateMin must be greater than 0 at index %d", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// UpdateTaskStatus 更新任务状态
func UpdateTaskStatus(input TaskStatusUpdateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateTaskStatusUpdateParams(input)
	if !r.IsOk() {
		olog.Error("task status update params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	var updatedCount int
	var failedTasks []int

	// 批量更新任务状态
	for _, taskId := range input.IDs {
		// 验证任务是否存在
		var taskInfo dao.TaskInfo
		r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, taskId)
		if !r.IsOk() {
			olog.Error("task not found when update task status, taskId:[%d], err:[%v]", taskId, r)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		// 验证任务类型是否支持状态更新（只有定时任务和自动化任务支持）
		if taskInfo.TaskType != base.TASK_TYPE_CRONTAB && taskInfo.TaskType != base.TASK_TYPE_AUTO_TASK {
			olog.Error("task type does not support status update, taskId:[%d], taskType:[%s]", taskId, taskInfo.TaskType)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		// 更新任务状态
		updateTaskData := &dao.TaskInfo{
			TaskStatus:     input.Status,
			LastModifyTime: time.Now(),
		}
		updateTaskFields := []string{"TaskStatus", "LastModifyTime"}
		_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateTaskData, updateTaskFields, taskId)
		if !r.IsOk() {
			olog.Error("failed to update task status, taskId:[%d], status:[%s], err:[%v]", taskId, input.Status, r)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		updatedCount++
		olog.Info("task status updated successfully, taskId:[%d], status:[%s]", taskId, input.Status)
	}

	// 如果有失败的任务，记录日志但不返回错误
	if len(failedTasks) > 0 {
		olog.Warn("some tasks failed to update status, failedTasks:[%v], input_data:[%s]", failedTasks, string(inputDebug))
	}

	result := TaskStatusUpdateResult{
		UpdatedCount: updatedCount,
	}

	olog.Info("task status batch update completed, updatedCount:[%d], totalCount:[%d], input_data:[%s]",
		updatedCount, len(input.IDs), string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

// validateTaskStatusUpdateParams 验证任务状态更新参数
func validateTaskStatusUpdateParams(input TaskStatusUpdateInput) (r ocommon.ResultInfo) {
	if len(input.IDs) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "ids is empty", nil, nil)
		return
	}

	// 验证任务ID的有效性
	for i, id := range input.IDs {
		if id <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("invalid task id at index %d", i), nil, nil)
			return
		}
	}

	// 验证状态值
	validStatuses := []string{
		base.TASK_STATUS_ENABLE,
		base.TASK_STATUS_TEST_RUN,
		base.TASK_STATUS_OFFLINE,
	}
	if !contains(validStatuses, input.Status) {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "status is invalid", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// DeleteTask 删除任务
func DeleteTask(input TaskDeleteInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateTaskDeleteParams(input)
	if !r.IsOk() {
		olog.Error("task delete params validation failed, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	var deletedCount int
	var failedTasks []int

	// 批量删除任务
	for _, taskId := range input.IDs {
		// 验证任务是否存在
		var taskInfo dao.TaskInfo
		r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, taskId)
		if !r.IsOk() {
			olog.Error("task not found when delete task, taskId:[%d], err:[%v]", taskId, r)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		// 验证任务类型是否支持删除（只有定时任务和自动化任务支持删除）
		if taskInfo.TaskType != base.TASK_TYPE_CRONTAB && taskInfo.TaskType != base.TASK_TYPE_AUTO_TASK {
			olog.Error("task type does not support delete, taskId:[%d], taskType:[%s]", taskId, taskInfo.TaskType)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		// 软删除任务（设置 Dflag = 1）
		updateTaskData := &dao.TaskInfo{
			Dflag:          1,
			LastModifyTime: time.Now(),
		}
		updateTaskFields := []string{"Dflag", "LastModifyTime"}
		_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateTaskData, updateTaskFields, taskId)
		if !r.IsOk() {
			olog.Error("failed to delete task, taskId:[%d], err:[%v]", taskId, r)
			failedTasks = append(failedTasks, taskId)
			continue
		}

		// 如果任务有关联的规则，也需要软删除规则
		if taskInfo.RuleId > 0 {
			updateRuleData := &dao.RuleOnline{
				Dflag:          1,
				LastModifyTime: time.Now(),
			}
			updateRuleFields := []string{"Dflag", "LastModifyTime"}
			_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateRuleData, updateRuleFields, taskInfo.RuleId)
			if !r.IsOk() {
				olog.Error("failed to delete rule when delete task, taskId:[%d], ruleId:[%d], err:[%v]", taskId, taskInfo.RuleId, r)
				// 规则删除失败不影响任务删除，只记录日志
			}
		}

		// 如果是定时任务，还需要删除关联的扩容和缩容规则
		if taskInfo.TaskType == base.TASK_TYPE_CRONTAB {
			if taskInfo.SchedScaleOutRule > 0 {
				updateScaleOutRuleData := &dao.RuleOnline{
					Dflag:          1,
					LastModifyTime: time.Now(),
				}
				updateScaleOutRuleFields := []string{"Dflag", "LastModifyTime"}
				_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateScaleOutRuleData, updateScaleOutRuleFields, taskInfo.SchedScaleOutRule)
				if !r.IsOk() {
					olog.Error("failed to delete scale out rule when delete crontab task, taskId:[%d], scaleOutRuleId:[%d], err:[%v]", taskId, taskInfo.SchedScaleOutRule, r)
				}
			}

			if taskInfo.SchedScaleInRule > 0 {
				updateScaleInRuleData := &dao.RuleOnline{
					Dflag:          1,
					LastModifyTime: time.Now(),
				}
				updateScaleInRuleFields := []string{"Dflag", "LastModifyTime"}
				_, r = dao.CreateRuleOnlinePtr().UpdateByPk(updateScaleInRuleData, updateScaleInRuleFields, taskInfo.SchedScaleInRule)
				if !r.IsOk() {
					olog.Error("failed to delete scale in rule when delete crontab task, taskId:[%d], scaleInRuleId:[%d], err:[%v]", taskId, taskInfo.SchedScaleInRule, r)
				}
			}
		}

		deletedCount++
		olog.Info("task deleted successfully, taskId:[%d], taskType:[%s]", taskId, taskInfo.TaskType)
	}

	// 如果有失败的任务，记录日志但不返回错误
	if len(failedTasks) > 0 {
		olog.Warn("some tasks failed to delete, failedTasks:[%v], input_data:[%s]", failedTasks, string(inputDebug))
	}

	result := TaskDeleteResult{
		DeletedCount: deletedCount,
	}

	olog.Info("task batch delete completed, deletedCount:[%d], totalCount:[%d], input_data:[%s]",
		deletedCount, len(input.IDs), string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

// validateTaskDeleteParams 验证任务删除参数
func validateTaskDeleteParams(input TaskDeleteInput) (r ocommon.ResultInfo) {
	if len(input.IDs) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "ids is empty", nil, nil)
		return
	}

	// 验证任务ID的有效性
	for i, id := range input.IDs {
		if id <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("invalid task id at index %d", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// updateTaskStatus 更新任务状态
func updateTaskStatus(taskId int, taskTypeName string, status string) (r ocommon.ResultInfo) {
	updateTaskData := &dao.TaskInfo{
		TaskStatus:     status,
		LastModifyTime: time.Now(),
	}
	updateTaskFields := []string{"TaskStatus", "LastModifyTime"}
	_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateTaskData, updateTaskFields, taskId)
	if !r.IsOk() {
		olog.Error("failed to update task status when update %s task, taskId:[%d], status:[%s], err:[%v]", taskTypeName, taskId, status, r)
		return
	}
	olog.Info("task status updated successfully, taskId:[%d], status:[%s]", taskId, status)
	return
}
