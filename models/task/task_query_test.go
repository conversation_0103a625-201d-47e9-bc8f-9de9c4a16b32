package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 为了不修改dao包，我们创建一个测试专用的函数包装器
type testTaskQueryService struct {
	taskInfoMockData   map[string]interface{}
	engineInfoMockData map[string]interface{}
	ruleOnlineMockData map[string]interface{}
	shouldReturnError  bool
	errorCode          int
	errorMessage       string
}

// 创建测试服务实例
func newTestTaskQueryService() *testTaskQueryService {
	return &testTaskQueryService{
		taskInfoMockData:   make(map[string]interface{}),
		engineInfoMockData: make(map[string]interface{}),
		ruleOnlineMockData: make(map[string]interface{}),
	}
}

// 设置TaskInfo模拟数据
func (t *testTaskQueryService) setTaskInfoMockData(count int64, tasks []dao.TaskInfo) {
	t.taskInfoMockData["count"] = count
	t.taskInfoMockData["tasks"] = tasks
}

// 设置EngineInfo模拟数据
func (t *testTaskQueryService) setEngineInfoMockData(count int64, engines []dao.EngineInfo) {
	t.engineInfoMockData["count"] = count
	t.engineInfoMockData["engines"] = engines
}

// 设置RuleOnline模拟数据
func (t *testTaskQueryService) setRuleOnlineMockData(rule dao.RuleOnline) {
	t.ruleOnlineMockData["rule"] = rule
}

// 设置错误返回
func (t *testTaskQueryService) setError(code int, message string) {
	t.shouldReturnError = true
	t.errorCode = code
	t.errorMessage = message
}

// 模拟GetTaskList函数，使用测试数据
func (t *testTaskQueryService) mockGetTaskList(input TaskQuery) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	// 设置默认分页参数
	if input.PageNum <= 0 {
		input.PageNum = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}

	count := t.taskInfoMockData["count"].(int64)
	tasks := t.taskInfoMockData["tasks"].([]dao.TaskInfo)

	var taskList []TaskInfoList
	for _, task := range tasks {
		var jobStatus, taskStatus string
		if input.Type == base.TASK_TYPE_MANUAL {
			jobStatus = task.TaskStatus
		} else {
			taskStatus = task.TaskStatus
		}

		taskList = append(taskList, TaskInfoList{
			ID:         task.ID,
			TaskName:   task.TaskName,
			TaskType:   task.TaskType,
			ModuleName: task.ModuleName,
			IdcTag:     task.IdcTag,
			SchedMode:  task.SchedMode,
			JobStatus:  jobStatus,
			TaskStatus: taskStatus,
		})
	}

	taskListRes := TaskListRes{
		PageInfo: PageInfo{
			PageNum:  input.PageNum,
			PageSize: input.PageSize,
			Total:    int(count),
		},
		TaskDetail: taskList,
	}

	return ocommon.GenResultInfo(0, "success", taskListRes, nil)
}

// 模拟GetTaskHistory函数
func (t *testTaskQueryService) mockGetTaskHistory(input TaskQuery) (r ocommon.ResultInfo) {
	if input.TaskId == 0 {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get manual task history", nil, nil)
	}

	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	// 设置默认分页参数
	if input.PageNum <= 0 {
		input.PageNum = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}

	count := t.engineInfoMockData["count"].(int64)
	engines := t.engineInfoMockData["engines"].([]dao.EngineInfo)

	var taskHistoryList []TaskHistoryList
	for _, engineInfo := range engines {
		taskHistoryList = append(taskHistoryList, TaskHistoryList{
			ID:               engineInfo.ID,
			CreateTime:       engineInfo.CreateTime,
			FinishTime:       engineInfo.FinishTime,
			IdcTag:           engineInfo.IdcTag,
			Step:             "1/1",
			CurrentNum:       engineInfo.CurrentNum,
			ExpectedTotalNum: engineInfo.ExpectedNum,
			ActualNum:        engineInfo.ActualNum,
			ActionReason:     engineInfo.ActionReason,
			Status:           engineInfo.Status,
		})
	}

	taskHistoryRes := TaskHistoryRes{
		PageInfo: PageInfo{
			PageNum:  input.PageNum,
			PageSize: input.PageSize,
			Total:    int(count),
		},
		TaskHistory: taskHistoryList,
	}

	return ocommon.GenResultInfo(0, "success", taskHistoryRes, nil)
}

// 模拟GetManualRule函数
func (t *testTaskQueryService) mockGetManualRule(input TaskQuery) (r ocommon.ResultInfo) {
	if input.TaskId == 0 {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get manual task rule", nil, nil)
	}

	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	ruleOnlineInfo := t.ruleOnlineMockData["rule"].(dao.RuleOnline)

	type ManualRule struct {
		ManualAction  string `json:"manualAction"`
		AdjustNum     int    `json:"adjustNum"`
		BlockAutoTask bool   `json:"blockAutoTask"`
		CooldownTime  int    `json:"cooldownTime"`
	}

	rule := ManualRule{
		ManualAction:  ruleOnlineInfo.ManualAction,
		AdjustNum:     ruleOnlineInfo.AdjustNum,
		BlockAutoTask: ruleOnlineInfo.BlockAutoTask,
		CooldownTime:  ruleOnlineInfo.CooldownTime,
	}

	return ocommon.GenResultInfo(0, "success", rule, nil)
}

func TestGetTaskList(t *testing.T) {
	tests := []struct {
		name           string
		input          TaskQuery
		mockCount      int64
		mockTasks      []dao.TaskInfo
		shouldError    bool
		errorCode      int
		errorMessage   string
		expectedResult TaskListRes
		wantErr        bool
	}{
		{
			name: "valid manual task query",
			input: TaskQuery{
				Type:     base.TASK_TYPE_MANUAL,
				PageNum:  1,
				PageSize: 10,
				Search:   "test",
			},
			mockCount: 1,
			mockTasks: []dao.TaskInfo{
				{
					ID:         1,
					TaskName:   "test-task",
					TaskType:   base.TASK_TYPE_MANUAL,
					ModuleName: "test-module",
					IdcTag:     "hba",
					SchedMode:  "scale",
					TaskStatus: "enable",
				},
			},
			shouldError: false,
			expectedResult: TaskListRes{
				PageInfo: PageInfo{
					PageNum:  1,
					PageSize: 10,
					Total:    1,
				},
				TaskDetail: []TaskInfoList{
					{
						ID:         1,
						TaskName:   "test-task",
						TaskType:   base.TASK_TYPE_MANUAL,
						ModuleName: "test-module",
						IdcTag:     "hba",
						SchedMode:  "scale",
						JobStatus:  "enable",
						TaskStatus: "",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "database error",
			input: TaskQuery{
				Type:     base.TASK_TYPE_MANUAL,
				PageNum:  1,
				PageSize: 10,
			},
			mockCount:    0,
			mockTasks:    []dao.TaskInfo{},
			shouldError:  true,
			errorCode:    errno.ERR_INVALID_RESULT,
			errorMessage: "database error",
			wantErr:      true,
		},
		{
			name: "default page parameters",
			input: TaskQuery{
				Type:     base.TASK_TYPE_CRONTAB,
				PageNum:  0, // 应该被设置为1
				PageSize: 0, // 应该被设置为10
			},
			mockCount:   0,
			mockTasks:   []dao.TaskInfo{},
			shouldError: false,
			expectedResult: TaskListRes{
				PageInfo: PageInfo{
					PageNum:  1,
					PageSize: 10,
					Total:    0,
				},
				TaskDetail: []TaskInfoList{},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务
			testService := newTestTaskQueryService()
			testService.setTaskInfoMockData(tt.mockCount, tt.mockTasks)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			// 执行测试
			result := testService.mockGetTaskList(tt.input)

			// 验证结果
			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					actualResult := result.Data.(TaskListRes)
					assert.Equal(t, tt.expectedResult.PageInfo, actualResult.PageInfo)
					assert.Equal(t, len(tt.expectedResult.TaskDetail), len(actualResult.TaskDetail))
					if len(tt.expectedResult.TaskDetail) > 0 {
						assert.Equal(t, tt.expectedResult.TaskDetail[0], actualResult.TaskDetail[0])
					}
				}
			}
		})
	}
}

func TestGetTaskHistory(t *testing.T) {
	tests := []struct {
		name           string
		input          TaskQuery
		mockCount      int64
		mockEngines    []dao.EngineInfo
		shouldError    bool
		errorCode      int
		errorMessage   string
		expectedResult TaskHistoryRes
		wantErr        bool
	}{
		{
			name: "valid task history query",
			input: TaskQuery{
				TaskId:   1,
				PageNum:  1,
				PageSize: 10,
			},
			mockCount: 1,
			mockEngines: []dao.EngineInfo{
				{
					ID:           1,
					TaskId:       1,
					CreateTime:   time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
					FinishTime:   time.Date(2023, 1, 1, 10, 5, 0, 0, time.UTC),
					IdcTag:       "hba",
					CurrentNum:   5,
					ExpectedNum:  10,
					ActualNum:    10,
					ActionReason: "manual scale",
					Status:       1,
				},
			},
			shouldError: false,
			expectedResult: TaskHistoryRes{
				PageInfo: PageInfo{
					PageNum:  1,
					PageSize: 10,
					Total:    1,
				},
				TaskHistory: []TaskHistoryList{
					{
						ID:               1,
						CreateTime:       time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC),
						FinishTime:       time.Date(2023, 1, 1, 10, 5, 0, 0, time.UTC),
						IdcTag:           "hba",
						Step:             "1/1",
						CurrentNum:       5,
						ExpectedTotalNum: 10,
						ActualNum:        10,
						ActionReason:     "manual scale",
						Status:           1,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "missing taskId",
			input: TaskQuery{
				TaskId:   0,
				PageNum:  1,
				PageSize: 10,
			},
			mockCount:   0,
			mockEngines: []dao.EngineInfo{},
			shouldError: false,
			wantErr:     true,
		},
		{
			name: "database error",
			input: TaskQuery{
				TaskId:   1,
				PageNum:  1,
				PageSize: 10,
			},
			mockCount:    0,
			mockEngines:  []dao.EngineInfo{},
			shouldError:  true,
			errorCode:    errno.ERR_INVALID_RESULT,
			errorMessage: "database error",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务
			testService := newTestTaskQueryService()
			testService.setEngineInfoMockData(tt.mockCount, tt.mockEngines)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			// 执行测试
			result := testService.mockGetTaskHistory(tt.input)

			// 验证结果
			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					actualResult := result.Data.(TaskHistoryRes)
					assert.Equal(t, tt.expectedResult.PageInfo, actualResult.PageInfo)
					assert.Equal(t, len(tt.expectedResult.TaskHistory), len(actualResult.TaskHistory))
					if len(tt.expectedResult.TaskHistory) > 0 {
						assert.Equal(t, tt.expectedResult.TaskHistory[0], actualResult.TaskHistory[0])
					}
				}
			}
		})
	}
}

func TestGetManualRule(t *testing.T) {
	tests := []struct {
		name         string
		input        TaskQuery
		mockRule     dao.RuleOnline
		shouldError  bool
		errorCode    int
		errorMessage string
		expectedRule map[string]interface{}
		wantErr      bool
	}{
		{
			name: "valid manual rule query",
			input: TaskQuery{
				TaskId: 1,
			},
			mockRule: dao.RuleOnline{
				TaskId:        1,
				ManualAction:  "up",
				AdjustNum:     5,
				BlockAutoTask: true,
				CooldownTime:  10,
			},
			shouldError: false,
			expectedRule: map[string]interface{}{
				"manualAction":  "up",
				"adjustNum":     float64(5), // JSON数字会被解析为float64
				"blockAutoTask": true,
				"cooldownTime":  float64(10),
			},
			wantErr: false,
		},
		{
			name: "missing taskId",
			input: TaskQuery{
				TaskId: 0,
			},
			mockRule:    dao.RuleOnline{},
			shouldError: false,
			wantErr:     true,
		},
		{
			name: "database error",
			input: TaskQuery{
				TaskId: 1,
			},
			mockRule:     dao.RuleOnline{},
			shouldError:  true,
			errorCode:    errno.ERR_INVALID_RESULT,
			errorMessage: "database error",
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试服务
			testService := newTestTaskQueryService()
			testService.setRuleOnlineMockData(tt.mockRule)

			if tt.shouldError {
				testService.setError(tt.errorCode, tt.errorMessage)
			}

			// 执行测试
			result := testService.mockGetManualRule(tt.input)

			// 验证结果
			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				if result.IsOk() {
					// 将结果转换为map进行比较
					resultBytes, _ := json.Marshal(result.Data)
					var actualRule map[string]interface{}
					json.Unmarshal(resultBytes, &actualRule)

					for key, expectedValue := range tt.expectedRule {
						assert.Equal(t, expectedValue, actualRule[key], "Field %s mismatch", key)
					}
				}
			}
		})
	}
}

// TestManualTaskCheckInput_Structure 测试手动任务检查输入结构体
func TestManualTaskCheckInput_Structure(t *testing.T) {
	input := ManualTaskCheckInput{
		TaskId: 123,
	}

	// 验证字段值
	assert.Equal(t, 123, input.TaskId)
}

// TestManualTaskCheckInstanceInfo_Structure 测试实例信息结构体
func TestManualTaskCheckInstanceInfo_Structure(t *testing.T) {
	instanceInfo := ManualTaskCheckInstanceInfo{
		TotalNumBefore: 10,
		AvailNumBefore: 8,
		TotalNumAfter:  12,
		AvailNumAfter:  10,
	}

	// 验证字段值
	assert.Equal(t, 10, instanceInfo.TotalNumBefore)
	assert.Equal(t, 8, instanceInfo.AvailNumBefore)
	assert.Equal(t, 12, instanceInfo.TotalNumAfter)
	assert.Equal(t, 10, instanceInfo.AvailNumAfter)
}

// TestManualTaskCheckResult_Structure 测试手动任务检查结果结构体
func TestManualTaskCheckResult_Structure(t *testing.T) {
	result := ManualTaskCheckResult{
		ID:         1,
		TaskName:   "test_manual_task",
		TaskType:   base.TASK_TYPE_MANUAL,
		ModuleId:   100,
		ModuleName: "test.module",
		IdcTag:     "hba",
		ScaleMode:  "traffic",
		InstanceInfos: ManualTaskCheckInstanceInfo{
			TotalNumBefore: 10,
			AvailNumBefore: 8,
			TotalNumAfter:  12,
			AvailNumAfter:  10,
		},
		WarnInfos: []string{"warning1", "warning2"},
	}

	// 验证字段值
	assert.Equal(t, 1, result.ID)
	assert.Equal(t, "test_manual_task", result.TaskName)
	assert.Equal(t, base.TASK_TYPE_MANUAL, result.TaskType)
	assert.Equal(t, 100, result.ModuleId)
	assert.Equal(t, "test.module", result.ModuleName)
	assert.Equal(t, "hba", result.IdcTag)
	assert.Equal(t, "traffic", result.ScaleMode)
	assert.Equal(t, 10, result.InstanceInfos.TotalNumBefore)
	assert.Equal(t, 8, result.InstanceInfos.AvailNumBefore)
	assert.Equal(t, 12, result.InstanceInfos.TotalNumAfter)
	assert.Equal(t, 10, result.InstanceInfos.AvailNumAfter)
	assert.Equal(t, []string{"warning1", "warning2"}, result.WarnInfos)
	assert.Equal(t, 2, len(result.WarnInfos))
}

// TestManualTaskCheck_ParameterValidation 测试手动任务检查参数验证
func TestManualTaskCheck_ParameterValidation(t *testing.T) {
	tests := []struct {
		name    string
		input   ManualTaskCheckInput
		wantErr bool
	}{
		{
			name: "valid taskId",
			input: ManualTaskCheckInput{
				TaskId: 1,
			},
			wantErr: false,
		},
		{
			name: "valid taskId - large number",
			input: ManualTaskCheckInput{
				TaskId: 999999,
			},
			wantErr: false,
		},
		{
			name: "invalid taskId - zero",
			input: ManualTaskCheckInput{
				TaskId: 0,
			},
			wantErr: true,
		},
		{
			name: "invalid taskId - negative",
			input: ManualTaskCheckInput{
				TaskId: -1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 只测试参数验证部分
			if tt.wantErr {
				assert.True(t, tt.input.TaskId <= 0, "Expected invalid taskId")
			} else {
				assert.True(t, tt.input.TaskId > 0, "Expected valid taskId")
			}
		})
	}
}
