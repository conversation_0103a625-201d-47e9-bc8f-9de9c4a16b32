package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/models/comm"
	"encoding/json"
	"fmt"
	"time"
)

// ManualTaskCreateInput 手动任务创建输入参数
type ManualTaskCreateInput struct {
	TaskCreateInfo
	ManualTaskCreateInfo
}

// CrontabTaskCreateInput 定时任务创建输入参数
type CrontabTaskCreateInput struct {
	TaskCreateInfo
	CrontabTaskCreateInfo
}

// AutoTaskCreateInput 自动化任务创建输入参数
type AutoTaskCreateInput struct {
	TaskCreateInfo
	AutoTaskCreateInfo
}

type TaskCreateInfo struct {
	ModuleId   int    `json:"moduleId"`   // 模块ID
	ModuleName string `json:"moduleName"` // 模块名称
	TaskName   string `json:"taskName"`   // 任务名称
	IdcTag     string `json:"idcTag"`     // 机房标签
	TaskDesc   string `json:"taskDesc"`   // 任务描述
}

type ManualTaskCreateInfo struct {
	ManualAction  string `json:"manualAction"`  // 手动操作动作: up(增加) 或 down(减少)
	AdjustNum     int    `json:"adjustNum"`     // 调整实例数量
	BlockAutoTask bool   `json:"blockAutoTask"` // 是否禁止自动任务
	CooldownTime  int    `json:"cooldownTime"`  // 冷却时间(分钟)
	StepCount     int    `json:"stepCount"`     // 步进次数
	StepInterval  int    `json:"stepInterval"`  // 步进时间间隔(分钟)
}

type CrontabTaskCreateInfo struct {
	TaskType        string          `json:"taskType"`        // 任务类型: crontab
	ScaleMode       string          `json:"scaleMode"`       // 伸缩模式: traffic/scale
	AdjustNum       int             `json:"adjustNum"`       // 调整实例数
	SchedType       string          `json:"schedType"`       // 定时任务模式
	CronProtectType string          `json:"cronProtectType"` // 保护类型
	StepCount       int             `json:"stepCount"`       // 步进次数
	StepInterval    int             `json:"stepInterval"`    // 步进间隔时间(分钟)
	ScaleOutRule    CrontabRuleInfo `json:"scaleOutRule"`    // 扩容规则
	ScaleInRule     CrontabRuleInfo `json:"scaleInRule"`     // 缩容规则
}

// CrontabRuleInfo 定时任务规则信息
type CrontabRuleInfo struct {
	CronPeriod string `json:"cronPeriod"` // 定时任务运行周期: week/day/hour
	CronDay    string `json:"cronDay"`    // 执行日期
	CronTime   string `json:"cronTime"`   // 执行时间: 00:00:00 格式
}

type AutoTaskCreateInfo struct {
	TaskType           string            `json:"taskType"`           // 任务类型: autoTask
	ScaleMode          string            `json:"scaleMode"`          // 伸缩模式: traffic/scale
	StepCount          int               `json:"stepCount"`          // 步进次数
	StepInterval       int               `json:"stepInterval"`       // 步进间隔时间(分钟)
	TargetPeriod       int               `json:"targetPeriod"`       // 周期
	TargetTriggerTimes int               `json:"targetTriggerTimes"` // 触发次数
	TargetCanScaling   bool              `json:"targetCanScaling"`   // 是否可以缩容
	TargetMetric       []base.MetricInfo `json:"targetMetric"`       // 指标列表
}

// ManualTaskCreateResult 手动任务创建结果
type ManualTaskCreateResult struct {
	TaskId int `json:"taskId"` // 创建的任务ID
	RuleId int `json:"ruleId"` // 创建的规则ID
}

// CrontabTaskCreateResult 定时任务创建结果
type CrontabTaskCreateResult struct {
	TaskId         int `json:"taskId"`         // 创建的任务ID
	ScaleOutRuleId int `json:"scaleOutRuleId"` // 扩容规则ID
	ScaleInRuleId  int `json:"scaleInRuleId"`  // 缩容规则ID
}

func CreateManualTask(input ManualTaskCreateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateBasicTaskParams(input.TaskCreateInfo)
	if !r.IsOk() {
		olog.Error("basic params validation failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	r = validateManualTaskParams(input.ManualTaskCreateInfo)
	if !r.IsOk() {
		olog.Error("manual task params validation failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证模块是否存在
	moduleInfo, r := validateModuleExists(input.ModuleId)
	if !r.IsOk() {
		olog.Error("module validation failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 检查任务是否已存在
	r = checkTaskExists(input.ModuleId, base.TASK_TYPE_MANUAL, input.IdcTag)
	if !r.IsOk() {
		olog.Error("task exists check failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 创建任务信息
	taskInfo := dao.TaskInfo{
		ModuleId:       input.ModuleId,
		ModuleName:     input.ModuleName,
		TaskModuleType: moduleInfo.ModuleType,
		TaskName:       input.TaskName,
		TaskType:       base.TASK_TYPE_MANUAL,
		TaskStatus:     base.TASK_MANUAL_STATUS_INIT,
		IdcTag:         input.IdcTag,
		SchedMode:      base.SCALE_MODE_SCALE,
		TaskDesc:       input.TaskDesc,
		LastModifyTime: time.Now(),
	}

	taskId, r := createTaskRecord(taskInfo)
	if !r.IsOk() {
		olog.Error("task creation failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}
	tagInsList, r := comm.GetOnlineIns(moduleInfo.ServiceName, moduleInfo.ModuleType, input.IdcTag)
	if !r.IsOk() {
		olog.Error("instance list fetch failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, 0)
		return
	}
	if len(tagInsList) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "no online instance", nil, nil)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 创建规则信息
	ruleInfo := dao.RuleOnline{
		ModuleId:        input.ModuleId,
		TaskId:          taskId,
		Name:            fmt.Sprintf("%s_manual_rule", moduleInfo.ServiceName),
		ScalingRuleType: base.SCALING_RULE_SIMPLE,
		CurrentNum:      tagInsList[0].TotalNum,
		AvailNum:        tagInsList[0].AvailNum,
		AdjustNum:       input.AdjustNum,
		StepCount:       input.StepCount,
		StepInterval:    input.StepInterval,
		ManualAction:    input.ManualAction,
		BlockAutoTask:   input.BlockAutoTask,
		CooldownTime:    input.CooldownTime,
		LastModifyTime:  time.Now(),
	}

	ruleId, r := createRuleRecord(ruleInfo)
	if !r.IsOk() {
		olog.Error("rule creation failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 关联任务和规则
	r = linkTaskAndRule(taskId, ruleId)
	if !r.IsOk() {
		olog.Error("task rule linking failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, ruleId)
		return
	}

	// 更新模块的TaskIdList
	r = updateModuleTaskIdList(input.ModuleId, taskId)
	if !r.IsOk() {
		olog.Error("module task list update failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, ruleId)
		return
	}

	result := ManualTaskCreateResult{
		TaskId: taskId,
		RuleId: ruleId,
	}

	olog.Info("manual task created successfully, taskId:[%d], ruleId:[%d], input_data:[%s]", taskId, ruleId, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

func CreateCrontabTask(input CrontabTaskCreateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateBasicTaskParams(input.TaskCreateInfo)
	if !r.IsOk() {
		olog.Error("basic params validation failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	r = validateCrontabTaskParams(input.CrontabTaskCreateInfo)
	if !r.IsOk() {
		olog.Error("crontab task params validation failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证模块是否存在
	moduleInfo, r := validateModuleExists(input.ModuleId)
	if !r.IsOk() {
		olog.Error("module validation failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 检查任务是否已存在
	r = checkTaskExists(input.ModuleId, base.TASK_TYPE_CRONTAB, input.IdcTag)
	if !r.IsOk() {
		olog.Error("task exists check failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 创建任务信息
	taskInfo := dao.TaskInfo{
		ModuleId:       input.ModuleId,
		ModuleName:     input.ModuleName,
		TaskModuleType: moduleInfo.ModuleType,
		TaskName:       input.TaskName,
		TaskType:       base.TASK_TYPE_CRONTAB,
		TaskStatus:     base.TASK_STATUS_TEST_RUN,
		IdcTag:         input.IdcTag,
		SchedMode:      input.ScaleMode,
		SchedType:      input.SchedType,
		TaskDesc:       input.TaskDesc,
		LastModifyTime: time.Now(),
	}

	taskId, r := createTaskRecord(taskInfo)
	if !r.IsOk() {
		olog.Error("task creation failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	tagInsList, r := comm.GetOnlineIns(moduleInfo.ServiceName, moduleInfo.ModuleType, input.IdcTag)
	if !r.IsOk() {
		olog.Error("instance list fetch failed when create manual task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, 0)
		return
	}
	if len(tagInsList) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "no online instance", nil, nil)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 根据不同的schedType创建相应的规则信息
	var scaleOutRuleId, scaleInRuleId int
	r = createCrontabRules(input, taskId, moduleInfo, tagInsList[0], &scaleOutRuleId, &scaleInRuleId)
	if !r.IsOk() {
		olog.Error("rule creation failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 关联任务和规则（同时更新扩容和缩容规则ID）
	r = linkTaskAndCrontabRules(taskId, scaleOutRuleId, scaleInRuleId)
	if !r.IsOk() {
		olog.Error("task rule linking failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, scaleOutRuleId)
		if scaleInRuleId > 0 {
			cleanupTaskAndRule(0, scaleInRuleId)
		}
		return
	}

	// 更新模块的TaskIdList
	r = updateModuleTaskIdList(input.ModuleId, taskId)
	if !r.IsOk() {
		olog.Error("module task list update failed when create crontab task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, scaleOutRuleId)
		if scaleInRuleId > 0 {
			cleanupTaskAndRule(0, scaleInRuleId)
		}
		return
	}

	result := CrontabTaskCreateResult{
		TaskId:         taskId,
		ScaleOutRuleId: scaleOutRuleId,
		ScaleInRuleId:  scaleInRuleId,
	}

	olog.Info("crontab task created successfully, taskId:[%d], scaleOutRuleId:[%d], scaleInRuleId:[%d], input_data:[%s]", taskId, scaleOutRuleId, scaleInRuleId, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

func CreateAutoTask(input AutoTaskCreateInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	r = validateBasicTaskParams(input.TaskCreateInfo)
	if !r.IsOk() {
		olog.Error("basic params validation failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	r = validateAutoTaskParams(input.AutoTaskCreateInfo)
	if !r.IsOk() {
		olog.Error("auto task params validation failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 验证模块是否存在
	moduleInfo, r := validateModuleExists(input.ModuleId)
	if !r.IsOk() {
		olog.Error("module validation failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 检查任务是否已存在
	r = checkTaskExists(input.ModuleId, base.TASK_TYPE_AUTO_TASK, input.IdcTag)
	if !r.IsOk() {
		olog.Error("task exists check failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 创建任务信息
	taskInfo := dao.TaskInfo{
		ModuleId:       input.ModuleId,
		ModuleName:     input.ModuleName,
		TaskModuleType: moduleInfo.ModuleType,
		TaskName:       input.TaskName,
		TaskType:       base.TASK_TYPE_AUTO_TASK,
		TaskStatus:     base.TASK_STATUS_TEST_RUN,
		IdcTag:         input.IdcTag,
		SchedMode:      input.ScaleMode,
		TaskDesc:       input.TaskDesc,
		LastModifyTime: time.Now(),
	}

	taskId, r := createTaskRecord(taskInfo)
	if !r.IsOk() {
		olog.Error("task creation failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		return
	}

	// 将指标信息转换为JSON字符串
	targetMetricJson, err := json.Marshal(input.TargetMetric)
	if err != nil {
		olog.Error("failed to marshal target metric when create auto task, input_data:[%s], err:[%v]", string(inputDebug), err)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "failed to process target metric", nil, nil)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 创建规则信息
	ruleInfo := dao.RuleOnline{
		ModuleId:           input.ModuleId,
		TaskId:             taskId,
		Name:               fmt.Sprintf("%s_auto_rule", moduleInfo.ServiceName),
		ScalingRuleType:    base.SCALING_RULE_TARGET_TRACKING,
		StepCount:          input.StepCount,
		StepInterval:       input.StepInterval,
		TargetPeriod:       input.TargetPeriod,
		TargetTriggerTimes: input.TargetTriggerTimes,
		TargetCanScaling:   input.TargetCanScaling,
		TargetMetric:       string(targetMetricJson),
		LastModifyTime:     time.Now(),
	}

	ruleId, r := createRuleRecord(ruleInfo)
	if !r.IsOk() {
		olog.Error("rule creation failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, 0)
		return
	}

	// 关联任务和规则
	r = linkTaskAndRule(taskId, ruleId)
	if !r.IsOk() {
		olog.Error("task rule linking failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, ruleId)
		return
	}

	// 更新模块的TaskIdList
	r = updateModuleTaskIdList(input.ModuleId, taskId)
	if !r.IsOk() {
		olog.Error("module task list update failed when create auto task, input_data:[%s], err:[%v]", string(inputDebug), r)
		cleanupTaskAndRule(taskId, ruleId)
		return
	}

	result := ManualTaskCreateResult{
		TaskId: taskId,
		RuleId: ruleId,
	}

	olog.Info("auto task created successfully, taskId:[%d], ruleId:[%d], input_data:[%s]", taskId, ruleId, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

// updateModuleTaskIdList 更新模块的任务ID列表，追加新任务ID并去重
func updateModuleTaskIdList(moduleId, newTaskId int) (r ocommon.ResultInfo) {
	// 获取当前模块信息
	var moduleInfo dao.Module
	r = dao.CreateModulePtr().SearchByColumn(&moduleInfo, &dao.Module{ID: moduleId}, []string{"ID"})
	if !r.IsOk() {
		olog.Error("failed to get module info when update task id list, moduleId:[%d], err:[%v]", moduleId, r)
		return
	}

	// 解析现有的TaskIdList
	taskIdList := parseTaskIdList(moduleInfo.TaskIdList)

	// 添加新的任务ID并去重
	taskIdList = appendAndDeduplicateTaskId(taskIdList, newTaskId)

	// 转换回字符串格式
	newTaskIdListStr := formatTaskIdList(taskIdList)

	// 更新模块的TaskIdList
	_, r = dao.CreateModulePtr().UpdateByPk(&dao.Module{TaskIdList: newTaskIdListStr}, []string{"TaskIdList"}, moduleId)
	if !r.IsOk() {
		olog.Error("failed to update module task id list, moduleId:[%d], newTaskIdList:[%s], err:[%v]", moduleId, newTaskIdListStr, r)
		return
	}

	olog.Info("module task id list updated successfully, moduleId:[%d], newTaskIdList:[%s]", moduleId, newTaskIdListStr)
	return
}

// parseTaskIdList 解析JSON格式的任务ID字符串为[]int
func parseTaskIdList(taskIdListStr string) []int {
	var taskIdList []int

	if taskIdListStr == "" {
		return taskIdList
	}

	// 从JSON字符串反序列化为[]int
	err := json.Unmarshal([]byte(taskIdListStr), &taskIdList)
	if err != nil {
		olog.Error("failed to unmarshal task id list, taskIdListStr:[%s], err:[%v]", taskIdListStr, err)
		return []int{} // 返回空切片
	}

	return taskIdList
}

// appendAndDeduplicateTaskId 添加新任务ID并去重
func appendAndDeduplicateTaskId(taskIdList []int, newTaskId int) []int {
	// 检查是否已存在
	for _, existingId := range taskIdList {
		if existingId == newTaskId {
			return taskIdList // 已存在，直接返回原列表
		}
	}

	// 不存在，添加到列表末尾
	return append(taskIdList, newTaskId)
}

// formatTaskIdList 将[]int格式的任务ID列表转换为JSON字符串
func formatTaskIdList(taskIdList []int) string {
	if len(taskIdList) == 0 {
		return "[]" // 返回空数组的JSON格式
	}

	// 序列化为JSON字符串
	jsonBytes, err := json.Marshal(taskIdList)
	if err != nil {
		olog.Error("failed to marshal task id list, taskIdList:[%v], err:[%v]", taskIdList, err)
		return "[]" // 出错时返回空数组
	}

	return string(jsonBytes)
}

// contains 检查字符串切片中是否包含指定字符串
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// validateBasicTaskParams 验证任务基础参数
func validateBasicTaskParams(input TaskCreateInfo) (r ocommon.ResultInfo) {
	if input.ModuleId <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "moduleId is invalid", nil, nil)
		return
	}

	if input.TaskName == "" {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskName is empty", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateManualTaskParams 验证手动任务特定参数
func validateManualTaskParams(input ManualTaskCreateInfo) (r ocommon.ResultInfo) {
	if input.ManualAction != base.ACTION_UP && input.ManualAction != base.ACTION_DOWN {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "manualAction must be 'up' or 'down'", nil, nil)
		return
	}

	if input.AdjustNum <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "adjustNum must be greater than 0", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateCrontabTaskParams 验证定时任务特定参数
func validateCrontabTaskParams(input CrontabTaskCreateInfo) (r ocommon.ResultInfo) {
	if input.TaskType != base.TASK_TYPE_CRONTAB {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskType must be 'crontab'", nil, nil)
		return
	}

	if input.ScaleMode != base.SCALE_MODE_SCALE && input.ScaleMode != base.SCALE_MODE_TRAFFIC {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "scaleMode must be 'scale' or 'traffic'", nil, nil)
		return
	}

	if input.AdjustNum <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "adjustNum must be greater than 0", nil, nil)
		return
	}

	// 验证定时任务模式
	validSchedTypes := []string{base.TASK_CRONTAB_TYPE_CRON2, base.TASK_CRONTAB_TYPE_EVENT_CRON, base.TASK_CRONTAB_TYPE_CRON_EVENT}
	if !contains(validSchedTypes, input.SchedType) {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "schedType is invalid", nil, nil)
		return
	}

	// 验证保护类型
	if input.CronProtectType != "" {
		validProtectTypes := []string{base.CRON_PROTECT_TYPE_SUGGEST, base.CRON_PROTECT_TYPE_PLAN, base.CRON_PROTECT_TYPE_NONE}
		if !contains(validProtectTypes, input.CronProtectType) {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "cronProtectType is invalid", nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateAutoTaskParams 验证自动化任务特定参数
func validateAutoTaskParams(input AutoTaskCreateInfo) (r ocommon.ResultInfo) {
	if input.TaskType != base.TASK_TYPE_AUTO_TASK {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskType must be 'autoTask'", nil, nil)
		return
	}

	if input.ScaleMode != base.SCALE_MODE_SCALE && input.ScaleMode != base.SCALE_MODE_TRAFFIC {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "scaleMode must be 'scale' or 'traffic'", nil, nil)
		return
	}

	if input.TargetPeriod <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetPeriod must be greater than 0", nil, nil)
		return
	}

	if input.TargetTriggerTimes <= 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetTriggerTimes must be greater than 0", nil, nil)
		return
	}

	if len(input.TargetMetric) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "targetMetric is required", nil, nil)
		return
	}

	// 验证指标信息
	for i, metric := range input.TargetMetric {
		if metric.MetricName == "" {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("metricName is empty at index %d", i), nil, nil)
			return
		}

		// 验证指标名称是否有效
		validMetrics := []string{
			base.METRIC_NAME_CPU_PERCENT,
			base.METRIC_NAME_APP_CPU_PERCENT,
			base.METRIC_NAME_AVERAGE_COST,
			base.METRIC_NAME_SINGLE_INSTANCE_QPS,
			base.METRIC_NAME_WATER_LEVEL,
			base.METRIC_NAME_HTTP_CODE_NUM,
		}
		if !contains(validMetrics, metric.MetricName) {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("metricName '%s' is invalid", metric.MetricName), nil, nil)
			return
		}

		if metric.TargetValue <= 0 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetValue must be greater than 0 at index %d", i), nil, nil)
			return
		}

		if metric.TargetRateMax <= 0 || metric.TargetRateMax >= 1 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetRateMax must be between 0 and 1 at index %d", i), nil, nil)
			return
		}

		if metric.TargetRateMin <= 0 || metric.TargetRateMin >= 1 {
			r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("targetRateMin must be between 0 and 1 at index %d", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateModuleExists 验证模块是否存在
func validateModuleExists(moduleId int) (moduleInfo dao.Module, r ocommon.ResultInfo) {
	r = dao.CreateModulePtr().SearchByColumn(&moduleInfo, &dao.Module{ID: moduleId}, []string{"ID"})
	if !r.IsOk() {
		olog.Error("failed to get module info from database, moduleId:[%d], err:[%v]", moduleId, r)
		return
	}

	if moduleInfo.ID <= 0 {
		olog.Error("module does not exist, moduleId:[%d]", moduleId)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module does not exist", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// checkTaskExists 检查任务是否已存在
func checkTaskExists(moduleId int, taskType, idcTag string) (r ocommon.ResultInfo) {
	var existingTasks []dao.TaskInfo
	r = dao.CreateTaskInfoPtr().SearchByColumn(&existingTasks, &dao.TaskInfo{
		ModuleId: moduleId,
		TaskType: taskType,
		IdcTag:   idcTag,
		Dflag:    0,
	}, []string{"ModuleId", "TaskType", "IdcTag", "Dflag"})
	if !r.IsOk() {
		olog.Error("failed to check existing task from database, moduleId:[%d], taskType:[%s], idcTag:[%s], err:[%v]", moduleId, taskType, idcTag, r)
		return
	}

	if len(existingTasks) > 0 {
		olog.Warn("task already exists for this module and idc, moduleId:[%d], taskType:[%s], idcTag:[%s]", moduleId, taskType, idcTag)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, fmt.Sprintf("%s task already exists for this module and idc", taskType), nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// createTaskRecord 创建任务记录的通用函数
func createTaskRecord(taskInfo dao.TaskInfo) (taskId int, r ocommon.ResultInfo) {
	taskId, r = dao.CreateTaskInfoPtr().Insert(&taskInfo)
	if !r.IsOk() {
		olog.Error("failed to insert task info to database, taskInfo:[%+v], err:[%v]", taskInfo, r)
		return
	}
	return
}

// createRuleRecord 创建规则记录的通用函数
func createRuleRecord(ruleInfo dao.RuleOnline) (ruleId int, r ocommon.ResultInfo) {
	ruleId, r = dao.CreateRuleOnlinePtr().Insert(&ruleInfo)
	if !r.IsOk() {
		olog.Error("failed to insert rule info to database, ruleInfo:[%+v], err:[%v]", ruleInfo, r)
		return
	}
	return
}

// linkTaskAndRule 关联任务和规则
func linkTaskAndRule(taskId, ruleId int) (r ocommon.ResultInfo) {
	_, r = dao.CreateTaskInfoPtr().UpdateByPk(&dao.TaskInfo{RuleId: ruleId}, []string{"RuleId"}, taskId)
	if !r.IsOk() {
		olog.Error("failed to update task rule id, taskId:[%d], ruleId:[%d], err:[%v]", taskId, ruleId, r)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// linkTaskAndCrontabRules 关联任务和定时任务的扩容缩容规则
func linkTaskAndCrontabRules(taskId, scaleOutRuleId, scaleInRuleId int) (r ocommon.ResultInfo) {
	updateFields := []string{"RuleId", "SchedScaleOutRule", "SchedScaleInRule"}
	updateData := &dao.TaskInfo{
		RuleId:            scaleOutRuleId, // 主规则ID设置为扩容规则ID
		SchedScaleOutRule: scaleOutRuleId,
		SchedScaleInRule:  scaleInRuleId,
	}

	_, r = dao.CreateTaskInfoPtr().UpdateByPk(updateData, updateFields, taskId)
	if !r.IsOk() {
		olog.Error("failed to update task crontab rule ids, taskId:[%d], scaleOutRuleId:[%d], scaleInRuleId:[%d], err:[%v]",
			taskId, scaleOutRuleId, scaleInRuleId, r)
		return
	}

	olog.Info("task crontab rules linked successfully, taskId:[%d], scaleOutRuleId:[%d], scaleInRuleId:[%d]",
		taskId, scaleOutRuleId, scaleInRuleId)
	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// cleanupTaskAndRule 清理已创建的任务和规则
func cleanupTaskAndRule(taskId, ruleId int) {
	if taskId > 0 {
		dao.CreateTaskInfoPtr().DeleteByPk(taskId)
	}
	if ruleId > 0 {
		dao.CreateRuleOnlinePtr().DeleteByPk(ruleId)
	}
}

// createCrontabRules 根据不同的schedType创建相应的规则信息
func createCrontabRules(input CrontabTaskCreateInput, taskId int, moduleInfo dao.Module, tagInsInfo comm.TagIns, scaleOutRuleId, scaleInRuleId *int) (r ocommon.ResultInfo) {
	switch input.SchedType {
	case base.TASK_CRONTAB_TYPE_CRON2:
		// cronCron: 定时缩容_定时扩容 - 两个都是时间触发规则
		*scaleOutRuleId, r = createTimeTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleOutRule, "scaleout")
		if !r.IsOk() {
			return
		}
		*scaleInRuleId, r = createTimeTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleInRule, "scalein")
		if !r.IsOk() {
			return
		}

	case base.TASK_CRONTAB_TYPE_EVENT_CRON:
		// eventCron: 事件缩容_定时扩容 - 扩容是时间触发，缩容是事件触发
		*scaleOutRuleId, r = createTimeTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleOutRule, "scaleout")
		if !r.IsOk() {
			return
		}
		*scaleInRuleId, r = createEventTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleInRule, "scalein")
		if !r.IsOk() {
			return
		}

	case base.TASK_CRONTAB_TYPE_CRON_EVENT:
		// cronEvent: 定时缩容_事件扩容 - 缩容是时间触发，扩容是事件触发
		*scaleOutRuleId, r = createEventTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleOutRule, "scaleout")
		if !r.IsOk() {
			return
		}
		*scaleInRuleId, r = createTimeTriggeredRule(input, taskId, moduleInfo, tagInsInfo, input.ScaleInRule, "scalein")
		if !r.IsOk() {
			return
		}

	default:
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "invalid schedType", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// createTimeTriggeredRule 创建时间触发规则
func createTimeTriggeredRule(input CrontabTaskCreateInput, taskId int, moduleInfo dao.Module, tagInsInfo comm.TagIns, ruleInfo CrontabRuleInfo, ruleType string) (ruleId int, r ocommon.ResultInfo) {
	rule := dao.RuleOnline{
		ModuleId:        input.ModuleId,
		TaskId:          taskId,
		Name:            fmt.Sprintf("%s_crontab_%s_rule", moduleInfo.ServiceName, ruleType),
		ScalingRuleType: base.SCALING_RULE_TIME_TRIGGER,
		CurrentNum:      tagInsInfo.TotalNum,
		AvailNum:        tagInsInfo.AvailNum,
		AdjustNum:       input.AdjustNum,
		StepCount:       input.StepCount,
		StepInterval:    input.StepInterval,
		CronPeriod:      ruleInfo.CronPeriod,
		CronDay:         ruleInfo.CronDay, // 直接存储中文星期
		CronTime:        ruleInfo.CronTime,
		CronProtectType: input.CronProtectType,
		LastModifyTime:  time.Now(),
	}

	ruleId, r = createRuleRecord(rule)
	return
}

// createEventTriggeredRule 创建事件触发规则
func createEventTriggeredRule(input CrontabTaskCreateInput, taskId int, moduleInfo dao.Module, tagInsInfo comm.TagIns, ruleInfo CrontabRuleInfo, ruleType string) (ruleId int, r ocommon.ResultInfo) {
	rule := dao.RuleOnline{
		ModuleId:        input.ModuleId,
		TaskId:          taskId,
		Name:            fmt.Sprintf("%s_crontab_%s_rule", moduleInfo.ServiceName, ruleType),
		ScalingRuleType: base.SCALING_RULE_EVENT_TRIGGER,
		CurrentNum:      tagInsInfo.TotalNum,
		AvailNum:        tagInsInfo.AvailNum,
		AdjustNum:       input.AdjustNum,
		StepCount:       input.StepCount,
		StepInterval:    input.StepInterval,
		CronProtectType: input.CronProtectType,
		LastModifyTime:  time.Now(),
	}

	ruleId, r = createRuleRecord(rule)
	return
}
