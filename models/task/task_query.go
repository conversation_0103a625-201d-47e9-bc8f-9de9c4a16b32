package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/oquery"
	"dxm/siod_sre/auto-scaler/base"
	base_tool "dxm/siod_sre/auto-scaler/base/tool"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/global"
	modelComm "dxm/siod_sre/auto-scaler/models/comm"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"time"

	"dxm/siod-cloud/go-common-lib/otool"

	"github.com/astaxie/beego/orm"
)

type TaskQuery struct {
	TaskId   int    `json:"taskId"`
	Type     string `json:"taskType"`
	PageNum  int    `json:"pageNum"`
	PageSize int    `json:"pageSize"`
	Search   string `json:"search"`
}

type TaskListRes struct {
	PageInfo
	TaskDetail []TaskInfoList `json:"taskDetail"`
}

type TaskInfoList struct {
	ID         int    `json:"id"`
	TaskName   string `json:"taskName"`
	TaskType   string `json:"taskType"`
	ModuleName string `json:"moduleName"`
	IdcTag     string `json:"idcTag"`
	SchedMode  string `json:"schedMode"`
	SchedType  string `json:"schedType"`
	JobStatus  string `json:"jobStatus"`
	TaskStatus string `json:"taskStatus"`
	TaskDesc   string `json:"taskDesc"`
}

type TaskHistoryRes struct {
	PageInfo
	TaskHistory []TaskHistoryList `json:"taskHistory"`
}

type TaskHistoryList struct {
	ID               int       `json:"id"`               // 任务历史ID
	CreateTime       time.Time `json:"createTime"`       // 创建时间
	FinishTime       time.Time `json:"finishTime"`       // 完成时间
	IdcTag           string    `json:"idcTag"`           // 机房标签
	Step             string    `json:"step"`             // 执行步骤
	CurrentNum       int       `json:"currentNum"`       // 当前实例数
	ExpectedTotalNum int       `json:"expectedTotalNum"` // 期望总实例数
	ActualNum        int       `json:"actualNum"`        // 实际实例数
	ActionReason     string    `json:"actionReason"`     // 执行原因
	Status           int       `json:"status"`           // 执行状态
}

type TaskQueryRes struct {
	PageNum     int `json:"pageNum"`
	PageSize    int `json:"pageSize"`
	Total       int `json:"total"`
	TaskDetails struct {
		dao.TaskInfo
		JobStatus string `json:"jobStatus"`
	} `json:"taskDetails"`
	TaskHistory []dao.EngineInfo `json:"taskHistory"`
	TaskRule    struct {
		dao.RuleOnline
		ScaleOutRule string `json:"scaleOutRule" decription:"扩容规则"`
		ScaleInRule  string `json:"scaleInRule" decription:"缩容规则"`
		//目标参数
		TargetParams []string `json:"targetParams" decription:"目标参数"`
	} `json:"taskRule"`
}

type TaskCreate struct {
	TaskId string `json:"taskId"`
	Type   string `json:"type"`
}

type PageInfo struct {
	PageNum  int `json:"pageNum"`
	PageSize int `json:"pageSize"`
	Total    int `json:"total"`
}

func GetTaskList(input TaskQuery) (r ocommon.ResultInfo) {
	// 设置默认分页参数
	if input.PageNum <= 0 {
		input.PageNum = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}

	daoTaskInfoPtr := dao.CreateTaskInfoPtr()
	var taskInfoSlice []dao.TaskInfo
	var count int64

	// 根据TaskId查询单条数据或分页查询
	if input.TaskId > 0 {
		var taskInfo dao.TaskInfo
		if r = daoTaskInfoPtr.SearchByPk(&taskInfo, input.TaskId); !r.IsOk() {
			olog.Error("failed to get task info by taskId from database, taskId:[%d], err:[%v]", input.TaskId, r)
			return
		}
		taskInfoSlice, count = []dao.TaskInfo{taskInfo}, 1
	} else {
		// 分页查询
		query := buildTaskQuery([]string{"TaskName", "ModuleName"}, input.Search, input.Type)
		if count, r = daoTaskInfoPtr.GetCountByQuery(query); !r.IsOk() {
			olog.Error("failed to get task count from database, err:[%v]", r)
			return
		}
		query.SetPageInfo(int64(input.PageNum), int64(input.PageSize))
		if r = daoTaskInfoPtr.SearchByQuery(&taskInfoSlice, query); !r.IsOk() {
			olog.Error("failed to get task info from database, err:[%v]", r)
			return
		}
	}

	// 构建任务列表
	var taskList []TaskInfoList
	for _, task := range taskInfoSlice {
		jobStatus, taskStatus := "", ""
		if input.Type == base.TASK_TYPE_MANUAL {
			jobStatus = task.TaskStatus
		} else {
			taskStatus = task.TaskStatus
		}

		taskList = append(taskList, TaskInfoList{
			ID: task.ID, TaskName: task.TaskName, TaskType: task.TaskType, ModuleName: task.ModuleName,
			IdcTag: task.IdcTag, SchedMode: task.SchedMode, SchedType: task.SchedType,
			JobStatus: jobStatus, TaskStatus: taskStatus, TaskDesc: task.TaskDesc,
		})
	}

	return ocommon.GenResultInfo(0, "success", TaskListRes{
		PageInfo:   PageInfo{PageNum: input.PageNum, PageSize: input.PageSize, Total: int(count)},
		TaskDetail: taskList,
	}, nil)
}

// BuildTaskQuery 构建任务查询条件的函数
// 参数:
//   - searchFields: 搜索字段列表
//   - searchKeyword: 搜索关键词
//   - taskType: 任务类型
//   - pageNum: 页码
//   - pageSize: 每页大小
//
// 返回:
//   - interface{}: 构建好的查询对象
func buildTaskQuery(searchFields []string, searchKeyword, taskType string) *oquery.QueryStructOfTable {
	query := oquery.NewQueryStructOfTable()
	var cond1 *orm.Condition
	var cond2 *orm.Condition //确定字段cond
	var cond3 *orm.Condition //用于结合cond1 和 cond2
	// 所有在 SearchField 的 都去搜索
	cond := orm.NewCondition()
	if searchKeyword != "" {
		for _, searchItem := range searchFields {
			cond1 = cond.AndCond(cond1).OrCond(cond.And(searchItem+"__"+oquery.OP_I_CONTAINS, searchKeyword))
		}
		cond1 = cond.AndCond(cond1)
	}
	cond2 = cond.And("Dflag"+"__"+oquery.OP_EQUAL, 0).And("TaskType"+"__"+oquery.OP_EQUAL, taskType)
	cond3 = cond.AndCond(cond1).AndCond(cond2)
	query.AddCustomCondition(cond3)
	query.AddOrders(oquery.Order{Name: "ID", IsASC: true})

	return query
}

// 获取历史执行记录
func GetTaskHistory(input TaskQuery) (r ocommon.ResultInfo) {
	if input.PageNum <= 0 {
		input.PageNum = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}
	if input.TaskId == 0 {
		olog.Error("taskId is not exist when get manual task history, input_data:[%v]", input)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get manual task history", nil, nil)
		return
	}

	var (
		daoEnginePtr = dao.CreateEngineInfoPtr()
		count        int64
	)
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("TaskId", oquery.OP_EQUAL, input.TaskId)
	count, r = daoEnginePtr.GetCountByQuery(query)
	if !r.IsOk() {
		olog.Error("failed to get engine count from database, err:[%v]", r)
		return
	}

	query.AddOrders(oquery.Order{Name: "ID", IsASC: false})
	query.SetPageInfo(int64(input.PageNum), int64(input.PageSize))
	var taskHistorySlice []dao.EngineInfo
	r = daoEnginePtr.SearchByQuery(&taskHistorySlice, query)
	if !r.IsOk() {
		olog.Error("failed to get engine info from database, err:[%v]", r)
		return
	}

	// 转换 dao.EngineInfo 为 TaskHistoryList
	var taskHistoryList []TaskHistoryList
	for _, engineInfo := range taskHistorySlice {
		taskHistoryList = append(taskHistoryList, TaskHistoryList{
			ID:               engineInfo.ID,
			CreateTime:       engineInfo.CreateTime,
			FinishTime:       engineInfo.FinishTime,
			IdcTag:           engineInfo.IdcTag,
			Step:             "1/1",
			CurrentNum:       engineInfo.CurrentNum,
			ExpectedTotalNum: engineInfo.ExpectedNum,
			ActualNum:        engineInfo.ActualNum,
			ActionReason:     engineInfo.ActionReason,
			Status:           engineInfo.Status,
		})
	}

	// 构建返回结果
	taskHistoryRes := TaskHistoryRes{
		PageInfo: PageInfo{
			PageNum:  input.PageNum,
			PageSize: input.PageSize,
			Total:    int(count),
		},
		TaskHistory: taskHistoryList,
	}

	r = ocommon.GenResultInfo(0, "success", taskHistoryRes, nil)
	return
}

func GetManualRule(input TaskQuery) (r ocommon.ResultInfo) {
	if input.TaskId == 0 {
		olog.Error("taskId is not exist when get manual task rule, input_data:[%v]", input)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get manual task rule", nil, nil)
		return
	}

	type ManualRule struct {
		TotalInsNum   int    `json:"totalInsNum"`   // 总实例数量
		AvailNum      int    `json:"availNum"`      // 可用实例数量
		ManualAction  string `json:"manualAction"`  // 手动操作动作: up(增加) 或 down(减少)
		AdjustNum     int    `json:"adjustNum"`     // 调整实例数量
		BlockAutoTask bool   `json:"blockAutoTask"` // 是否禁止自动任务
		CooldownTime  int    `json:"cooldownTime"`  // 冷却时间(分钟)
	}

	var (
		daoRulePtr     = dao.CreateRuleOnlinePtr()
		ruleOnlineInfo dao.RuleOnline
		rule           ManualRule
	)
	r = daoRulePtr.SearchByColumn(&ruleOnlineInfo, &dao.RuleOnline{TaskId: input.TaskId}, []string{"TaskId"})
	if !r.IsOk() {
		olog.Error("failed to get rule online from database, task_id:[%d], err:[%v]", input.TaskId, r)
		return
	}

	// 转换 dao.RuleOnline 为 ManualRule
	rule = ManualRule{
		TotalInsNum:   ruleOnlineInfo.CurrentNum,
		AvailNum:      ruleOnlineInfo.AvailNum,
		ManualAction:  ruleOnlineInfo.ManualAction,
		AdjustNum:     ruleOnlineInfo.AdjustNum,
		BlockAutoTask: ruleOnlineInfo.BlockAutoTask,
		CooldownTime:  ruleOnlineInfo.CooldownTime,
	}

	r = ocommon.GenResultInfo(0, "success", rule, nil)
	return
}

func GetCrontabRule(input TaskQuery) (r ocommon.ResultInfo) {
	if input.TaskId == 0 {
		olog.Error("taskId is not exist when get crontab task rule, input_data:[%v]", input)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get crontab task rule", nil, nil)
		return
	}
	type CrontabRule struct {
		TotalInsNum     int             `json:"totalInsNum"`     // 总实例数量
		AvailNum        int             `json:"availNum"`        // 可用实例数量
		AdjustNum       int             `json:"adjustNum"`       // 调整实例数量
		ScaleOutRuleCH  string          `json:"scaleOutRuleCH"`  // 扩容规则(定时时间)
		ScaleInRuleCH   string          `json:"scaleInRuleCH"`   // 缩容规则(手动调用或定时)
		ScaleOutRule    CrontabRuleInfo `json:"scaleOutRule"`    // 扩容规则
		ScaleInRule     CrontabRuleInfo `json:"scaleInRule"`     // 缩容规则
		SchedType       string          `json:"scaleMode"`       // 缩扩模式(定时任务)
		CronProtectType string          `json:"cronProtectType"` // 定时任务保护类型
		StepCount       int             `json:"stepCount"`       // 步进次数
		StepInterval    int             `json:"stepInterval"`    // 步进时间间隔(分钟)
	}

	var (
		taskInfo     dao.TaskInfo
		scaleInRule  dao.RuleOnline // 缩容规则(定时)
		scaleOutRule dao.RuleOnline // 扩容规则(定时)
		scaleInInfo  string
		scaleOutInfo string
	)

	// 查询任务信息
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, input.TaskId)
	if !r.IsOk() {
		olog.Error("failed to get task info from database, taskId:[%d], err:[%v]", input.TaskId, r)
		return
	}

	// 查询扩容规则
	r = dao.CreateRuleOnlinePtr().SearchByPk(&scaleOutRule, taskInfo.SchedScaleOutRule)
	if !r.IsOk() {
		olog.Error("failed to get scale out rule from database, taskId:[%d], ruleId:[%d], err:[%v]",
			input.TaskId, taskInfo.SchedScaleOutRule, r)
		return
	}

	// 查询缩容规则
	r = dao.CreateRuleOnlinePtr().SearchByPk(&scaleInRule, taskInfo.SchedScaleInRule)
	if !r.IsOk() {
		olog.Error("failed to get scale in rule from database, taskId:[%d], ruleId:[%d], err:[%v]",
			input.TaskId, taskInfo.SchedScaleInRule, r)
		return
	}

	switch taskInfo.SchedType {
	case base.TASK_CRONTAB_TYPE_CRON2:
		scaleOutInfo = buildCrontabRule(scaleOutRule, base.SCALING_RULE_TIME_TRIGGER)
		scaleInInfo = buildCrontabRule(scaleInRule, base.SCALING_RULE_TIME_TRIGGER)
	case base.TASK_CRONTAB_TYPE_CRON_EVENT:
		scaleOutInfo = buildCrontabRule(scaleOutRule, base.SCALING_RULE_TIME_TRIGGER)
		scaleInInfo = buildCrontabRule(scaleInRule, base.SCALING_RULE_EVENT_TRIGGER)
	case base.TASK_CRONTAB_TYPE_EVENT_CRON:
		scaleOutInfo = buildCrontabRule(scaleOutRule, base.SCALING_RULE_EVENT_TRIGGER)
		scaleInInfo = buildCrontabRule(scaleInRule, base.SCALING_RULE_TIME_TRIGGER)
	default:
		r = ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, "sched type is invalid", nil, nil)
		return
	}

	crontabRule := CrontabRule{
		TotalInsNum:    scaleOutRule.CurrentNum,
		AdjustNum:      scaleOutRule.AdjustNum,
		SchedType:      taskInfo.SchedType,
		ScaleOutRuleCH: fmt.Sprintf("扩容: %s", scaleOutInfo),
		ScaleInRuleCH:  fmt.Sprintf("缩容: %s", scaleInInfo),
		ScaleOutRule: CrontabRuleInfo{
			CronPeriod: scaleOutRule.CronPeriod,
			CronTime:   scaleOutRule.CronTime,
			CronDay:    scaleOutRule.CronDay,
		},
		ScaleInRule: CrontabRuleInfo{
			CronPeriod: scaleInRule.CronPeriod,
			CronTime:   scaleInRule.CronTime,
			CronDay:    scaleInRule.CronDay,
		},
		CronProtectType: scaleOutRule.CronProtectType,
		StepCount:       scaleOutRule.StepCount,
		StepInterval:    scaleOutRule.StepInterval,
	}

	r = ocommon.GenResultInfo(0, "success", crontabRule, nil)
	return
}

func buildCrontabRule(rule dao.RuleOnline, ruleType string) (crontabRule string) {
	switch ruleType {
	case base.SCALING_RULE_TIME_TRIGGER:
		switch rule.CronPeriod {
		case base.CRON_PERIOD_DAY:
			crontabRule = fmt.Sprintf("每天 %s", rule.CronTime)
		case base.CRON_PERIOD_WEEK:
			// 保持显示中文星期，因为这是给用户看的
			crontabRule = fmt.Sprintf("每周%s %s", rule.CronDay, rule.CronTime)
		case base.CRON_PERIOD_MONTH:
			crontabRule = fmt.Sprintf("每月%s号 %s", rule.CronDay, rule.CronTime)
		}
	case base.SCALING_RULE_EVENT_TRIGGER:
		switch global.YunEnv {
		case "yun_env_xd":
			crontabRule = fmt.Sprintf("http://cloud.dxmxd02-int.com/auto-scaler/api/v1/task/run?ruleId=%d", rule.ID)
		case "yun_env_kj":
			crontabRule = fmt.Sprintf("http://cloud.dxmkj01-int.com/auto-scaler/api/v1/task/run?ruleId=%d", rule.ID)
		case "yun_env_zf":
			crontabRule = fmt.Sprintf("http://cloud.dxmzf01-int.com/auto-scaler/api/v1/task/run?ruleId=%d", rule.ID)
		case "yun_env_test":
			crontabRule = fmt.Sprintf("http://cloud-test.duxiaoman-int.com/auto-scaler/api/v1/task/run?ruleId=%d", rule.ID)
		}
	}
	return
}

func GetAutoTaskRule(input TaskQuery) (r ocommon.ResultInfo) {
	if input.TaskId == 0 {
		olog.Error("taskId is not exist when get auto task rule, input_data:[%v]", input)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is not exist when get auto task rule", nil, nil)
		return
	}

	type AutoTaskRule struct {
		TargetParams       []string          `json:"targetParams"`       // 目标参数列表(如CPU使用率、平均耗时等)
		TargetMetric       []base.MetricInfo `json:"targetMetric"`       // 目标监控指标(json格式)
		TargetPeriod       int               `json:"targetPeriod"`       // 目标监控周期(分钟)
		TargetTriggerTimes int               `json:"targetTriggerTimes"` // 目标触发次数
		TargetCanScaling   bool              `json:"targetCanScaling"`   // 是否可以进行扩缩容
		StepCount          int               `json:"stepCount"`          // 步进次数
		StepInterval       int               `json:"stepInterval"`       // 步进时间间隔(分钟)
	}

	var (
		daoRulePtr     = dao.CreateRuleOnlinePtr()
		ruleOnlineInfo dao.RuleOnline
	)
	r = daoRulePtr.SearchByColumn(&ruleOnlineInfo, &dao.RuleOnline{TaskId: input.TaskId}, []string{"TaskId"})
	if !r.IsOk() {
		olog.Error("failed to get rule online from database, task_id:[%d], err:[%v]", input.TaskId, r)
		return
	}

	var metricList []base.MetricInfo
	err := json.Unmarshal([]byte(ruleOnlineInfo.TargetMetric), &metricList)
	if err != nil {
		olog.Error("json unmarshal failed when get auto task rule, taskId: %d, err: %v", input.TaskId, err)
		return
	}

	autoTaskRule := AutoTaskRule{
		TargetParams:       buildAutoTaskMetric(metricList),   // 目标参数列表
		TargetMetric:       metricList,                        // 监控指标
		TargetPeriod:       ruleOnlineInfo.TargetPeriod,       // 监控周期
		TargetTriggerTimes: ruleOnlineInfo.TargetTriggerTimes, // 触发次数
		TargetCanScaling:   ruleOnlineInfo.TargetCanScaling,   // 是否可扩缩容
		StepCount:          ruleOnlineInfo.StepCount,          // 步进次数
		StepInterval:       ruleOnlineInfo.StepInterval,       // 步进间隔
	}

	r = ocommon.GenResultInfo(0, "success", autoTaskRule, nil)
	return
}

func buildAutoTaskMetric(metricList []base.MetricInfo) (metricInfoList []string) {
	for _, metric := range metricList {
		upLimit, downLimit := comm.ComputeMetricLimit(metric)
		var metricInfo string
		switch metric.MetricName {
		case base.METRIC_NAME_CPU_PERCENT:
			metricInfo = fmt.Sprintf("%s：%.0f%% （%.0f%% ~ %.0f%%）", base.MetricNameAliasMap[metric.MetricName],
				metric.TargetValue, downLimit, upLimit)
		case base.METRIC_NAME_SINGLE_INSTANCE_QPS:
			metricInfo = fmt.Sprintf("%s：%.2f （%.2f ~ %.2f）", base.MetricNameAliasMap[metric.MetricName],
				metric.TargetValue, downLimit, upLimit)
		default:
			metricInfo = fmt.Sprintf("%s：%.2f （%.2f ~ %.2f）", base.MetricNameAliasMap[metric.MetricName],
				metric.TargetValue, downLimit, upLimit)
		}

		metricInfoList = append(metricInfoList, metricInfo)
	}
	return
}

// ManualTaskCheckInput 手动任务检查输入参数
type ManualTaskCheckInput struct {
	TaskId int `json:"taskId"` // 任务ID
}

// ManualTaskCheckInstanceInfo 实例信息
type ManualTaskCheckInstanceInfo struct {
	TotalNumBefore int `json:"totalNumBefore"` // 变更前总实例数
	AvailNumBefore int `json:"availNumBefore"` // 变更前可用实例数
	TotalNumAfter  int `json:"totalNumAfter"`  // 变更后总实例数
	AvailNumAfter  int `json:"availNumAfter"`  // 变更后可用实例数
}

// ManualTaskCheckResult 手动任务检查结果
type ManualTaskCheckResult struct {
	ID            int                         `json:"id"`            // 任务ID
	TaskName      string                      `json:"taskName"`      // 任务名称
	TaskType      string                      `json:"taskType"`      // 任务类型
	ModuleId      int                         `json:"moduleId"`      // 模块ID
	ModuleName    string                      `json:"moduleName"`    // 模块名称
	IdcTag        string                      `json:"idcTag"`        // 机房
	ScaleMode     string                      `json:"scaleMode"`     // 伸缩模式
	InstanceInfos ManualTaskCheckInstanceInfo `json:"instanceInfos"` // 实例信息
	WarnInfos     []string                    `json:"warnInfos"`     // 警告信息
}

func ManualTaskCheck(input ManualTaskCheckInput) (r ocommon.ResultInfo) {
	inputDebug, _ := json.Marshal(&input)

	// 参数验证
	if input.TaskId <= 0 {
		olog.Error("invalid taskId when check manual task, taskId:[%d]", input.TaskId)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskId is invalid", nil, nil)
		return
	}

	// 获取任务相关信息
	taskInfo, moduleInfo, ruleInfo, r := getManualTaskCheckData(input.TaskId)
	if !r.IsOk() {
		return
	}

	// 获取当前实例信息
	currentTagIns, r := getCurrentInstanceInfo(moduleInfo, taskInfo)
	if !r.IsOk() {
		olog.Error("failed to get instance info when check manual task, taskId:[%d], err:[%v]", input.TaskId, r)
		return
	}

	// 解析模块实例限制信息
	moduleInstanceInfo, foundInstanceInfo := parseModuleInstanceInfo(moduleInfo, taskInfo.IdcTag, input.TaskId)

	// 计算变更后的实例数和警告信息
	totalNumAfter, availNumAfter, warnInfos := calculateInstanceChanges(ruleInfo, currentTagIns, moduleInstanceInfo, foundInstanceInfo, input.TaskId)
	if totalNumAfter == -1 { // 表示无效操作
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "invalid manual action", nil, nil)
		return
	}

	// 添加其他警告信息
	warnInfos = addAdditionalWarnings(warnInfos, moduleInfo, ruleInfo)

	// 构造返回结果
	result := buildManualTaskCheckResult(taskInfo, currentTagIns, totalNumAfter, availNumAfter, warnInfos)

	olog.Info("manual task check completed successfully, taskId:[%d], input_data:[%s]", input.TaskId, string(inputDebug))
	r = ocommon.GenResultInfo(0, "success", result, nil)
	return
}

// getManualTaskCheckData 获取手动任务检查所需的基础数据
func getManualTaskCheckData(taskId int) (taskInfo dao.TaskInfo, moduleInfo dao.Module, ruleInfo dao.RuleOnline, r ocommon.ResultInfo) {
	// 查询任务信息
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, taskId)
	if !r.IsOk() {
		olog.Error("task not found when check manual task, taskId:[%d], err:[%v]", taskId, r)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "task not found", nil, nil)
		return
	}

	// 验证任务类型是否为手动任务
	if taskInfo.TaskType != base.TASK_TYPE_MANUAL {
		olog.Error("task type is not manual when check manual task, taskId:[%d], taskType:[%s]", taskId, taskInfo.TaskType)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "task type is not manual", nil, nil)
		return
	}

	// 获取模块信息
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, taskInfo.ModuleId)
	if !r.IsOk() {
		olog.Error("failed to get module info when check manual task, taskId:[%d], err:[%v]", taskId, r)
		return
	}

	// 获取规则信息
	r = dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, taskInfo.RuleId)
	if !r.IsOk() {
		olog.Error("rule not found when check manual task, taskId:[%d], ruleId:[%d], err:[%v]", taskId, taskInfo.RuleId, r)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "rule not found", nil, nil)
		return
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// getCurrentInstanceInfo 获取当前实例信息
func getCurrentInstanceInfo(moduleInfo dao.Module, taskInfo dao.TaskInfo) (currentTagIns modelComm.TagIns, r ocommon.ResultInfo) {
	tagInsList, r := modelComm.GetOnlineIns(moduleInfo.ServiceName, taskInfo.TaskModuleType, taskInfo.IdcTag)
	if !r.IsOk() {
		return
	}

	if len(tagInsList) != 0 {
		currentTagIns = tagInsList[0]
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// parseModuleInstanceInfo 解析模块实例限制信息
func parseModuleInstanceInfo(moduleInfo dao.Module, idcTag string, taskId int) (moduleInstanceInfo dao.ModuleInstanceInfo, foundInstanceInfo bool) {
	var moduleInstanceInfos []dao.ModuleInstanceInfo
	if moduleInfo.InstanceInfo != "" {
		err := json.Unmarshal([]byte(moduleInfo.InstanceInfo), &moduleInstanceInfos)
		if err != nil {
			olog.Error("failed to unmarshal module instance info when check manual task, taskId:[%d], err:[%v]", taskId, err)
			return
		}
	}

	// 查找对应机房的实例限制信息
	for _, info := range moduleInstanceInfos {
		if info.Tag == idcTag {
			moduleInstanceInfo = info
			foundInstanceInfo = true
			break
		}
	}
	return
}

// calculateInstanceChanges 计算实例变更和警告信息
func calculateInstanceChanges(ruleInfo dao.RuleOnline, currentTagIns modelComm.TagIns, moduleInstanceInfo dao.ModuleInstanceInfo, foundInstanceInfo bool, taskId int) (totalNumAfter, availNumAfter int, warnInfos []string) {
	switch ruleInfo.ManualAction {
	case base.ACTION_UP:
		// 扩容操作
		totalNumAfter = currentTagIns.TotalNum + ruleInfo.AdjustNum
		availNumAfter = currentTagIns.AvailNum + ruleInfo.AdjustNum

		// 检查是否超过最大实例数限制
		if foundInstanceInfo && totalNumAfter > moduleInstanceInfo.MaxNum {
			warnInfos = append(warnInfos, fmt.Sprintf("扩容后总实例数(%d)将超过模块设定的最大实例数(%d)", totalNumAfter, moduleInstanceInfo.MaxNum))
		}

	case base.ACTION_DOWN:
		// 缩容操作
		totalNumAfter = currentTagIns.TotalNum - ruleInfo.AdjustNum
		availNumAfter = currentTagIns.AvailNum - ruleInfo.AdjustNum

		// 检查缩容后实例数是否合理
		if totalNumAfter < 0 {
			totalNumAfter = 0
			warnInfos = append(warnInfos, "缩容后总实例数将为0，请确认操作")
		}
		if availNumAfter < 0 {
			availNumAfter = 0
			warnInfos = append(warnInfos, "缩容后可用实例数将为0，请确认操作")
		}
		if ruleInfo.AdjustNum > currentTagIns.TotalNum {
			warnInfos = append(warnInfos, "缩容调整数量大于当前总实例数")
		}

		// 检查是否低于最小实例数限制
		if foundInstanceInfo && totalNumAfter < moduleInstanceInfo.MinNum {
			warnInfos = append(warnInfos, fmt.Sprintf("缩容后总实例数(%d)将低于模块设定的最小实例数(%d)", totalNumAfter, moduleInstanceInfo.MinNum))
		}

	default:
		olog.Error("invalid manual action when check manual task, taskId:[%d], action:[%s]", taskId, ruleInfo.ManualAction)
		return -1, -1, nil // 返回-1表示无效操作
	}
	return
}

// addAdditionalWarnings 添加其他警告信息
func addAdditionalWarnings(warnInfos []string, moduleInfo dao.Module, ruleInfo dao.RuleOnline) []string {
	// 检查模块是否被禁用自动任务
	if moduleInfo.DisableAutoTask && ruleInfo.BlockAutoTask {
		warnInfos = append(warnInfos, "模块已禁用自动任务，且当前任务也设置了禁用自动任务")
	}

	// 检查冷却时间
	if ruleInfo.CooldownTime > 0 {
		warnInfos = append(warnInfos, fmt.Sprintf("任务设置了冷却时间：%d分钟", ruleInfo.CooldownTime))
	}

	return warnInfos
}

// buildManualTaskCheckResult 构造手动任务检查结果
func buildManualTaskCheckResult(taskInfo dao.TaskInfo, currentTagIns modelComm.TagIns, totalNumAfter, availNumAfter int, warnInfos []string) ManualTaskCheckResult {
	return ManualTaskCheckResult{
		ID:         taskInfo.ID,
		TaskName:   taskInfo.TaskName,
		TaskType:   taskInfo.TaskType,
		ModuleId:   taskInfo.ModuleId,
		ModuleName: taskInfo.ModuleName,
		IdcTag:     taskInfo.IdcTag,
		ScaleMode:  taskInfo.SchedMode,
		InstanceInfos: ManualTaskCheckInstanceInfo{
			TotalNumBefore: currentTagIns.TotalNum,
			AvailNumBefore: currentTagIns.AvailNum,
			TotalNumAfter:  totalNumAfter,
			AvailNumAfter:  availNumAfter,
		},
		WarnInfos: warnInfos,
	}
}

// GetMetricValueInput 获取指标值输入参数
type GetMetricValueInput struct {
	ModuleName string `json:"moduleName"` // 模块名称
	IdcTag     string `json:"idcTag"`     // 机房标签
}

// MetricValueInfo 指标值信息
type MetricValueInfo struct {
	Metric   string  `json:"metric"`   // 指标名
	MetricCH string  `json:"metricCH"` // 指标中文名
	Value    float64 `json:"value"`    // 当前值
}

// calculate90thPercentile 计算90%分位数
func calculate90thPercentile(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	// 过滤掉0值
	var filteredValues []float64
	for _, v := range values {
		if v > 0 {
			filteredValues = append(filteredValues, v)
		}
	}

	if len(filteredValues) == 0 {
		return 0
	}

	// 排序
	sort.Float64s(filteredValues)

	// 计算90%分位数的索引
	index := int(math.Ceil(float64(len(filteredValues))*0.9)) - 1
	if index < 0 {
		index = 0
	}
	if index >= len(filteredValues) {
		index = len(filteredValues) - 1
	}

	return filteredValues[index]
}

// GetMetricValue 获取自动化任务目标值
func GetMetricValue(input GetMetricValueInput) (r ocommon.ResultInfo) {
	// 当两个参数均为空时，直接返回构建指标列表
	if input.ModuleName == "" && input.IdcTag == "" {
		olog.Info("both moduleName and idcTag are empty, returning default metric list")
		return buildDefaultMetricList()
	}

	// 参数验证
	if input.ModuleName == "" || input.IdcTag == "" {
		olog.Error("invalid input parameters when get metric value, input:[%+v]", input)
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "moduleName and idcTag are required", nil, nil)
	}

	// 查询模块信息
	var moduleInfo dao.Module
	if r = dao.CreateModulePtr().SearchByColumn(&moduleInfo, &dao.Module{Name: input.ModuleName, Dflag: 0}, []string{"Name", "Dflag"}); !r.IsOk() || moduleInfo.ID == 0 {
		olog.Error("module not found when get metric value, moduleName:[%s], err:[%v]", input.ModuleName, r)
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module not found", nil, nil)
	}

	// 查询容量平台数据
	oceanMoudle, r := dao_ocean.CreateNoahModulesPtr().GetOceanModuleList(moduleInfo.ServiceName)
	if !r.IsOk() || len(oceanMoudle) == 0 {
		olog.Error("no ocean module found when get metric value, serviceName:[%s], err:[%v]", moduleInfo.ServiceName, r)
		return ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, "no ocean module found", nil, nil)
	}

	// 查询最近一周的容量数据
	oceanDetails := dao_ocean.OceanDetailSearchByTag(oceanMoudle[0].Product, oceanMoudle[0].BnsName, input.IdcTag,
		base_tool.GetLastWeekByTime(time.Now()).Format(otool.TIME_FORMAT_STR), time.Now().Format(otool.TIME_FORMAT_STR))
	oceanDetailPods := dao_ocean.OceanDetailSearchByTag(oceanMoudle[0].Product, oceanMoudle[0].BnsName, input.IdcTag+"-pod",
		base_tool.GetLastWeekByTime(time.Now()).Format(otool.TIME_FORMAT_STR), time.Now().Format(otool.TIME_FORMAT_STR))
	oceanDetails = append(oceanDetails, oceanDetailPods...)
	if len(oceanDetails) == 0 {
		olog.Error("no ocean data found for IDC when get metric value, serviceName:[%s], idcTag:[%s]", moduleInfo.ServiceName, input.IdcTag)
		return ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, "no ocean data found for the specified IDC", nil, nil)
	}

	// 收集指标数据并计算90%分位数
	var costValues, qpsValues, waterLevelValues []float64
	for _, detail := range oceanDetails {
		costValues = append(costValues, detail.CostAvg)
		qpsValues = append(qpsValues, detail.QpsAvg)
		waterLevelValues = append(waterLevelValues, detail.Level)
	}
	cost90th, qps90th, waterLevel90th := calculate90thPercentile(costValues), calculate90thPercentile(qpsValues), calculate90thPercentile(waterLevelValues)
	olog.Debug("90th percentile values - cost:[%.2f], qps:[%.2f], waterLevel:[%.2f], data count:[%d]", cost90th, qps90th, waterLevel90th, len(oceanDetails))

	// 构建指标值列表
	metricValues := map[string]float64{
		base.METRIC_NAME_CPU_PERCENT:         35,
		base.METRIC_NAME_APP_CPU_PERCENT:     35,
		base.METRIC_NAME_AVERAGE_COST:        math.Round(cost90th),
		base.METRIC_NAME_SINGLE_INSTANCE_QPS: math.Round(qps90th),
		base.METRIC_NAME_WATER_LEVEL:         math.Round(waterLevel90th),
		base.METRIC_NAME_HTTP_CODE_NUM:       0,
	}

	var metricValueList []MetricValueInfo
	for metricName, metricCH := range base.MetricNameAliasMap {
		if value, exists := metricValues[metricName]; exists {
			metricValueList = append(metricValueList, MetricValueInfo{Metric: metricName, MetricCH: metricCH, Value: value})
		}
	}

	olog.Info("get metric value successfully, moduleName:[%s], idcTag:[%s], metricCount:[%d]", input.ModuleName, input.IdcTag, len(metricValueList))
	return ocommon.GenResultInfo(0, "success", metricValueList, nil)
}

// buildDefaultMetricList 构建默认指标列表
func buildDefaultMetricList() ocommon.ResultInfo {
	// 构建默认指标值列表
	metricValues := map[string]float64{
		base.METRIC_NAME_CPU_PERCENT:         35,
		base.METRIC_NAME_APP_CPU_PERCENT:     35,
		base.METRIC_NAME_AVERAGE_COST:        0,
		base.METRIC_NAME_SINGLE_INSTANCE_QPS: 0,
		base.METRIC_NAME_WATER_LEVEL:         0,
		base.METRIC_NAME_HTTP_CODE_NUM:       0,
	}

	var metricValueList []MetricValueInfo
	for metricName, metricCH := range base.MetricNameAliasMap {
		if value, exists := metricValues[metricName]; exists {
			metricValueList = append(metricValueList, MetricValueInfo{Metric: metricName, MetricCH: metricCH, Value: value})
		}
	}

	olog.Info("built default metric list successfully, metricCount:[%d]", len(metricValueList))
	return ocommon.GenResultInfo(0, "success", metricValueList, nil)
}
