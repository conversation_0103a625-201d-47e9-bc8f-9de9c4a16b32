package task

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/errno"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateManualTaskUpdateParams(t *testing.T) {
	tests := []struct {
		name    string
		input   ManualTaskUpdateInput
		wantErr bool
	}{
		{
			name: "valid manual task update params - up action",
			input: ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: false,
		},
		{
			name: "valid manual task update params - down action",
			input: ManualTaskUpdateInput{
				ID:            2,
				ManualAction:  base.ACTION_DOWN,
				AdjustNum:     3,
				BlockAutoTask: true,
				CooldownTime:  15,
			},
			wantErr: false,
		},
		{
			name: "valid manual task update params - zero cooldown time",
			input: ManualTaskUpdateInput{
				ID:            3,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     2,
				BlockAutoTask: false,
				CooldownTime:  0,
			},
			wantErr: false,
		},
		{
			name: "invalid id - zero",
			input: ManualTaskUpdateInput{
				ID:            0,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: true,
		},
		{
			name: "invalid id - negative",
			input: ManualTaskUpdateInput{
				ID:            -1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: true,
		},
		{
			name: "invalid manual action",
			input: ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  "invalid",
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: true,
		},
		{
			name: "invalid adjust num - zero",
			input: ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     0,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: true,
		},
		{
			name: "invalid adjust num - negative",
			input: ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     -1,
				BlockAutoTask: false,
				CooldownTime:  10,
			},
			wantErr: true,
		},
		{
			name: "invalid cooldown time - negative",
			input: ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  -1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateManualTaskUpdateParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
				assert.Equal(t, errno.ERR_INVALID_INPUT, result.ErrNo, "Expected ERR_INVALID_INPUT error code")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

func TestManualTaskUpdateInput_Validation(t *testing.T) {
	// 测试结构体字段的正确性
	input := ManualTaskUpdateInput{
		ID:            123,
		ManualAction:  base.ACTION_UP,
		AdjustNum:     10,
		BlockAutoTask: true,
		CooldownTime:  20,
	}

	// 验证字段值
	assert.Equal(t, 123, input.ID)
	assert.Equal(t, base.ACTION_UP, input.ManualAction)
	assert.Equal(t, 10, input.AdjustNum)
	assert.True(t, input.BlockAutoTask)
	assert.Equal(t, 20, input.CooldownTime)
}

func TestManualTaskUpdateResult_Structure(t *testing.T) {
	// 测试返回结果结构体
	result := ManualTaskUpdateResult{
		TaskId: 456,
	}

	assert.Equal(t, 456, result.TaskId)
}

// 测试边界值
func TestValidateManualTaskUpdateParams_BoundaryValues(t *testing.T) {
	tests := []struct {
		name         string
		id           int
		adjustNum    int
		cooldownTime int
		expectValid  bool
	}{
		{"minimum valid id", 1, 1, 0, true},
		{"large valid id", 999999, 1, 0, true},
		{"minimum valid adjustNum", 1, 1, 0, true},
		{"large valid adjustNum", 1, 1000, 0, true},
		{"minimum valid cooldownTime", 1, 1, 0, true},
		{"large valid cooldownTime", 1, 1, 9999, true},
		{"invalid id boundary", 0, 1, 0, false},
		{"invalid adjustNum boundary", 1, 0, 0, false},
		{"invalid cooldownTime boundary", 1, 1, -1, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := ManualTaskUpdateInput{
				ID:            tt.id,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     tt.adjustNum,
				BlockAutoTask: false,
				CooldownTime:  tt.cooldownTime,
			}

			result := validateManualTaskUpdateParams(input)

			if tt.expectValid {
				assert.True(t, result.IsOk(), "Expected valid but got error: %v", result)
			} else {
				assert.False(t, result.IsOk(), "Expected invalid but got success")
			}
		})
	}
}

// 测试不同的ManualAction值
func TestValidateManualTaskUpdateParams_ManualActions(t *testing.T) {
	validActions := []string{base.ACTION_UP, base.ACTION_DOWN}
	invalidActions := []string{"", "invalid", "UP", "DOWN", "scale", "expand", "shrink"}

	// 测试有效的动作
	for _, action := range validActions {
		t.Run("valid_action_"+action, func(t *testing.T) {
			input := ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  action,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			}

			result := validateManualTaskUpdateParams(input)
			assert.True(t, result.IsOk(), "Expected valid action %s but got error: %v", action, result)
		})
	}

	// 测试无效的动作
	for _, action := range invalidActions {
		t.Run("invalid_action_"+action, func(t *testing.T) {
			input := ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  action,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
			}

			result := validateManualTaskUpdateParams(input)
			assert.False(t, result.IsOk(), "Expected invalid action %s but got success", action)
		})
	}
}

// 测试BlockAutoTask的不同值
func TestValidateManualTaskUpdateParams_BlockAutoTask(t *testing.T) {
	testCases := []bool{true, false}

	for _, blockValue := range testCases {
		t.Run(fmt.Sprintf("block_auto_task_%v", blockValue), func(t *testing.T) {
			input := ManualTaskUpdateInput{
				ID:            1,
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: blockValue,
				CooldownTime:  10,
			}

			result := validateManualTaskUpdateParams(input)
			assert.True(t, result.IsOk(), "BlockAutoTask value %v should be valid", blockValue)
		})
	}
}

// TestValidateCrontabTaskUpdateParams 测试定时任务更新参数验证
func TestValidateCrontabTaskUpdateParams(t *testing.T) {
	tests := []struct {
		name    string
		input   CrontabTaskUpdateInput
		wantErr bool
	}{
		{
			name: "valid crontab update params - cronCron",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       2,
				StepInterval:    10,
			},
			wantErr: false,
		},
		{
			name: "valid crontab update params - eventCron",
			input: CrontabTaskUpdateInput{
				ID:              2,
				AdjustNum:       3,
				SchedType:       base.TASK_CRONTAB_TYPE_EVENT_CRON,
				CronProtectType: base.CRON_PROTECT_TYPE_PLAN,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: false,
		},
		{
			name: "valid crontab update params - cronEvent",
			input: CrontabTaskUpdateInput{
				ID:              3,
				AdjustNum:       4,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON_EVENT,
				CronProtectType: base.CRON_PROTECT_TYPE_NONE,
				StepCount:       3,
				StepInterval:    15,
			},
			wantErr: false,
		},
		{
			name: "valid crontab update params - zero stepInterval",
			input: CrontabTaskUpdateInput{
				ID:              4,
				AdjustNum:       2,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    0,
			},
			wantErr: false,
		},
		{
			name: "invalid id - zero",
			input: CrontabTaskUpdateInput{
				ID:              0,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid id - negative",
			input: CrontabTaskUpdateInput{
				ID:              -1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid adjustNum - zero",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       0,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid adjustNum - negative",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       -1,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid schedType",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       "invalid",
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid cronProtectType",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: "invalid",
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid stepCount - zero",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       0,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid stepCount - negative",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       -1,
				StepInterval:    5,
			},
			wantErr: true,
		},
		{
			name: "invalid stepInterval - negative",
			input: CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    -1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateCrontabTaskUpdateParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
				assert.Equal(t, errno.ERR_INVALID_INPUT, result.ErrNo, "Expected ERR_INVALID_INPUT error code")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

// TestCrontabTaskUpdateInput_Validation 测试定时任务更新输入结构体
func TestCrontabTaskUpdateInput_Validation(t *testing.T) {
	input := CrontabTaskUpdateInput{
		ID:              123,
		AdjustNum:       10,
		SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
		CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
		StepCount:       2,
		StepInterval:    15,
		ScaleOutRule: CrontabRuleInfo{
			CronPeriod: base.CRON_PERIOD_WEEK,
			CronDay:    "一",
			CronTime:   "09:00:00",
		},
		ScaleInRule: CrontabRuleInfo{
			CronPeriod: base.CRON_PERIOD_WEEK,
			CronDay:    "五",
			CronTime:   "18:00:00",
		},
	}

	// 验证字段值
	assert.Equal(t, 123, input.ID)
	assert.Equal(t, 10, input.AdjustNum)
	assert.Equal(t, base.TASK_CRONTAB_TYPE_CRON2, input.SchedType)
	assert.Equal(t, base.CRON_PROTECT_TYPE_SUGGEST, input.CronProtectType)
	assert.Equal(t, 2, input.StepCount)
	assert.Equal(t, 15, input.StepInterval)
	assert.Equal(t, base.CRON_PERIOD_WEEK, input.ScaleOutRule.CronPeriod)
	assert.Equal(t, "一", input.ScaleOutRule.CronDay)
	assert.Equal(t, "09:00:00", input.ScaleOutRule.CronTime)
}

// TestCrontabTaskUpdateResult_Structure 测试定时任务更新结果结构体
func TestCrontabTaskUpdateResult_Structure(t *testing.T) {
	result := CrontabTaskUpdateResult{
		TaskId: 456,
	}

	assert.Equal(t, 456, result.TaskId)
}

// TestValidateCrontabTaskUpdateParams_SchedTypes 测试不同的schedType值
func TestValidateCrontabTaskUpdateParams_SchedTypes(t *testing.T) {
	validSchedTypes := []string{
		base.TASK_CRONTAB_TYPE_CRON2,
		base.TASK_CRONTAB_TYPE_EVENT_CRON,
		base.TASK_CRONTAB_TYPE_CRON_EVENT,
	}
	invalidSchedTypes := []string{"", "invalid", "cron", "event", "manual", "auto"}

	// 测试有效的schedType
	for _, schedType := range validSchedTypes {
		t.Run("valid_schedType_"+schedType, func(t *testing.T) {
			input := CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       schedType,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			}

			result := validateCrontabTaskUpdateParams(input)
			assert.True(t, result.IsOk(), "Expected valid schedType %s but got error: %v", schedType, result)
		})
	}

	// 测试无效的schedType
	for _, schedType := range invalidSchedTypes {
		t.Run("invalid_schedType_"+schedType, func(t *testing.T) {
			input := CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       schedType,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			}

			result := validateCrontabTaskUpdateParams(input)
			assert.False(t, result.IsOk(), "Expected invalid schedType %s but got success", schedType)
		})
	}
}

// TestValidateCrontabTaskUpdateParams_CronProtectTypes 测试不同的cronProtectType值
func TestValidateCrontabTaskUpdateParams_CronProtectTypes(t *testing.T) {
	validProtectTypes := []string{
		base.CRON_PROTECT_TYPE_SUGGEST,
		base.CRON_PROTECT_TYPE_PLAN,
		base.CRON_PROTECT_TYPE_NONE,
		"", // 空字符串应该是有效的
	}
	invalidProtectTypes := []string{"invalid", "protect", "block", "allow"}

	// 测试有效的cronProtectType
	for _, protectType := range validProtectTypes {
		t.Run("valid_cronProtectType_"+protectType, func(t *testing.T) {
			input := CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: protectType,
				StepCount:       1,
				StepInterval:    5,
			}

			result := validateCrontabTaskUpdateParams(input)
			assert.True(t, result.IsOk(), "Expected valid cronProtectType %s but got error: %v", protectType, result)
		})
	}

	// 测试无效的cronProtectType
	for _, protectType := range invalidProtectTypes {
		t.Run("invalid_cronProtectType_"+protectType, func(t *testing.T) {
			input := CrontabTaskUpdateInput{
				ID:              1,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: protectType,
				StepCount:       1,
				StepInterval:    5,
			}

			result := validateCrontabTaskUpdateParams(input)
			assert.False(t, result.IsOk(), "Expected invalid cronProtectType %s but got success", protectType)
		})
	}
}

// TestValidateCrontabTaskUpdateParams_BoundaryValues 测试边界值
func TestValidateCrontabTaskUpdateParams_BoundaryValues(t *testing.T) {
	tests := []struct {
		name         string
		id           int
		adjustNum    int
		stepCount    int
		stepInterval int
		expectValid  bool
	}{
		{"minimum valid values", 1, 1, 1, 0, true},
		{"large valid values", 999999, 1000, 100, 9999, true},
		{"invalid id boundary", 0, 1, 1, 0, false},
		{"invalid adjustNum boundary", 1, 0, 1, 0, false},
		{"invalid stepCount boundary", 1, 1, 0, 0, false},
		{"invalid stepInterval boundary", 1, 1, 1, -1, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := CrontabTaskUpdateInput{
				ID:              tt.id,
				AdjustNum:       tt.adjustNum,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       tt.stepCount,
				StepInterval:    tt.stepInterval,
			}

			result := validateCrontabTaskUpdateParams(input)

			if tt.expectValid {
				assert.True(t, result.IsOk(), "Expected valid but got error: %v", result)
			} else {
				assert.False(t, result.IsOk(), "Expected invalid but got success")
			}
		})
	}
}

// TestValidateAutoTaskUpdateParams 测试自动化任务更新参数验证
func TestValidateAutoTaskUpdateParams(t *testing.T) {
	tests := []struct {
		name    string
		input   AutoTaskUpdateInput
		wantErr bool
	}{
		{
			name: "valid auto task update params",
			input: AutoTaskUpdateInput{
				ID:                 1,
				StepCount:          2,
				StepInterval:       10,
				TargetPeriod:       5,
				TargetTriggerTimes: 3,
				TargetCanScaling:   true,
				TargetMetric: []base.MetricInfo{
					{
						MetricName:    base.METRIC_NAME_CPU_PERCENT,
						TargetValue:   70.0,
						TargetRateMax: 0.2,
						TargetRateMin: 0.1,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid id - zero",
			input: AutoTaskUpdateInput{
				ID:                 0,
				StepCount:          1,
				StepInterval:       5,
				TargetPeriod:       5,
				TargetTriggerTimes: 3,
				TargetCanScaling:   true,
				TargetMetric: []base.MetricInfo{
					{
						MetricName:    base.METRIC_NAME_CPU_PERCENT,
						TargetValue:   70.0,
						TargetRateMax: 0.2,
						TargetRateMin: 0.1,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "invalid stepCount - zero",
			input: AutoTaskUpdateInput{
				ID:                 1,
				StepCount:          0,
				StepInterval:       5,
				TargetPeriod:       5,
				TargetTriggerTimes: 3,
				TargetCanScaling:   true,
				TargetMetric: []base.MetricInfo{
					{
						MetricName:    base.METRIC_NAME_CPU_PERCENT,
						TargetValue:   70.0,
						TargetRateMax: 0.2,
						TargetRateMin: 0.1,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "invalid targetMetric - empty",
			input: AutoTaskUpdateInput{
				ID:                 1,
				StepCount:          1,
				StepInterval:       5,
				TargetPeriod:       5,
				TargetTriggerTimes: 3,
				TargetCanScaling:   true,
				TargetMetric:       []base.MetricInfo{},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateAutoTaskUpdateParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
				assert.Equal(t, errno.ERR_INVALID_INPUT, result.ErrNo, "Expected ERR_INVALID_INPUT error code")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

// TestValidateTaskStatusUpdateParams 测试任务状态更新参数验证
func TestValidateTaskStatusUpdateParams(t *testing.T) {
	tests := []struct {
		name    string
		input   TaskStatusUpdateInput
		wantErr bool
	}{
		{
			name: "valid task status update params - enable",
			input: TaskStatusUpdateInput{
				IDs:    []int{1, 2, 3},
				Status: base.TASK_STATUS_ENABLE,
			},
			wantErr: false,
		},
		{
			name: "valid task status update params - testRun",
			input: TaskStatusUpdateInput{
				IDs:    []int{4, 5},
				Status: base.TASK_STATUS_TEST_RUN,
			},
			wantErr: false,
		},
		{
			name: "valid task status update params - offline",
			input: TaskStatusUpdateInput{
				IDs:    []int{6},
				Status: base.TASK_STATUS_OFFLINE,
			},
			wantErr: false,
		},
		{
			name: "invalid ids - empty",
			input: TaskStatusUpdateInput{
				IDs:    []int{},
				Status: base.TASK_STATUS_ENABLE,
			},
			wantErr: true,
		},
		{
			name: "invalid ids - zero value",
			input: TaskStatusUpdateInput{
				IDs:    []int{1, 0, 3},
				Status: base.TASK_STATUS_ENABLE,
			},
			wantErr: true,
		},
		{
			name: "invalid status - invalid value",
			input: TaskStatusUpdateInput{
				IDs:    []int{1, 2, 3},
				Status: "invalid",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateTaskStatusUpdateParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
				assert.Equal(t, errno.ERR_INVALID_INPUT, result.ErrNo, "Expected ERR_INVALID_INPUT error code")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

// TestValidateTaskDeleteParams 测试任务删除参数验证
func TestValidateTaskDeleteParams(t *testing.T) {
	tests := []struct {
		name    string
		input   TaskDeleteInput
		wantErr bool
	}{
		{
			name: "valid task delete params - single task",
			input: TaskDeleteInput{
				IDs: []int{1},
			},
			wantErr: false,
		},
		{
			name: "valid task delete params - multiple tasks",
			input: TaskDeleteInput{
				IDs: []int{1, 2, 3, 4, 5},
			},
			wantErr: false,
		},
		{
			name: "valid task delete params - large task id",
			input: TaskDeleteInput{
				IDs: []int{999999},
			},
			wantErr: false,
		},
		{
			name: "invalid ids - empty",
			input: TaskDeleteInput{
				IDs: []int{},
			},
			wantErr: true,
		},
		{
			name: "invalid ids - zero value",
			input: TaskDeleteInput{
				IDs: []int{1, 0, 3},
			},
			wantErr: true,
		},
		{
			name: "invalid ids - negative value",
			input: TaskDeleteInput{
				IDs: []int{1, -1, 3},
			},
			wantErr: true,
		},
		{
			name: "invalid ids - all zero",
			input: TaskDeleteInput{
				IDs: []int{0, 0, 0},
			},
			wantErr: true,
		},
		{
			name: "invalid ids - all negative",
			input: TaskDeleteInput{
				IDs: []int{-1, -2, -3},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateTaskDeleteParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
				assert.Equal(t, errno.ERR_INVALID_INPUT, result.ErrNo, "Expected ERR_INVALID_INPUT error code")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

// TestTaskDeleteInput_Structure 测试任务删除输入结构体
func TestTaskDeleteInput_Structure(t *testing.T) {
	input := TaskDeleteInput{
		IDs: []int{1, 2, 3, 4, 5},
	}

	// 验证字段值
	assert.Equal(t, []int{1, 2, 3, 4, 5}, input.IDs)
	assert.Equal(t, 5, len(input.IDs))
}

// TestTaskDeleteResult_Structure 测试任务删除结果结构体
func TestTaskDeleteResult_Structure(t *testing.T) {
	result := TaskDeleteResult{
		DeletedCount: 3,
	}

	assert.Equal(t, 3, result.DeletedCount)
}
