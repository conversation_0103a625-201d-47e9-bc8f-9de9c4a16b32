package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"testing"

	"github.com/stretchr/testify/assert"
)

// 测试专用的任务创建服务
type testTaskCreateService struct {
	moduleData        dao.Module
	existingTasks     []dao.TaskInfo
	shouldReturnError bool
	errorCode         int
	errorMessage      string
	createdTaskId     int
	createdRuleId     int
}

// 创建测试服务实例
func newTestTaskCreateService() *testTaskCreateService {
	return &testTaskCreateService{
		createdTaskId: 1,
		createdRuleId: 1,
	}
}

// 设置模块数据
func (t *testTaskCreateService) setModuleData(module dao.Module) {
	t.moduleData = module
}

// 设置已存在的任务
func (t *testTaskCreateService) setExistingTasks(tasks []dao.TaskInfo) {
	t.existingTasks = tasks
}

// 设置错误返回
func (t *testTaskCreateService) setError(code int, message string) {
	t.shouldReturnError = true
	t.errorCode = code
	t.errorMessage = message
}

// 设置创建的ID
func (t *testTaskCreateService) setCreatedIds(taskId, ruleId int) {
	t.createdTaskId = taskId
	t.createdRuleId = ruleId
}

// 模拟validateBasicTaskParams函数
func (t *testTaskCreateService) mockValidateBasicTaskParams(input TaskCreateInfo) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	if input.ModuleId <= 0 {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "moduleId is invalid", nil, nil)
	}

	if input.TaskName == "" {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "taskName is empty", nil, nil)
	}

	return ocommon.GenResultInfo(0, "success", nil, nil)
}

// 模拟validateManualTaskParams函数
func (t *testTaskCreateService) mockValidateManualTaskParams(input ManualTaskCreateInfo) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	if input.ManualAction != base.ACTION_UP && input.ManualAction != base.ACTION_DOWN {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "manualAction must be 'up' or 'down'", nil, nil)
	}

	if input.AdjustNum <= 0 {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "adjustNum must be greater than 0", nil, nil)
	}

	return ocommon.GenResultInfo(0, "success", nil, nil)
}

// 模拟validateModuleExists函数
func (t *testTaskCreateService) mockValidateModuleExists(moduleId int) (moduleInfo dao.Module, r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return dao.Module{}, ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	if t.moduleData.ID == 0 {
		return dao.Module{}, ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, "module does not exist", nil, nil)
	}

	return t.moduleData, ocommon.GenResultInfo(0, "success", nil, nil)
}

// 模拟checkTaskExists函数
func (t *testTaskCreateService) mockCheckTaskExists(moduleId int, taskType, idcTag string) (r ocommon.ResultInfo) {
	if t.shouldReturnError {
		return ocommon.GenResultInfo(t.errorCode, t.errorMessage, nil, nil)
	}

	if len(t.existingTasks) > 0 {
		return ocommon.GenResultInfo(errno.ERR_INVALID_INPUT, taskType+" task already exists for this module and idc", nil, nil)
	}

	return ocommon.GenResultInfo(0, "success", nil, nil)
}

// 模拟CreateManualTask函数
func (t *testTaskCreateService) mockCreateManualTask(input ManualTaskCreateInput) (r ocommon.ResultInfo) {
	// 参数验证
	r = t.mockValidateBasicTaskParams(input.TaskCreateInfo)
	if !r.IsOk() {
		return
	}

	r = t.mockValidateManualTaskParams(input.ManualTaskCreateInfo)
	if !r.IsOk() {
		return
	}

	// 验证模块是否存在
	_, r = t.mockValidateModuleExists(input.ModuleId)
	if !r.IsOk() {
		return
	}

	// 检查任务是否已存在
	r = t.mockCheckTaskExists(input.ModuleId, base.TASK_TYPE_MANUAL, input.IdcTag)
	if !r.IsOk() {
		return
	}

	// 模拟创建成功
	result := ManualTaskCreateResult{
		TaskId: t.createdTaskId,
		RuleId: t.createdRuleId,
	}

	return ocommon.GenResultInfo(0, "success", result, nil)
}

func TestValidateBasicTaskParams(t *testing.T) {
	tests := []struct {
		name    string
		input   TaskCreateInfo
		wantErr bool
	}{
		{
			name: "valid basic params",
			input: TaskCreateInfo{
				ModuleId:   1,
				ModuleName: "test-module",
				TaskName:   "test-task",
				IdcTag:     "hba",
				TaskDesc:   "test description",
			},
			wantErr: false,
		},
		{
			name: "invalid moduleId",
			input: TaskCreateInfo{
				ModuleId:   0,
				ModuleName: "test-module",
				TaskName:   "test-task",
				IdcTag:     "hba",
				TaskDesc:   "test description",
			},
			wantErr: true,
		},
		{
			name: "empty taskName",
			input: TaskCreateInfo{
				ModuleId:   1,
				ModuleName: "test-module",
				TaskName:   "",
				IdcTag:     "hba",
				TaskDesc:   "test description",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestTaskCreateService()
			result := testService.mockValidateBasicTaskParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

func TestValidateManualTaskParams(t *testing.T) {
	tests := []struct {
		name    string
		input   ManualTaskCreateInfo
		wantErr bool
	}{
		{
			name: "valid manual task params",
			input: ManualTaskCreateInfo{
				ManualAction:  base.ACTION_UP,
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
				StepCount:     1,
				StepInterval:  5,
			},
			wantErr: false,
		},
		{
			name: "invalid manual action",
			input: ManualTaskCreateInfo{
				ManualAction:  "invalid",
				AdjustNum:     5,
				BlockAutoTask: false,
				CooldownTime:  10,
				StepCount:     1,
				StepInterval:  5,
			},
			wantErr: true,
		},
		{
			name: "invalid adjust num",
			input: ManualTaskCreateInfo{
				ManualAction:  base.ACTION_UP,
				AdjustNum:     0,
				BlockAutoTask: false,
				CooldownTime:  10,
				StepCount:     1,
				StepInterval:  5,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestTaskCreateService()
			result := testService.mockValidateManualTaskParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

func TestValidateModuleExists(t *testing.T) {
	tests := []struct {
		name       string
		moduleId   int
		moduleData dao.Module
		wantErr    bool
	}{
		{
			name:     "valid module",
			moduleId: 1,
			moduleData: dao.Module{
				ID:          1,
				ServiceName: "test-service",
				ModuleType:  "web",
			},
			wantErr: false,
		},
		{
			name:       "module not found",
			moduleId:   1,
			moduleData: dao.Module{}, // 空模块表示不存在
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestTaskCreateService()
			testService.setModuleData(tt.moduleData)

			moduleInfo, result := testService.mockValidateModuleExists(tt.moduleId)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
				assert.Equal(t, tt.moduleData.ID, moduleInfo.ID)
				assert.Equal(t, tt.moduleData.ServiceName, moduleInfo.ServiceName)
			}
		})
	}
}

func TestCheckTaskExists(t *testing.T) {
	tests := []struct {
		name          string
		moduleId      int
		taskType      string
		idcTag        string
		existingTasks []dao.TaskInfo
		wantErr       bool
	}{
		{
			name:          "no existing task",
			moduleId:      1,
			taskType:      base.TASK_TYPE_MANUAL,
			idcTag:        "hba",
			existingTasks: []dao.TaskInfo{},
			wantErr:       false,
		},
		{
			name:     "task already exists",
			moduleId: 1,
			taskType: base.TASK_TYPE_MANUAL,
			idcTag:   "hba",
			existingTasks: []dao.TaskInfo{
				{
					ID:       1,
					ModuleId: 1,
					TaskType: base.TASK_TYPE_MANUAL,
					IdcTag:   "hba",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testService := newTestTaskCreateService()
			testService.setExistingTasks(tt.existingTasks)

			result := testService.mockCheckTaskExists(tt.moduleId, tt.taskType, tt.idcTag)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}

// TestCreateCrontabRules 测试不同schedType的规则创建逻辑
func TestCreateCrontabRules(t *testing.T) {
	testCases := []struct {
		name                 string
		schedType            string
		expectedScaleOutType string
		expectedScaleInType  string
	}{
		{
			name:                 "cronCron - 定时缩容_定时扩容",
			schedType:            base.TASK_CRONTAB_TYPE_CRON2,
			expectedScaleOutType: base.SCALING_RULE_TIME_TRIGGER,
			expectedScaleInType:  base.SCALING_RULE_TIME_TRIGGER,
		},
		{
			name:                 "eventCron - 事件缩容_定时扩容",
			schedType:            base.TASK_CRONTAB_TYPE_EVENT_CRON,
			expectedScaleOutType: base.SCALING_RULE_TIME_TRIGGER,
			expectedScaleInType:  base.SCALING_RULE_EVENT_TRIGGER,
		},
		{
			name:                 "cronEvent - 定时缩容_事件扩容",
			schedType:            base.TASK_CRONTAB_TYPE_CRON_EVENT,
			expectedScaleOutType: base.SCALING_RULE_EVENT_TRIGGER,
			expectedScaleInType:  base.SCALING_RULE_TIME_TRIGGER,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("Test case: %s", tc.name)
			t.Logf("SchedType: %s", tc.schedType)
			t.Logf("Expected ScaleOut Type: %s", tc.expectedScaleOutType)
			t.Logf("Expected ScaleIn Type: %s", tc.expectedScaleInType)

			// 验证schedType的逻辑
			switch tc.schedType {
			case base.TASK_CRONTAB_TYPE_CRON2:
				assert.Equal(t, base.SCALING_RULE_TIME_TRIGGER, tc.expectedScaleOutType)
				assert.Equal(t, base.SCALING_RULE_TIME_TRIGGER, tc.expectedScaleInType)
			case base.TASK_CRONTAB_TYPE_EVENT_CRON:
				assert.Equal(t, base.SCALING_RULE_TIME_TRIGGER, tc.expectedScaleOutType)
				assert.Equal(t, base.SCALING_RULE_EVENT_TRIGGER, tc.expectedScaleInType)
			case base.TASK_CRONTAB_TYPE_CRON_EVENT:
				assert.Equal(t, base.SCALING_RULE_EVENT_TRIGGER, tc.expectedScaleOutType)
				assert.Equal(t, base.SCALING_RULE_TIME_TRIGGER, tc.expectedScaleInType)
			}
		})
	}
}

// TestValidateCrontabTaskParams 测试定时任务参数验证
func TestValidateCrontabTaskParams(t *testing.T) {
	tests := []struct {
		name    string
		input   CrontabTaskCreateInfo
		wantErr bool
	}{
		{
			name: "valid crontab params - cronCron",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       3,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
				StepCount:       1,
				StepInterval:    5,
			},
			wantErr: false,
		},
		{
			name: "valid crontab params - eventCron",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_TRAFFIC,
				AdjustNum:       2,
				SchedType:       base.TASK_CRONTAB_TYPE_EVENT_CRON,
				CronProtectType: base.CRON_PROTECT_TYPE_PLAN,
				StepCount:       2,
				StepInterval:    10,
			},
			wantErr: false,
		},
		{
			name: "valid crontab params - cronEvent",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       5,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON_EVENT,
				CronProtectType: base.CRON_PROTECT_TYPE_NONE,
				StepCount:       1,
				StepInterval:    15,
			},
			wantErr: false,
		},
		{
			name: "invalid taskType",
			input: CrontabTaskCreateInfo{
				TaskType:        "invalid",
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       3,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
			},
			wantErr: true,
		},
		{
			name: "invalid scaleMode",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       "invalid",
				AdjustNum:       3,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
			},
			wantErr: true,
		},
		{
			name: "invalid adjustNum",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       0,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
			},
			wantErr: true,
		},
		{
			name: "invalid schedType",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       3,
				SchedType:       "invalid",
				CronProtectType: base.CRON_PROTECT_TYPE_SUGGEST,
			},
			wantErr: true,
		},
		{
			name: "invalid cronProtectType",
			input: CrontabTaskCreateInfo{
				TaskType:        base.TASK_TYPE_CRONTAB,
				ScaleMode:       base.SCALE_MODE_SCALE,
				AdjustNum:       3,
				SchedType:       base.TASK_CRONTAB_TYPE_CRON2,
				CronProtectType: "invalid",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateCrontabTaskParams(tt.input)

			if tt.wantErr {
				assert.False(t, result.IsOk(), "Expected error but got success")
			} else {
				assert.True(t, result.IsOk(), "Expected success but got error: %v", result)
			}
		})
	}
}
