package task

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/oquery"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"encoding/json"
	"fmt"
)

// RuleModelQuery 查询参数结构体
type RuleModelQuery struct {
	RuleId   int    `json:"ruleId"`
	Search   string `json:"search"`
	PageNum  int    `json:"pageNum"`
	PageSize int    `json:"pageSize"`
}

// RuleModelCreateInput 创建规则模型输入参数
type RuleModelCreateInput struct {
	Name           string            `json:"name" validate:"required"`
	MetricInfo     []base.MetricInfo `json:"metricInfo"`
	Period         int               `json:"period"`
	TriggerTimes   int               `json:"triggerTimes"`
	DisableScaleIn bool              `json:"disableScaleIn"`
	StepCount      int               `json:"stepCount"`
	StepInterval   int               `json:"stepInterval"`
}

// RuleModelModifyInput 修改规则模型输入参数
type RuleModelModifyInput struct {
	RuleId         int               `json:"ruleId" validate:"required"`
	Name           string            `json:"name"`
	MetricInfo     []base.MetricInfo `json:"metricInfo"`
	Period         int               `json:"period"`
	TriggerTimes   int               `json:"triggerTimes"`
	DisableScaleIn bool              `json:"disableScaleIn"`
	StepCount      int               `json:"stepCount"`
	StepInterval   int               `json:"stepInterval"`
}

// RuleModelListRes 分页查询返回结构体
type RuleModelListRes struct {
	PageInfo struct {
		PageNum  int `json:"pageNum"`
		PageSize int `json:"pageSize"`
		Total    int `json:"total"`
	} `json:"pageInfo"`
	RuleModelDetail []dao.RuleModel `json:"ruleModelDetail"`
}

// GetRuleModel 获取规则模型信息
// 当ruleId存在时，则仅返回该数据
// 若ruleId为0，则分页返回信息，查询rule_model表
func GetRuleModel(input RuleModelQuery) (r ocommon.ResultInfo) {
	// 当ruleId存在时，返回单条数据
	if input.RuleId > 0 {
		return getRuleModelById(input.RuleId)
	}

	// 当ruleId为0时，分页返回数据
	return getRuleModelList(input.Search, input.PageNum, input.PageSize)
}

// getRuleModelById 根据ID获取单条规则模型数据
func getRuleModelById(ruleId int) (r ocommon.ResultInfo) {
	var (
		daoRuleModelPtr = dao.CreateRuleModelPtr()
		ruleModelInfo   dao.RuleModel
	)

	r = daoRuleModelPtr.SearchByPk(&ruleModelInfo, ruleId)
	if !r.IsOk() {
		olog.Error("failed to get rule model from database, rule_id:[%d], err:[%v]", ruleId, r)
		return
	}

	r = ocommon.GenResultInfo(0, "success", ruleModelInfo, nil)
	return
}

// getRuleModelList 分页获取规则模型列表
func getRuleModelList(search string, pageNum, pageSize int) (r ocommon.ResultInfo) {
	// 设置默认分页参数
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	var (
		daoRuleModelPtr = dao.CreateRuleModelPtr()
		count           int64
	)

	// 构建查询条件
	query := oquery.NewQueryStructOfTable()
	if len(search) > 0 {
		query.AddConditonsByOperator("Name", oquery.OP_I_CONTAINS, search)
	}
	query.AddConditonsByOperator("Dflag", oquery.OP_EQUAL, 0) // 只查询未删除的数据
	query.AddOrders(oquery.Order{Name: "ID", IsASC: false})   // 按ID倒序排列

	// 获取总数
	count, r = daoRuleModelPtr.GetCountByQuery(query)
	if !r.IsOk() {
		olog.Error("failed to get rule model count from database, err:[%v]", r)
		return
	}

	// 设置分页信息
	query.SetPageInfo(int64(pageNum), int64(pageSize))

	// 查询数据
	var ruleModelSlice []dao.RuleModel
	r = daoRuleModelPtr.SearchByQuery(&ruleModelSlice, query)
	if !r.IsOk() {
		olog.Error("failed to get rule model list from database, err:[%v]", r)
		return
	}

	// 构建返回结果
	ruleModelListRes := RuleModelListRes{
		RuleModelDetail: ruleModelSlice,
	}
	ruleModelListRes.PageInfo.PageNum = pageNum
	ruleModelListRes.PageInfo.PageSize = pageSize
	ruleModelListRes.PageInfo.Total = int(count)

	r = ocommon.GenResultInfo(0, "success", ruleModelListRes, nil)
	return
}

// CreateRuleModel 创建规则模型
func CreateRuleModel(input RuleModelCreateInput) (r ocommon.ResultInfo) {
	// 参数验证
	r = validateRuleModelCreateParams(input)
	if !r.IsOk() {
		olog.Error("rule model create params validation failed, input:[%+v], err:[%v]", input, r)
		return
	}

	// 将 MetricInfo 数组序列化为 JSON 字符串
	var metricInfoJson string
	if len(input.MetricInfo) > 0 {
		metricBytes, err := json.Marshal(input.MetricInfo)
		if err != nil {
			olog.Error("failed to marshal metric info, metricInfo:[%+v], err:[%v]", input.MetricInfo, err)
			r = ocommon.GenResultInfo(500, "failed to serialize metric info", nil, nil)
			return
		}
		metricInfoJson = string(metricBytes)
	}

	// 构建数据库记录
	ruleModel := dao.RuleModel{
		Name:           input.Name,
		MetricInfo:     metricInfoJson,
		Period:         input.Period,
		TriggerTimes:   input.TriggerTimes,
		DisableScaleIn: input.DisableScaleIn,
		StepCount:      input.StepCount,
		StepInterval:   input.StepInterval,
		Dflag:          0, // 未删除状态
	}

	// 插入数据库
	var daoRuleModelPtr = dao.CreateRuleModelPtr()
	ruleId, r := daoRuleModelPtr.Insert(&ruleModel)
	if !r.IsOk() {
		olog.Error("failed to insert rule model to database, ruleModel:[%+v], err:[%v]", ruleModel, r)
		return
	}

	olog.Info("rule model created successfully, ruleId:[%d], input:[%+v]", ruleId, input)
	r = ocommon.GenResultInfo(0, "success", map[string]interface{}{"ruleId": ruleId}, nil)
	return
}

// validateRuleModelCreateParams 验证创建规则模型的参数
func validateRuleModelCreateParams(input RuleModelCreateInput) (r ocommon.ResultInfo) {
	// 验证必填字段
	if input.Name == "" {
		r = ocommon.GenResultInfo(400, "name is required", nil, nil)
		return
	}

	// 验证数值范围
	if input.Period < 0 {
		r = ocommon.GenResultInfo(400, "period must be non-negative", nil, nil)
		return
	}

	if input.TriggerTimes < 0 {
		r = ocommon.GenResultInfo(400, "triggerTimes must be non-negative", nil, nil)
		return
	}

	if input.StepCount < 0 {
		r = ocommon.GenResultInfo(400, "stepCount must be non-negative", nil, nil)
		return
	}

	if input.StepInterval < 0 {
		r = ocommon.GenResultInfo(400, "stepInterval must be non-negative", nil, nil)
		return
	}

	// 验证 MetricInfo 数组
	if len(input.MetricInfo) == 0 {
		r = ocommon.GenResultInfo(400, "metricInfo is required and cannot be empty", nil, nil)
		return
	}

	// 验证每个 MetricInfo 的字段
	for i, metric := range input.MetricInfo {
		if metric.MetricName == "" {
			r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].metricName is required", i), nil, nil)
			return
		}
		if metric.TargetRateMax < 0 || metric.TargetRateMax > 1 {
			r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].targetRateMax must be between 0 and 1", i), nil, nil)
			return
		}
		if metric.TargetRateMin < 0 || metric.TargetRateMin > 1 {
			r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].targetRateMin must be between 0 and 1", i), nil, nil)
			return
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// validateRuleModelModifyParams 验证修改规则模型的参数
func validateRuleModelModifyParams(input RuleModelModifyInput) (r ocommon.ResultInfo) {
	// 验证必填字段
	if input.RuleId <= 0 {
		r = ocommon.GenResultInfo(400, "ruleId is required and must be greater than 0", nil, nil)
		return
	}

	// 验证数值范围（只验证非零值）
	if input.Period < 0 {
		r = ocommon.GenResultInfo(400, "period must be non-negative", nil, nil)
		return
	}

	if input.TriggerTimes < 0 {
		r = ocommon.GenResultInfo(400, "triggerTimes must be non-negative", nil, nil)
		return
	}

	if input.StepCount < 0 {
		r = ocommon.GenResultInfo(400, "stepCount must be non-negative", nil, nil)
		return
	}

	if input.StepInterval < 0 {
		r = ocommon.GenResultInfo(400, "stepInterval must be non-negative", nil, nil)
		return
	}

	// 验证 MetricInfo 数组（如果提供）
	if len(input.MetricInfo) > 0 {
		for i, metric := range input.MetricInfo {
			if metric.MetricName == "" {
				r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].metricName is required", i), nil, nil)
				return
			}
			if metric.TargetRateMax < 0 || metric.TargetRateMax > 1 {
				r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].targetRateMax must be between 0 and 1", i), nil, nil)
				return
			}
			if metric.TargetRateMin < 0 || metric.TargetRateMin > 1 {
				r = ocommon.GenResultInfo(400, fmt.Sprintf("metricInfo[%d].targetRateMin must be between 0 and 1", i), nil, nil)
				return
			}
		}
	}

	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// ModifyRuleModel 修改规则模型
func ModifyRuleModel(input RuleModelModifyInput) (r ocommon.ResultInfo) {
	// 参数验证
	r = validateRuleModelModifyParams(input)
	if !r.IsOk() {
		olog.Error("rule model modify params validation failed, input:[%+v], err:[%v]", input, r)
		return
	}

	// 检查规则是否存在
	var daoRuleModelPtr = dao.CreateRuleModelPtr()
	var existingRule dao.RuleModel
	r = daoRuleModelPtr.SearchByPk(&existingRule, input.RuleId)
	if !r.IsOk() {
		olog.Error("failed to get rule model from database, ruleId:[%d], err:[%v]", input.RuleId, r)
		return
	}

	// 构建更新字段列表和更新数据
	var updateFields []string
	updateData := dao.RuleModel{ID: input.RuleId}

	// 只更新非零值字段
	if input.Name != "" {
		updateData.Name = input.Name
		updateFields = append(updateFields, "Name")
	}

	if len(input.MetricInfo) > 0 {
		// 将 MetricInfo 数组序列化为 JSON 字符串
		metricBytes, err := json.Marshal(input.MetricInfo)
		if err != nil {
			olog.Error("failed to marshal metric info, metricInfo:[%+v], err:[%v]", input.MetricInfo, err)
			r = ocommon.GenResultInfo(500, "failed to serialize metric info", nil, nil)
			return
		}
		updateData.MetricInfo = string(metricBytes)
		updateFields = append(updateFields, "MetricInfo")
	}

	if input.Period > 0 {
		updateData.Period = input.Period
		updateFields = append(updateFields, "Period")
	}

	if input.TriggerTimes > 0 {
		updateData.TriggerTimes = input.TriggerTimes
		updateFields = append(updateFields, "TriggerTimes")
	}

	// DisableScaleIn 是布尔值，需要特殊处理
	updateData.DisableScaleIn = input.DisableScaleIn
	updateFields = append(updateFields, "DisableScaleIn")

	if input.StepCount > 0 {
		updateData.StepCount = input.StepCount
		updateFields = append(updateFields, "StepCount")
	}

	if input.StepInterval > 0 {
		updateData.StepInterval = input.StepInterval
		updateFields = append(updateFields, "StepInterval")
	}

	// 如果没有字段需要更新
	if len(updateFields) == 1 { // 只有 DisableScaleIn 字段
		r = ocommon.GenResultInfo(400, "no fields to update", nil, nil)
		return
	}

	// 执行更新
	_, r = daoRuleModelPtr.UpdateByPk(&updateData, updateFields, input.RuleId)
	if !r.IsOk() {
		olog.Error("failed to update rule model in database, ruleId:[%d], updateData:[%+v], err:[%v]", input.RuleId, updateData, r)
		return
	}

	olog.Info("rule model updated successfully, ruleId:[%d], updateFields:[%v], input:[%+v]", input.RuleId, updateFields, input)
	r = ocommon.GenResultInfo(0, "success", map[string]interface{}{"ruleId": input.RuleId}, nil)
	return
}

// DeleteRuleModel 删除规则模型（软删除）
func DeleteRuleModel(ruleId int) (r ocommon.ResultInfo) {
	// 参数验证
	if ruleId <= 0 {
		r = ocommon.GenResultInfo(400, "ruleId is required and must be greater than 0", nil, nil)
		return
	}

	// 检查规则是否存在
	var daoRuleModelPtr = dao.CreateRuleModelPtr()
	var existingRule dao.RuleModel
	r = daoRuleModelPtr.SearchByPk(&existingRule, ruleId)
	if !r.IsOk() {
		olog.Error("failed to get rule model from database, ruleId:[%d], err:[%v]", ruleId, r)
		return
	}

	// 检查是否已经被删除
	if existingRule.Dflag == 1 {
		r = ocommon.GenResultInfo(400, "rule model has already been deleted", nil, nil)
		return
	}

	// 执行软删除（设置 Dflag = 1）
	updateData := dao.RuleModel{
		ID:    ruleId,
		Dflag: 1,
	}
	updateFields := []string{"Dflag"}

	_, r = daoRuleModelPtr.UpdateByPk(&updateData, updateFields, ruleId)
	if !r.IsOk() {
		olog.Error("failed to delete rule model in database, ruleId:[%d], err:[%v]", ruleId, r)
		return
	}

	olog.Info("rule model deleted successfully, ruleId:[%d]", ruleId)
	r = ocommon.GenResultInfo(0, "success", map[string]interface{}{"ruleId": ruleId}, nil)
	return
}
