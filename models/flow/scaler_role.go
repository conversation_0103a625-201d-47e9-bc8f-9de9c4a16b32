package scaler_flow

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	flow_center "dxm/siod-cloud/go-common-lib/user-center/flow-center"
	"dxm/siod_sre/auto-scaler/dao"
	"strings"
	"time"
)

// ChooseNoahRole noah权限审批时动态获取审批角色
func ChooseScalerOPRole(listID float64, role string) (r ocommon.ResultInfo) {

	olog.Info("ChooseScalerOPRole, listID:%+v, role:%+v", listID, role)

	listId := int(listID)
	scalerRecordInfo, r := getScalerRecordById(listId)
	if !r.IsDataNotEmpty("No listId:%+v", listId) {
		return
	}

	moduleInfo, r := getModuleById(scalerRecordInfo.ModuleId)
	if !r.<PERSON>() {
		olog.Error("failed to get module info from database, listId:[%d], err:[%v]", listID, r)
		return
	}

	ownerList := strings.Split(moduleInfo.OpOwnerList, ",")

	var roleNameList []string
	var roleConditionList []string
	for _, user := range ownerList {
		roleNameList = append(roleNameList, "uuap@"+user)
		roleConditionList = append(roleConditionList, "[\""+"uuap@"+user+"\"]")
	}
	r.Data = map[string]string{
		"nodeRole":      strings.Join(roleNameList, ","),
		"nodeCondition": "[" + strings.Join(roleConditionList, ",") + "]",
	}
	return r
}

func ChooseScalerRDRole(listID float64, role string) (r ocommon.ResultInfo) {
	olog.Info("ChooseScalerRDRole, listID:%+v, role:%+v", listID, role)
	listId := int(listID)
	scalerRecordInfo, r := getScalerRecordById(listId)
	if !r.IsDataNotEmpty("No listId:%+v", listId) {
		return
	}

	moduleInfo, r := getModuleById(scalerRecordInfo.ModuleId)
	if !r.IsOk() {
		olog.Error("failed to get module info from database, listId:[%d], err:[%v]", listID, r)
		return
	}

	ownerList := strings.Split(moduleInfo.RdOwnerList, ",")

	var roleNameList []string
	var roleConditionList []string
	for _, user := range ownerList {
		roleNameList = append(roleNameList, "uuap@"+user)
		roleConditionList = append(roleConditionList, "[\""+"uuap@"+user+"\"]")
	}
	r.Data = map[string]string{
		"nodeRole":      strings.Join(roleNameList, ","),
		"nodeCondition": "[" + strings.Join(roleConditionList, ",") + "]",
	}
	return r
}

func getScalerRecordById(key int) (dataInfo dao.ScalerFlow, r ocommon.ResultInfo) {
	var daoPtr = dao.CreateScalerRecordPtr()
	r = daoPtr.SearchByPk(&dataInfo, key)
	return
}

func getModuleById(key int) (dataInfo dao.Module, r ocommon.ResultInfo) {
	r = dao.CreateScalerRecordPtr().SearchByPk(&dataInfo, key)
	return
}

func AutoSkipOPRoleAuth(flowInstanceID, nodeInstanceID, listId int, listType string,
	nodeSubmitInfo, nodeProcessInfo map[string]string) {

	olog.Info("AutoSkipOPRoleAuth, flowInstanceID:[%d], nodeInstanceID:[%d], listId:[%d], listType:[%s]", flowInstanceID, nodeInstanceID, listId, listType)
	var r ocommon.ResultInfo
	// 1、获取操作单信息

	// 2、根据操作单信息，以及之前节点的返回信息判断该节点是否可以跳过

	var result string
	// if token.Token != "" {
	// 	result = "pass"
	// } else {
	// 	result = "ignore"
	// }
	result = "ignore"

	// 3、返回结果（pass或ignore） r.Data必填字段：flowInstanceId，nodeInstanceId，nodeResult
	r.Data = map[string]interface{}{
		"flowInstanceId": flowInstanceID,
		"nodeInstanceId": nodeInstanceID,
		"nodeResult":     result,
	}
	processResult := flow_center.ProcessResult{
		FlowInstanceId: flowInstanceID,
		NodeInstanceId: nodeInstanceID,
		CallbackMsg: map[string]interface{}{
			"nodeResult": result,
		},
		Result: r,
	}
	//r = flow_center.AddProcessResultToFlow(processResult)
	for i := 0; i < 3; i++ {
		r = flow_center.AddProcessResultToFlow(processResult)
		if r.IsOk() {
			break
		}
		time.Sleep(1 * time.Second)
	}

	switch result {
	case "pass":
		// 通告业务侧扩容
	case "ignore":
		// do nothing
	}
	return
}

func AutoSkipRDRoleAuth(flowInstanceID, nodeInstanceID, listId int, listType string,
	nodeSubmitInfo, nodeProcessInfo map[string]string) {

	olog.Info("AutoSkipRDRoleAuth, flowInstanceID:[%d], nodeInstanceID:[%d], listId:[%d], listType:[%s]", flowInstanceID, nodeInstanceID, listId, listType)
	var r ocommon.ResultInfo
	// 1、获取操作单信息

	// 2、根据操作单信息，以及之前节点的返回信息判断该节点是否可以跳过

	var result string
	// if token.Token != "" {
	// 	result = "pass"
	// } else {
	// 	result = "ignore"
	// }
	result = "pass"

	// 3、返回结果（pass或ignore） r.Data必填字段：flowInstanceId，nodeInstanceId，nodeResult
	r.Data = map[string]interface{}{
		"flowInstanceId": flowInstanceID,
		"nodeInstanceId": nodeInstanceID,
		"nodeResult":     result,
	}
	processResult := flow_center.ProcessResult{
		FlowInstanceId: flowInstanceID,
		NodeInstanceId: nodeInstanceID,
		CallbackMsg: map[string]interface{}{
			"nodeResult": result,
		},
		Result: r,
	}
	//r = flow_center.AddProcessResultToFlow(processResult)
	for i := 0; i < 3; i++ {
		r = flow_center.AddProcessResultToFlow(processResult)
		if r.IsOk() {
			break
		}
		time.Sleep(1 * time.Second)
	}

	switch result {
	case "pass":
		// 通告业务侧扩容
	case "ignore":
		// do nothing
	}

	return
}
