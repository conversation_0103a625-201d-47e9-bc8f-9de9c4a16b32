package scaler_flow

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/dao"
)

// listId为记录表主键ID，listType为记录表中flowType
func GetModelListDetail(listId int, listType string) (dataList map[string]interface{}, r ocommon.ResultInfo) {
	// 查询数据
	var recordList []dao.ScalerFlow
	r = dao.CreateScalerRecordPtr().SearchByPk(&recordList, listId)
	if !r.IsOk() {
		olog.Error("failed to get scaler record by id: %v, error: %v", listId, r)
		return
	}
	if len(recordList) == 0 {
		olog.Error("failed to get scaler record by id: %v, no record found", listId)
		return
	}

	// 校验数据
	dataList = make(map[string]interface{})
	dataList["moduleName"] = "40482-1001"
	dataList["action"] = "扩容"
	dataList["currInsNum"] = 10
	dataList["expectedInsNum"] = 12
	dataList["changeNum"] = 2
	dataList["decisionInfo"] = "容量决策数据不足，需要扩容2个"
	return
}
