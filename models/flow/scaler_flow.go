package scaler_flow

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/dao"
	"encoding/json"
	"fmt"
	"time"

	"dxm/siod-cloud/go-common-lib/olog"
	flow_center "dxm/siod-cloud/go-common-lib/user-center/flow-center"

	"dxm/siod-cloud/go-common-lib/otool"
)

const (
	// 流程名称
	FLOW_NAME_AUTO_SCALER_OUT = "auto_scaler_out" //扩容流程
	FLOW_NAME_AUTO_SCALER_IN  = "auto_scaler_in"  //缩容流程

	// 流程任务类型
	FLOW_TYPE_SCALE_OUT = "auto_scaler_out" // 横向扩容
	FLOW_TYPE_SCALE_IN  = "auto_scaler_in"  // 横向缩容
)

var (
	// 流程状态
	FlowStatusDone      = 0
	FlowStatusInit      = 1
	FlowStatusOPConfirm = 2
	FlowStatusRDConfirm = 3
	FlowStatusRun       = 4
)

type ExpansionFlowInfo struct {
	ModuleId       int    `json:"moduleId"`
	ModuleName     string `json:"moduleName"`
	CurrInsNum     int    `json:"currInsNum"`
	ExpectedInsNum int    `json:"expectedInsNum"`
	ChangeNum      int    `json:"changeNum"`
	DecisionInfo   string `json:"decisionInfo"`
	Applicant      string `json:"applicant"`
}

func CreateExpansionFlow(inputData ExpansionFlowInfo) (r ocommon.ResultInfo) {

	dataJ, _ := json.Marshal(&inputData)

	taskRegInfo := &dao.ScalerFlow{
		Applicant:  inputData.Applicant,
		FlowName:   FLOW_NAME_AUTO_SCALER_OUT,
		Status:     FlowStatusInit,
		CreateTime: time.Now(),
		FlowType:   FLOW_TYPE_SCALE_OUT,
		FlowArgs:   string(dataJ),
	}

	daoPtr := dao.CreateScalerRecordPtr()
	taskRegId, r := daoPtr.Insert(taskRegInfo)
	if !r.IsOk() {
		olog.Error("failed to insert scaler record, err:[%v], data:[%s]", r, string(dataJ))
		return
	}

	flowCreate := flow_center.FlowCreate{
		ListId:    taskRegId,
		ListType:  FLOW_TYPE_SCALE_OUT,
		FlowName:  FLOW_NAME_AUTO_SCALER_OUT,
		Applicant: inputData.Applicant,
		FlowIndex: flow_center.FlowIndexType{
			FlowSubmitter: inputData.Applicant,
		},
		FlowIndexColumn: []string{"FlowSubmitter"},
		//增加角色选择，暂未实现
	}
	r = flow_center.AddListToFlow(flowCreate)
	return
}

func AutoScalerOutRun(flowInstanceID, nodeInstanceID, registrationID int, registrationType string,
	nodeSubmitInfo, nodeProcessInfo map[string]string) (r ocommon.ResultInfo) {
	olog.Info("AutoScalerOutRun, flowInstanceID:%d, nodeInstanceID:%d, registrationID:%d, registrationType:%s, nodeSubmitInfo:%+v",
		flowInstanceID, nodeInstanceID, registrationID, registrationType, nodeSubmitInfo, nodeProcessInfo)
	//1、运行
	//1.1 调用任务执行
	// 调用执行引擎，执行相应任务

	oneLog := flow_center.LogInfo{
		StartTime: time.Now().Format(otool.TIME_FORMAT_STR),
		Operator:  "[AUTO]",
		Operation: "执行扩容任务",
		Result:    "执行扩容任务成功",
	}
	flow_center.AddLogToFlow(flowInstanceID, nodeInstanceID, oneLog)

	//2、返回结果
	time.Sleep(time.Second * 5)

	var recordInfo ExpansionFlowInfo
	scalerRecordInfo, r := getScalerRecordById(registrationID)
	json.Unmarshal([]byte(scalerRecordInfo.FlowArgs), &recordInfo)

	var msg, content, message string
	switch scalerRecordInfo.FlowType {
	case FLOW_TYPE_SCALE_OUT:
		msg = "扩容任务执行成功"
		content = fmt.Sprintf("扩容数量：%d，当前实例数：%d", recordInfo.ChangeNum, recordInfo.CurrInsNum)
		message = fmt.Sprintf("<br>任务结果： %+v, 运行信息：%+v", msg, content)
	case FLOW_TYPE_SCALE_IN:
		msg = "缩容任务执行成功"
		content = fmt.Sprintf("缩容数量：%d，当前实例数：%d", recordInfo.ChangeNum, recordInfo.CurrInsNum)
		message = fmt.Sprintf("<br>任务结果： %+v, 运行信息：%+v", msg, content)
	}

	processResult := flow_center.ProcessResult{
		FlowInstanceId: flowInstanceID,
		NodeInstanceId: nodeInstanceID,
		CallbackMsg: map[string]interface{}{
			"Subject": "[弹性伸缩通知] 扩容任务成功",
			"Message": message,
		},
		Result: r,
	}
	r = flow_center.AddProcessResultToFlow(processResult)
	return
}
