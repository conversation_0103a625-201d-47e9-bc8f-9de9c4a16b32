package comm

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	auth_center "dxm/siod-cloud/go-common-lib/user-center/auth-center"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"encoding/json"
	"slices"
)

func CheckUserAuthRole(username string) (r ocommon.ResultInfo) {
	manager, _ := auth_center.GetUsersByRole("auto-scaler-manager", auth_center.AUTH_TYPE_LOCAL)
	internalAuditor, _ := auth_center.GetUsersByRole("sre_engineer", auth_center.AUTH_TYPE_LOCAL)
	if slices.Contains(manager, username) || slices.Contains(internalAuditor, username) || username == "liupanpan_dxm" {
		return
	}

	r.ErrNo = errno.ERR_PERMISSON_NOT_EXIST
	r.ErrMsg = errno.ErrnoMap[errno.ERR_PERMISSON_NOT_EXIST]
	olog.Info("Operation not permitted, username:[%+v]", username)
	return
}

func OperateRecordUser(username, operate string, context interface{}) {
	contextJson, _ := json.Marshal(&context)
	dao.CreateOperateRecordPtr().RecordUserOperate(username, operate, string(contextJson))
}
