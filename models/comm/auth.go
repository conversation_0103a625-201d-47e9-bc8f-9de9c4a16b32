package comm

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	auth_center "dxm/siod-cloud/go-common-lib/user-center/auth-center"
	"dxm/siod_sre/auto-scaler/errno"
	"slices"
)

func CheckUserAuthRole(username string) (r ocommon.ResultInfo) {
	manager, _ := auth_center.GetUsersByRole("auto-scaler-manager", auth_center.AUTH_TYPE_LOCAL)
	internalAuditor, _ := auth_center.GetUsersByRole("sre_engineer", auth_center.AUTH_TYPE_LOCAL)
	if slices.Contains(manager, username) || slices.Contains(internalAuditor, username) || username == "liupanpan_dxm" {
		return
	}

	r.ErrNo = errno.ERR_PERMISSON_NOT_EXIST
	r.ErrMsg = errno.ErrnoMap[errno.ERR_PERMISSON_NOT_EXIST]
	olog.Info("Operation not permitted, username:[%+v]", username)
	return
}
