package comm

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"time"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	base_tool "dxm/siod_sre/auto-scaler/base/tool"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
)

type TagIns struct {
	Tag      string `json:"tag"`
	AvailNum int    `json:"availNum"`
	TotalNum int    `json:"totalNum"`
}

func GetOnlineIns(service, moduleType, idcTag string) (tagInsList []TagIns, r ocommon.ResultInfo) {
	switch moduleType {
	case base.MODULE_TYPE_MODEL:
		tagInsList, r = GetModelInstance(service, idcTag)

	case base.MODULE_TYPE_POD:
		tagInsList, r = GetServiceInstance(service, idcTag)
	}

	return tagInsList, r
}

func GetModelInstance(service, idcTag string) (tagInsList []TagIns, r ocommon.ResultInfo) {
	var (
		moduleList []dao_ocean.NoahModules
		tagNumMap  = make(map[string]TagIns, 2)
	)

	// 获取容量库信息
	moduleList, r = dao_ocean.CreateNoahModulesPtr().GetRiskModelList(service)
	if !r.IsOk() {
		olog.Error("failed to get ocean module info, service:%s, err:%v", service, r)
		return
	}
	if len(moduleList) == 0 {
		r = ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, "no ocean module info", nil, nil)
		return
	}

	serviceOcean := dao_ocean.OceanDetailSearchLatestBetweenTime(
		moduleList[0].Product,
		moduleList[0].BnsName,
		base_tool.GetLastMinuteByTime(time.Now()).Format(otool.TIME_FORMAT_STR),
		time.Now().Format(otool.TIME_FORMAT_STR))

	for _, v := range serviceOcean {
		tagNumMap[v.LogicIdc] = TagIns{
			Tag:      v.LogicIdc,
			AvailNum: int(v.InsCount),
			TotalNum: int(v.InsCount),
		}
	}

	for _, tagInfo := range tagNumMap {
		// 当传入的idcTag为空，则返回所有的信息，当不为空，则返回适配的数据
		if idcTag == "" || tagInfo.Tag == idcTag {
			tagInsList = append(tagInsList, tagInfo)
		}
	}

	return
}

func GetServiceInstance(service, idcTag string) (tagInsList []TagIns, r ocommon.ResultInfo) {
	var (
		tagNumMap = make(map[string]TagIns, 2)
	)

	insList, err := apptree.GetInstanceByBNSWithDisabledV2(service)
	if err != nil {
		olog.Error("failed to get instance from apptree, err:%v", err)
		r = ocommon.GenResultInfo(errno.ERR_INVALID_RESULT, "failed to get instance from apptree", nil, err)
		return
	}

	for _, ins := range insList {
		var useNum, availNum = 1, 1
		if ins.Disable == 1 || ins.Status != 0 {
			availNum = 0
		}

		if _, ok := tagNumMap[ins.Tags["idc"]]; !ok {
			tagNumMap[ins.Tags["idc"]] = TagIns{
				Tag:      ins.Tags["idc"],
				AvailNum: availNum,
				TotalNum: useNum,
			}
			continue
		}

		tagInsNum := tagNumMap[ins.Tags["idc"]]
		tagNumMap[ins.Tags["idc"]] = TagIns{
			Tag:      ins.Tags["idc"],
			AvailNum: tagInsNum.AvailNum + availNum,
			TotalNum: tagInsNum.TotalNum + useNum,
		}
	}

	for _, tagInfo := range tagNumMap {
		// 当传入的idcTag为空，则返回所有的信息，当不为空，则返回适配的数据
		if idcTag == "" || tagInfo.Tag == idcTag {
			tagInsList = append(tagInsList, tagInfo)
		}
	}

	return
}
