package dao_ocean

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"time"

	"github.com/astaxie/beego/orm"
)

// 逻辑生命周期控制表
const (
	TABLENAME_RISK_MODEL_RESOURCE = "risk_model_resource"
	RISK_MODEL_RESOURCE_KEY       = "ID"
)

type RiskModelResource struct {
	ID             int        `json:"id" orm:"column(id);auto;pk" description:"自增主键"`
	Host           string     `json:"host" orm:"column(host)" description:"主机"`
	IdcTag         string     `json:"idcTag" orm:"column(idc_tag)" description:"机房"`
	FreeCores      int        `json:"freeCores" orm:"column(free_cores)" description:"可用核数"`
	UsedCores      int        `json:"usedCores" orm:"column(used_cores)" description:"已使用核数"`
	ShareModelNum  int        `json:"shareModelNum" orm:"column(share_model_num)" description:"共享模型数"`
	DFlag          int        `json:"dflag" diff:"dflag" orm:"column(dflag);size(4);default(0)" description:"软删标记"`
	LastModifyTime time.Time  `json:"lastModifyTime" diff:"last_modify_time" orm:"column(last_modify_time);type(datetime);auto_now" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type RiskModelResourceBuilder struct {
	dbBase.DbBase
}

func CreateRiskModelResourcePtr() *RiskModelResourceBuilder {
	return &RiskModelResourceBuilder{dbBase.DbBase{Tablename: TABLENAME_RISK_MODEL_RESOURCE, PrimaryKey: RISK_MODEL_RESOURCE_KEY, PtrDaoStruct: &RiskModelResource{}}}
}
