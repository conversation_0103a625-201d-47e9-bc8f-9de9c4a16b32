package dao_ocean

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/errno"
	"encoding/json"
	"fmt"

	"github.com/astaxie/beego/orm"
)

const (
	PRIMARY_KEY_OCEAN_DETAIL = "ID"
)

type OceanDetail struct {
	Id            int     `orm:"column(id);auto;pk" description:"ID"`
	BnsName       string  `orm:"column(bns_name)" description:"BNS"`
	LogicIdc      string  `orm:"column(logic_idc)" description:"逻辑机房"`
	DeployType    string  `orm:"column(deploy_type)" description:"部署类型 默认空为机器，pod为容器"`
	InsCount      int     `orm:"column(ins_count)" description:"实例数"`
	Desc          string  `orm:"column(desc)" description:"资源池描述"`
	CpuAvg        float64 `orm:"column(cpu_avg)" description:"cpu"`
	CpuMax        float64 `orm:"column(cpu_max)" description:"cpu"`
	CpuMin        float64 `orm:"column(cpu_min)" description:"cpu"`
	CpuStd        float64 `orm:"column(cpu_std)" description:"cpu"`
	QpsAvg        float64 `orm:"column(qps_avg)" description:"qps"`
	QpsMax        float64 `orm:"column(qps_max)" description:"qps"`
	QpsMin        float64 `orm:"column(qps_min)" description:"qps"`
	QpsStd        float64 `orm:"column(qps_std)" description:"qps"`
	CostAvg       float64 `orm:"column(cost_avg)" description:"cost"`
	CostMax       float64 `orm:"column(cost_max)" description:"cost"`
	CostMin       float64 `orm:"column(cost_min)" description:"cost"`
	CostStd       float64 `orm:"column(cost_std)" description:"cost"`
	MonitorAvg    float64 `orm:"column(monitor_avg)" description:"监控的CPU平均值"`
	ServiceAppAvg float64 `orm:"column(service_app_avg)" description:"业务容器CPU平均值"`
	MemAvg        float64 `orm:"column(mem_avg)" description:"mem"`
	Level         float64 `orm:"column(level)" description:"水位"`
	QpsCpuWeight  float64 `orm:"column(qps_cpu_weight)" description:"qps、cpu权重"`
	InsertTime    string  `orm:"column(insert_time)" description:"插入时间"`
}

type OceanDetailBuilder struct {
	dbBase.DbBase
}

func GetDetailTableName(product string) (table string) {
	table = fmt.Sprintf("noah_%s", product)
	return table
}

func GetOceanDetailByCycle(product, bns, idc_tag string, cycle int) (detailList []OceanDetail, r ocommon.ResultInfo) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE `bns_name`='%s' AND logic_idc = '%s' ORDER BY `insert_time` DESC LIMIT %d ",
		table, bns, idc_tag, cycle)
	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&detailList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get ocean detail from database, bns:[%s], idc_tag:[%s], retry:[%d], err:[%v]", bns, idc_tag, retry, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get ocean detail from database, bns:[%s], idc_tag:[%s], retry:[%d], err:[%v]", bns, idc_tag, retry, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	debugData, _ := json.Marshal(&detailList)
	olog.Debug("get ocean detail info from database, info:[%s]", string(debugData))
	return
}

func GetOceanDetailBySql(sql string) (detailList []OceanDetail, r ocommon.ResultInfo) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}

	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&detailList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get ocean detail from database, sql:[%s], retry:[%d], err:[%v]", sql, retry, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get ocean detail from database, sql:[%s], retry:[%d], err:[%v]", sql, retry, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	debugData, _ := json.Marshal(&detailList)
	olog.Debug("get ocean detail info from database, info:[%s]", string(debugData))
	return
}

func OceanDetailSearchLatest20(product string, bns string) (detailList []OceanDetail) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE `bns_name`='%s' ORDER BY `insert_time` DESC LIMIT 20", table, bns)
	o.Raw(sql).QueryRows(&detailList)
	return detailList
}
func OceanDetailSearchLatestByEndTime(product string, bns string, end string) (detailList []OceanDetail) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("SELECT * FROM(SELECT * FROM `%s` WHERE `bns_name`='%s' AND `insert_time` < '%s' ORDER BY `insert_time` DESC LIMIT 10)t GROUP BY `logic_idc`", table, bns, end)
	o.Raw(sql).QueryRows(&detailList)
	return detailList
}
func OceanDetailSearchLatestBetweenTime(product string, bns string, start string, end string) (detailList []OceanDetail) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("SELECT * FROM(SELECT * FROM `%s` WHERE `bns_name`='%s' AND `insert_time` BETWEEN '%s' AND '%s' ORDER BY `insert_time` DESC LIMIT 10)t GROUP BY `logic_idc`", table, bns, start, end)
	o.Raw(sql).QueryRows(&detailList)
	return detailList
}
func OceanDetailSearch(product string, bns string, start string, end string) (detailList []OceanDetail) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	condition := fmt.Sprintf("`bns_name`='%s' AND `insert_time` BETWEEN '%s' AND '%s'", bns, start, end)
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE %s", table, condition)
	o.Raw(sql).QueryRows(&detailList)
	return detailList
}

func OceanDetailSearchByTag(product string, bns string, logicIdc string, start string, end string) (detailList []OceanDetail) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	condition := fmt.Sprintf("`bns_name`='%s' AND logic_idc = '%s' AND `insert_time` BETWEEN '%s' AND '%s'", bns, logicIdc, start, end)
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE %s", table, condition)
	o.Raw(sql).QueryRows(&detailList)
	return detailList
}
func OceanDetailSearchLimit(product string, bns string, start string, end string, limit int) (detailList []OceanDetail, err error) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	condition := fmt.Sprintf("`bns_name`='%s' AND `insert_time` BETWEEN '%s' AND '%s' limit %d", bns, start, end, limit)
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE %s", table, condition)
	_, err = o.Raw(sql).QueryRows(&detailList)
	return
}

func OceanDetailInsert(product string, d OceanDetail) (err error) {
	o := orm.NewOrm()
	//o, err := getDBOcapXdbOrmer()
	//if err != nil {
	//	olog.Error("failed to get ocap_x ormer, err:[%v]", err)
	//	return
	//}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("insert into %s(`bns_name`, `logic_idc`, `deploy_type`, `ins_count`, "+
		" `cpu_avg`, `cpu_max`, `cpu_min`, `cpu_std`, "+
		" `qps_avg`, `qps_max`, `qps_min`, `qps_std`, "+
		" `cost_avg`, `cost_max`, `cost_min`, `cost_std`, "+
		" `mem_avg`, `level`, `insert_time`) values('%s', '%s', '%s', '%d', "+
		" '%.2f', '%.2f', '%.2f', '%.2f', "+
		" '%.2f', '%.2f', '%.2f', '%.2f', "+
		" '%.2f', '%.2f', '%.2f', '%.2f', "+
		"'%.2f', '%.2f', '%s')", table,
		d.BnsName, d.LogicIdc, d.DeployType, d.InsCount,
		d.CpuAvg, d.CpuMax, d.CpuMin, d.CpuStd,
		d.QpsAvg, d.QpsMax, d.QpsMin, d.QpsStd,
		d.CostAvg, d.CostMax, d.CostMin, d.CostStd,
		d.MemAvg, d.Level, d.InsertTime)

	_, err = o.Raw(sql).Exec()
	return
}

func OceanComparisonQuery(product, bns, idc, start, end string) (detailList []OceanDetail, err error) {
	o, err := getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		// r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	table := GetDetailTableName(product)
	sql := fmt.Sprintf("SELECT cpu_avg, qps_avg, cost_avg, level, insert_time, ins_count FROM `%s` WHERE `bns_name` = ? AND `logic_idc` = ? AND `insert_time` BETWEEN ? AND ?", table)
	_, err = o.Raw(sql, bns, idc, start, end).QueryRows(&detailList)
	return
}
