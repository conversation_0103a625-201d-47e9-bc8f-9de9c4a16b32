package dao_ocean

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/errno"
	"fmt"

	"github.com/astaxie/beego/orm"
)

const (
	TABLENAME_NOAH_MODULES   = "noah_modules"
	PRIMARY_KEY_NOAH_MODULES = "Id"
)

type NoahModules struct {
	Id          int    `orm:"column(id);auto;pk" description:"ID"`
	Product     string `orm:"column(product)" description:""`
	BnsName     string `orm:"column(bns_name)" description:"bns名称"`
	MonitorItem string `orm:"column(monitor_item)" description:""`
	Dflag       int    `orm:"column(dflag)" description:"软删除"`
}

type NoahModulesBuilder struct {
	dbBase.DbBase
}

// CreateNoahModulesPtr
func CreateNoahModulesPtr() *NoahModulesBuilder {
	return &NoahModulesBuilder{dbBase.DbBase{Tablename: TABLENAME_NOAH_MODULES,
		PrimaryKey: PRIMARY_KEY_NOAH_MODULES, PtrDaoStruct: &NoahModules{}}}
}

func (c *NoahModulesBuilder) GetOceanModuleList(moduleName string) (dataList []NoahModules, r ocommon.ResultInfo) {
	var o orm.Ormer
	var err error
	o, err = getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}

	olog.Debug("get risk model info, moduleName:[%s]", moduleName)
	sql := fmt.Sprintf("SELECT "+
		"id, "+
		"product, "+
		"bns_name, "+
		"monitor_item, "+
		"dflag "+
		"FROM `noah_modules`"+
		"where bns_name like '%%%s%%' and dflag = 0", moduleName)

	olog.Debug("get risk model sql, sql:[%s]", sql)

	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&dataList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get risk model list from database, retry:[%d], err:[%v]", retry, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get risk model list from database, retry:[%d], err:[%v]", retry, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	return
}

func (c *NoahModulesBuilder) GetOceanModuleListByType(moduleName, typeT string) (dataList []NoahModules, r ocommon.ResultInfo) {
	var o orm.Ormer
	var err error
	o, err = getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}

	olog.Debug("get risk model info, moduleName:[%s]", moduleName)
	var sql string
	if typeT == "POD" {
		sql = fmt.Sprintf("SELECT "+
			"id, "+
			"product, "+
			"bns_name, "+
			"monitor_item, "+
			"dflag "+
			"FROM `noah_modules`"+
			"where bns_name like '%%%s%%' and product != 'risk_model' and dflag = 0", moduleName)
	} else {
		sql = fmt.Sprintf("SELECT "+
			"id, "+
			"product, "+
			"bns_name, "+
			"monitor_item, "+
			"dflag "+
			"FROM `noah_modules`"+
			"where bns_name like '%%%s%%' and product = 'risk_model' and dflag = 0", moduleName)
	}

	olog.Debug("get module sql, sql:[%s]", sql)

	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&dataList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get module list from database, retry:[%d], err:[%v]", retry, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get module list from database, retry:[%d], err:[%v]", retry, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	return
}

func (c *NoahModulesBuilder) GetRiskModelList(moduleName string) (dataList []NoahModules, r ocommon.ResultInfo) {
	var o orm.Ormer
	var err error
	o, err = getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}

	olog.Debug("get risk model info, moduleName:[%s]", moduleName)
	sql := fmt.Sprintf("SELECT "+
		"id, "+
		"product, "+
		"bns_name, "+
		"monitor_item, "+
		"dflag "+
		"FROM `noah_modules`"+
		"where bns_name like '%%%s%%' and product = 'risk_model' and dflag = 0", moduleName)

	olog.Debug("get risk model sql, sql:[%s]", sql)

	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&dataList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get risk model list from database, retry:[%d], err:[%v]", retry, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get risk model list from database, retry:[%d], err:[%v]", retry, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	return
}
