package dao_ocean

import (
	"database/sql"
	"dxm/siod-cloud/go-common-lib/olog"

	"github.com/astaxie/beego/orm"
)

const (
	OCEAN_DB_ORM_NAME = "ocap_xd"
)

func getOceanOrmer() (orm.Ormer, error) {
	var (
		err   error
		db    *sql.DB
		ormer orm.Ormer
	)
	if db, err = orm.GetDB(OCEAN_DB_ORM_NAME); err != nil {
		olog.Error("fail to get ocean DB. error=[%v]", err)
		return nil, err
	}
	if ormer, err = orm.NewOrmWithDB("mysql", OCEAN_DB_ORM_NAME, db); err != nil {
		olog.Error("fail to get ocean db. error=[%v]", err)
		return nil, err
	}
	return ormer, nil
}
