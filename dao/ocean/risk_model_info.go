package dao_ocean

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod_sre/auto-scaler/errno"
	"fmt"
	"time"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/olog"

	"github.com/astaxie/beego/orm"
)

// 逻辑生命周期控制表
const (
	TABLENAME_RISK_MODEL_INFO = "risk_model_info"
	RISK_MODEL_INFO_KEY       = "ID"
)

type RiskModelInfo struct {
	ID             int        `json:"id" orm:"column(id);auto;pk" description:"自增主键"`
	AppId          int        `json:"appId" orm:"column(app_id)" description:"应用ID"`
	StateId        int        `json:"stateId" orm:"column(state_id)" description:"状态"`
	Host           string     `json:"host" orm:"column(host)" description:"主机"`
	IdcTag         string     `json:"idcTag" orm:"column(idc_tag)" description:"机房"`
	IsShare        int        `json:"isShare" orm:"column(is_share)" description:"是否共享模型"`
	CpuCore        float64    `json:"cpuCore" orm:"column(cpu_core)" description:"cpu核数"`
	Num            int        `json:"num" orm:"column(num)" description:"模型数"`
	Version        string     `json:"version" orm:"column(version)" description:"模型版本"`
	Port           string     `json:"port" orm:"column(port)" description:"端口"`
	DFlag          int        `json:"dflag" diff:"dflag" orm:"column(dflag);size(4);default(0)" description:"软删标记"`
	LastModifyTime time.Time  `json:"lastModifyTime" diff:"last_modify_time" orm:"column(last_modify_time);type(datetime);auto_now" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type RiskModelInfoBuilder struct {
	dbBase.DbBase
}

func CreateRiskModelInfoPtr() *RiskModelInfoBuilder {
	return &RiskModelInfoBuilder{dbBase.DbBase{Tablename: TABLENAME_RISK_MODEL_INFO, PrimaryKey: RISK_MODEL_INFO_KEY, PtrDaoStruct: &RiskModelInfo{}}}
}

type RiskModelDetail struct {
	Name      string
	AppId     int
	StateId   int
	IsShare   int
	Num       int
	Version   string
	ModelList []RiskModelInfo
}

func (c *RiskModelInfoBuilder) GetRiskModelInfoByAppId(appId, stateId int, idcTag string) (dataInfo RiskModelDetail, r ocommon.ResultInfo) {
	var o orm.Ormer
	var err error
	o, err = getOceanOrmer()
	if err != nil {
		olog.Error("failed to get ocean ormer, err:[%v]", err)
		r = ocommon.GenResultInfo(oerrno.ERR_COMMON, oerrno.ErrnoMap[oerrno.ERR_COMMON], nil, nil)
		return
	}
	var sql string
	if idcTag == "" {
		sql = fmt.Sprintf("SELECT "+
			"id, "+
			"app_id, "+
			"state_id, "+
			"host, "+
			"idc_tag, "+
			"is_share, "+
			"cpu_core, "+
			"num, "+
			"version, "+
			"port, "+
			"dflag, "+
			"last_modify_time "+
			"FROM `risk_model_info` "+
			"where app_id = %d and state_id = %d", appId, stateId)
	} else {
		sql = fmt.Sprintf("SELECT "+
			"id, "+
			"app_id, "+
			"state_id, "+
			"host, "+
			"idc_tag, "+
			"is_share, "+
			"cpu_core, "+
			"num, "+
			"version, "+
			"port, "+
			"dflag, "+
			"last_modify_time "+
			"FROM `risk_model_info` "+
			"where app_id = %d and state_id = %d and idc_tag = '%s'", appId, stateId, idcTag)
	}

	var dataList []RiskModelInfo
	for retry := 1; retry < 3; retry++ {
		_, err = o.Raw(sql).QueryRows(&dataList)
		if err != nil {
			if retry == 1 {
				olog.Error("failed to get risk model info from database, retry:[%d], app_id:[%d], state_id:[%d], err:[%v]", retry, appId, stateId, err)
				continue
			}
			if retry == 2 {
				olog.Error("failed to get risk model info from database, retry:[%d], app_id:[%d], state_id:[%d], err:[%v]", retry, appId, stateId, err)
				r = ocommon.GenResultInfo(errno.ERR_DB_QUERY_FAILED, errno.ErrnoMap[errno.ERR_DB_QUERY_FAILED], nil, nil)
				return
			}
		}
		// 查询成功,跳出循环
		break
	}
	// debugData, _ := json.Marshal(&dataList)
	// olog.Debug("get data from risk_model_info, data:[%s]", string(debugData))

	// 判断查询数据是否可用
	if len(dataList) == 0 {
		r = ocommon.GenResultInfo(oerrno.ERR_DATA_WRONG, "data num error", nil, nil)
		olog.Warn("get null data by app_id from database, app_id:[%d], state_id:[%d]", appId, stateId)
		return
	}

	for _, v := range dataList {
		dataInfo.Num += v.Num
	}
	dataInfo.AppId = appId
	dataInfo.StateId = stateId
	dataInfo.Name = fmt.Sprintf("%d-%d", appId, stateId)
	dataInfo.IsShare = dataList[0].IsShare
	dataInfo.Version = dataList[0].Version
	dataInfo.ModelList = dataList
	return
}
