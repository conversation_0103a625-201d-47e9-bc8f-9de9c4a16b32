package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"time"

	"github.com/astaxie/beego/orm"
)

// 任务管理表
const (
	TABLENAME_TASK_INFO = "task_info"
	TASK_INFO_KEY       = "ID"
)

type TaskInfo struct {
	ID                int        `json:"id" orm:"column(id);size(20);auto;pk" description:"自增主键"`
	ModuleId          int        `json:"moduleId" orm:"column(module_id);size(11)" description:"模块ID"`
	ModuleName        string     `json:"moduleName" orm:"column(module_name);size(128)" description:"模块ID"`
	TaskModuleType    string     `json:"taskModuleType" orm:"column(task_module_type);size(64)" description:"任务模块类型"`
	TaskName          string     `json:"taskName" orm:"column(task_name);size(128)" description:"任务名称"`
	TaskType          string     `json:"taskType" orm:"column(task_type);size(128)" description:"任务类型"`
	TaskStatus        string     `json:"taskStatus" orm:"column(task_status);size(64)" description:"状态"`
	IdcTag            string     `json:"idcTag" orm:"column(idc_tag);size(32)" description:"机房标签"`
	RuleId            int        `json:"ruleId" orm:"column(rule_id);size(11)" description:"规则ID"`
	SchedMode         string     `json:"schedMode" orm:"column(sched_mode);size(32)" description:"伸缩模式, scale:缩容扩容 traffic:屏蔽解屏蔽"`
	SchedType         string     `json:"schedType" orm:"column(sched_type);size(32)" description:"定时伸缩类型"`
	SchedScaleInRule  int        `json:"schedScaleinRule" orm:"column(sched_scalein_rule);size(11)" description:"定时缩容规则"`
	SchedScaleOutRule int        `json:"schedScaleoutRule" orm:"column(sched_scaleout_rule);size(11)" description:"定时扩容规则"`
	TaskDesc          string     `json:"taskDesc" orm:"column(task_desc);size(1024)" description:"任务描述"`
	Dflag             int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime    time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer          *orm.Ormer `json:"-"`
}

type TaskInfoBuilder struct {
	dbBase.DbBase
}

func CreateTaskInfoPtr() *TaskInfoBuilder {
	return &TaskInfoBuilder{dbBase.DbBase{Tablename: TABLENAME_TASK_INFO, PrimaryKey: TASK_INFO_KEY, PtrDaoStruct: &TaskInfo{}}}
}

func (d *TaskInfoBuilder) GetTaskInfoByModuleID(moduleId int, taskType, idcTag string) (taskInfoList []TaskInfo, r ocommon.ResultInfo) {
	r = d.SearchByColumn(&taskInfoList, &TaskInfo{ModuleId: moduleId, TaskType: taskType, IdcTag: idcTag, Dflag: 0}, []string{"ModuleId", "TaskType", "IdcTag", "Dflag"})
	return
}

func (d *TaskInfoBuilder) GetTaskInfoByType(taskType string) (taskInfoList []TaskInfo, r ocommon.ResultInfo) {
	r = d.SearchByColumn(&taskInfoList, &TaskInfo{TaskType: taskType, Dflag: 0}, []string{"TaskType", "Dflag"})
	return
}

func (d *TaskInfoBuilder) UpdateTaskStatus(taskId int, taskStatus string) (r ocommon.ResultInfo) {
	_, r = d.UpdateByPk(&TaskInfo{TaskStatus: taskStatus}, []string{"TaskStatus"}, taskId)
	return
}
