package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"time"
)

// 模块信息
const (
	TABLENAME_SCALER_FLOW = "scaler_flow"
	SCALER_FLOW_KEY       = "ID"
)

type ScalerFlow struct {
	ID         int       `orm:"column(id);size(20);auto;pk" json:"id" description:"自增ID"`
	Applicant  string    `orm:"column(applicant);size(128)" json:"applicant" description:"申请人"`
	ModuleId   int       `orm:"column(module_id);size(11)" json:"moduleId" description:"模块ID"`
	Status     int       `orm:"column(status);size(8)" json:"status" description:"流程状态"`
	CreateTime time.Time `orm:"column(create_time);type(datetime)" json:"createTime" description:"创建时间"`
	FlowName   string    `orm:"column(flow_name);size(256)" json:"flowName" description:"流程名称"`
	FlowType   string    `orm:"column(flow_type);size(128)" json:"flowType" description:"流程类型"`
	FlowArgs   string    `orm:"column(flow_args);size(1024)" json:"flowArgs" description:"流程参数"`
}

type ScalerFlowBuilder struct {
	dbBase.DbBase
}

func CreateScalerRecordPtr() *ScalerFlowBuilder {
	return &ScalerFlowBuilder{dbBase.DbBase{Tablename: TABLENAME_SCALER_FLOW, PrimaryKey: SCALER_FLOW_KEY, PtrDaoStruct: &ScalerFlow{}}}
}
