/*
 *    @package: dao
 *    @author: <EMAIL>
 *    @Modifier:
 *    @usage: 数据库表操作初始化
 *    @date: 2024-03-15 16:34
 *    @Last modified: 2024-03-15 16:34
 */

package dao

import (
	"fmt"
	"os"
	"time"

	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"

	"dxm/siod-cloud/go-common-lib/olog"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	_ "github.com/go-sql-driver/mysql"

	Init "dxm/siod-cloud/go-common-lib/init"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	omysql "dxm/siod-cloud/go-common-lib/omysql2"
)

var bolInit = false
var zeroTime time.Time

const (
	DB_NAME = "default"
)

// @brief 初始化所有DB，新建的Table在InitDB中完成RegisterModel
func InitDB() {
	bol, _ := beego.AppConfig.Bool("orm_debug")
	if bol == true {
		orm.Debug = true //!打开orm日志

	}
	if bolInit == true {
		return
	}

	//!TODO, 注册项目的数据库表
	orm.RegisterModel(

		new(dao_ocean.RiskModelInfo),
		new(dao_ocean.RiskModelResource),
		new(Module),
		new(TaskInfo),
		new(RuleModel),
		new(RuleOnline),
		new(MonitorInfo),
		new(EventInfo),
		new(DecisionRecord),
		new(EngineInfo),
		new(ScalerFlow),
		new(ModuleNotifyRel),
		new(NotifyAction),
		new(ClusterNodes),
	)
	return
}

func InitDB2() {
	db, err := orm.GetDB("default")
	if err != nil {
		olog.Error("Failed to get default DB connection: %v", err)
		return
	}
	// 设置连接最大生命周期为5分钟，避免长时间连接导致的问题
	db.SetConnMaxLifetime(5 * time.Minute)
	// 减少空闲连接数，避免过多连接
	orm.SetMaxIdleConns("default", 10)
	// 减少最大打开连接数
	orm.SetMaxOpenConns("default", 20)
}

func InitOceanDB() {
	orm.DefaultTimeLoc, _ = time.LoadLocation("Asia/Shanghai")
	beego.Debug("DefaultTimeLoc:", orm.DefaultTimeLoc)
	dbUser := beego.AppConfig.String("ocean_db_user")
	dbHost := beego.AppConfig.String("ocean_db_host_name")
	dbPort := beego.AppConfig.String("ocean_db_port")
	dbPasswd := beego.AppConfig.String("ocean_db_password")
	dbName := beego.AppConfig.String("ocean_db_name")
	dbReadTimeout := beego.AppConfig.String("db_read_timeout")
	dbWriteTimeout := beego.AppConfig.String("db_write_timeout")
	dbConnectTimeout := beego.AppConfig.String("db_connect_timeout")

	strConnection := dbUser + ":" + dbPasswd + "@tcp(" + dbHost + ":" + dbPort + ")/" +
		dbName + "?charset=utf8&loc=Local&timeout=" + dbConnectTimeout + "&readTimeout=" +
		dbReadTimeout + "&writeTimeout=" + dbWriteTimeout
	beego.Notice("db connection:", strConnection)
	if err := orm.RegisterDataBase(dao_ocean.OCEAN_DB_ORM_NAME, "mysql", strConnection); err != nil {
		olog.Warn("Failed to connect DB=(%v:%v), err=(%v)", dbHost, dbPort, err)
		os.Exit(-1)
		return
	} else {
		olog.Info("RegisterDriver DB=(%+v:%+v) successfully", dbHost, dbPort)
	}
	if err := orm.RegisterDriver("mysql", orm.DRMySQL); err != nil {
		olog.Warn("Failed to register DB driver, DB=(%v:%v), err=(%v)", dbHost, dbPort, err)
		os.Exit(-1)
		return
	} else {
		olog.Info("RegisterDriver DB=(%+v:%+v) successfully", dbHost, dbPort)
	}

	// 低频请求，设置为短连接，避免坏连接
	db, err := orm.GetDB(dao_ocean.OCEAN_DB_ORM_NAME)
	if err != nil {
		olog.Error("Failed to get ocean DB connection: %v", err)
		return
	}
	// 设置更短的连接生命周期，避免连接堆积
	db.SetConnMaxLifetime(30 * time.Second)
	// 设置连接池参数
	orm.SetMaxIdleConns(dao_ocean.OCEAN_DB_ORM_NAME, 5)
	orm.SetMaxOpenConns(dao_ocean.OCEAN_DB_ORM_NAME, 10)
}

// CheckDBHealth 检查数据库连接健康状态
func CheckDBHealth() error {
	// 检查默认数据库连接
	if db, err := orm.GetDB("default"); err != nil {
		return fmt.Errorf("failed to get default DB: %v", err)
	} else if err := db.Ping(); err != nil {
		return fmt.Errorf("default DB ping failed: %v", err)
	}

	// 检查Ocean数据库连接
	if db, err := orm.GetDB(dao_ocean.OCEAN_DB_ORM_NAME); err != nil {
		return fmt.Errorf("failed to get ocean DB: %v", err)
	} else if err := db.Ping(); err != nil {
		return fmt.Errorf("ocean DB ping failed: %v", err)
	}

	return nil
}

// StartDBHealthMonitor 启动数据库健康监控
func StartDBHealthMonitor() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			if err := CheckDBHealth(); err != nil {
				olog.Error("Database health check failed: %v", err)
			} else {
				olog.Debug("Database health check passed")
			}
		}
	}()
}

var bolInitTest = false

// ！ 单纯环境初始化
func InitTest() {
	if bolInitTest == true {
		return
	}
	bolInitTest = true

	//!初始化错误码部分
	oerrno.Init()
	ocommon.Init(oerrno.ErrnoMap)

	//!单测环境，加载配置文件
	conf := []string{"conf/app_test.conf"} //!must be load fake_api.conf first
	Init.InitAppConfigForUnitTest(conf)
	Init.InitL4g()
	// TODO:Really confused by the function name(too many InitDB)
	InitDB()
	Init.InitDB()

}

// !统一处理DAO层异常
func doDaoException(r *ocommon.ResultInfo) {
	omysql.DoDBException(r, 1)
}
