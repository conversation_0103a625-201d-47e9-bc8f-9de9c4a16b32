/*
 *    @package: dao
 *    @author: <EMAIL>
 *    @Modifier:
 *    @usage: 数据库表操作初始化
 *    @date: 2024-03-15 16:34
 *    @Last modified: 2024-03-15 16:34
 */

package dao

import (
	"os"
	"time"

	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"

	"dxm/siod-cloud/go-common-lib/olog"

	"github.com/astaxie/beego"
	"github.com/astaxie/beego/orm"
	_ "github.com/go-sql-driver/mysql"

	Init "dxm/siod-cloud/go-common-lib/init"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	omysql "dxm/siod-cloud/go-common-lib/omysql2"
)

var bolInit = false
var zeroTime time.Time

const (
	DB_NAME = "default"
)

// @brief 初始化所有DB，新建的Table在InitDB中完成RegisterModel
func InitDB() {
	bol, _ := beego.AppConfig.Bool("orm_debug")
	if bol == true {
		orm.Debug = true //!打开orm日志

	}
	if bolInit == true {
		return
	}

	//!TODO, 注册项目的数据库表
	orm.RegisterModel(

		new(dao_ocean.RiskModelInfo),
		new(dao_ocean.RiskModelResource),
		new(Module),
		new(TaskInfo),
		new(RuleModel),
		new(RuleOnline),
		new(MonitorInfo),
		new(EventInfo),
		new(DecisionRecord),
		new(EngineInfo),
		new(ScalerFlow),
		new(ModuleNotifyRel),
		new(NotifyAction),
		new(ClusterNodes),
	)
	return
}

func InitDB2() {
	db, _ := orm.GetDB("default")
	db.SetConnMaxLifetime(60)
	orm.SetMaxIdleConns("default", 30)
	orm.SetMaxOpenConns("default", 30)
}

func InitOceanDB() {
	orm.DefaultTimeLoc, _ = time.LoadLocation("Asia/Shanghai")
	beego.Debug("DefaultTimeLoc:", orm.DefaultTimeLoc)
	dbUser := beego.AppConfig.String("ocean_db_user")
	dbHost := beego.AppConfig.String("ocean_db_host_name")
	dbPort := beego.AppConfig.String("ocean_db_port")
	dbPasswd := beego.AppConfig.String("ocean_db_password")
	dbName := beego.AppConfig.String("ocean_db_name")
	dbReadTimeout := beego.AppConfig.String("db_read_timeout")
	dbWriteTimeout := beego.AppConfig.String("db_write_timeout")
	dbConnectTimeout := beego.AppConfig.String("db_connect_timeout")

	strConnection := dbUser + ":" + dbPasswd + "@tcp(" + dbHost + ":" + dbPort + ")/" +
		dbName + "?charset=utf8&loc=Local&timeout=" + dbConnectTimeout + "&readTimeout=" +
		dbReadTimeout + "&writeTimeout=" + dbWriteTimeout
	beego.Notice("db connection:", strConnection)
	if err := orm.RegisterDataBase(dao_ocean.OCEAN_DB_ORM_NAME, "mysql", strConnection); err != nil {
		olog.Warn("Failed to connect DB=(%v:%v), err=(%v)", dbHost, dbPort, err)
		os.Exit(-1)
		return
	} else {
		olog.Info("RegisterDriver DB=(%+v:%+v) successfully", dbHost, dbPort)
	}
	if err := orm.RegisterDriver("mysql", orm.DRMySQL); err != nil {
		olog.Warn("Failed to register DB driver, DB=(%v:%v), err=(%v)", dbHost, dbPort, err)
		os.Exit(-1)
		return
	} else {
		olog.Info("RegisterDriver DB=(%+v:%+v) successfully", dbHost, dbPort)
	}

	// 低频请求，设置为短连接，避免坏连接
	db, _ := orm.GetDB(dao_ocean.OCEAN_DB_ORM_NAME)
	db.SetConnMaxLifetime(5 * time.Second)
}

var bolInitTest = false

// ！ 单纯环境初始化
func InitTest() {
	if bolInitTest == true {
		return
	}
	bolInitTest = true

	//!初始化错误码部分
	oerrno.Init()
	ocommon.Init(oerrno.ErrnoMap)

	//!单测环境，加载配置文件
	conf := []string{"conf/app_test.conf"} //!must be load fake_api.conf first
	Init.InitAppConfigForUnitTest(conf)
	Init.InitL4g()
	// TODO:Really confused by the function name(too many InitDB)
	InitDB()
	Init.InitDB()

}

// !统一处理DAO层异常
func doDaoException(r *ocommon.ResultInfo) {
	omysql.DoDBException(r, 1)
}
