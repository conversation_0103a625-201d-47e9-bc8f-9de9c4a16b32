package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"time"

	"github.com/astaxie/beego/orm"
)

// 任务实例表
const (
	TABLENAME_RULE_MODEL = "rule_model"
	RULE_MODEL_KEY       = "ID"
)

type RuleModel struct {
	ID int `json:"id" orm:"column(id);auto;pk" description:"自增ID"`

	// 规则基本信息
	Name string `json:"name" orm:"column(name);size(64)" description:"规则名称"`

	// 目标追踪
	MetricInfo     string `json:"metricInfo" orm:"column(metric_info);size(512);" description:"指标"`
	Period         int    `json:"period" orm:"column(period);size(4);" description:"周期"`
	TriggerTimes   int    `json:"triggerTimes" orm:"column(trigger_times);size(4);" description:"触发次数"`
	DisableScaleIn bool   `json:"disableScaleIn" orm:"column(disable_scale_in);size(4);" description:"是否禁用缩容"`
	StepCount      int    `json:"stepCount" orm:"column(step_count);size(8);" description:"步进次数,默认1"`
	StepInterval   int    `json:"stepInterval" orm:"column(step_interval);size(11);" description:"步进时间间隔,单位分钟"`

	Dflag          int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime time.Time  `json:"last_modify_time" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type ScalingRuleModelBuilder struct {
	dbBase.DbBase
}

func CreateRuleModelPtr() *ScalingRuleModelBuilder {
	return &ScalingRuleModelBuilder{dbBase.DbBase{Tablename: TABLENAME_RULE_MODEL, PrimaryKey: RULE_MODEL_KEY, PtrDaoStruct: &RuleModel{}}}
}
