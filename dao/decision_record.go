package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"time"

	"github.com/astaxie/beego/orm"
)

// 任务决策记录表
const (
	TABLENAME_DECISION_RECORD = "decision_record"
	DECISION_RECORD_KEY       = "ID"
)

type DecisionRecord struct {
	ID              int        `json:"id" orm:"column(id);auto;pk" description:"自增主键"`
	ModuleId        int        `json:"moduleId" orm:"column(module_id);size(4)" description:"模块ID"`
	EngineId        int        `json:"engineId" orm:"column(engine_id);size(4)" description:"引擎ID"`
	ServiceName     string     `json:"serviceName" orm:"column(service_name);size(64)" description:"服务名称"`
	TaskId          int        `json:"taskId" orm:"column(task_id);size(4)" description:"任务ID"`
	RuleId          int        `json:"ruleId" orm:"column(rule_id);size(4)" description:"规则ID"`
	IdcTag          string     `json:"idcTag" orm:"column(idc_tag)" description:"机房"`
	TaskType        string     `json:"taskType" orm:"column(task_type);size(32)" description:"任务类型"`
	TaskStatus      string     `json:"taskStatus" orm:"column(task_status);size(64)" description:"任务状态 启用、暂停、试运行、下线"`
	Status          int        `json:"status" orm:"column(status);size(32)" description:"执行状态"`
	CurrentNum      int        `json:"currentNum" orm:"column(current_num);size(32)" description:"当前实例数"`
	Action          string     `json:"action" orm:"column(action);size(4);" description:"动作 up: 增加 down: 减少"`
	AdjustmentValue int        `json:"adjustmentValue" orm:"column(adjustment_value);size(4);" description:"调整数量，调整数量"`
	HitMetric       string     `json:"hitMetric" orm:"column(hit_metric);size(4);" description:"命中指标"`
	FailedReason    string     `json:"failedReason" orm:"column(failed_reason);size(4);" description:"失败原因"`
	ProcessInfo     string     `json:"processInfo" orm:"column(process_info);size(4);" description:"过程信息"`
	InstanceInfo    string     `json:"instanceInfo" orm:"column(instance_info);size(4);" description:"实例状态信息"`
	NoticeFlag      bool       `json:"noticeFlag" orm:"column(notice_flag);size(4);" description:"已通告标记"`
	LastModifyTime  time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer        *orm.Ormer `json:"-"`
}

// 过程信息记录
type ProcessInfo struct {
	DecisionNum         int    `json:"decisionNum" description:"决策总数量"`
	DecisionHealthNum   int    `json:"decisionhealthNum" description:"决策健康总数量"`
	DecisionInfo        string `json:"decisionInfo" description:"决策指标"`
	CorrectionNum       int    `json:"correctionNum" description:"修正总数量"`
	CorrectionHealthNum int    `json:"correctionHealthNum" description:"修正健康总数量"`
	CorrectInfo         string `json:"correctInfo" description:"修正指标"`
	FinalNum            int    `json:"finalNum" description:"最终数量"`
}

// 决策时实例信息记录
type InstanceInfo struct {
	TotalNum    int `json:"totalNum" description:"全部实例数"`
	DisableNum  int `json:"disableNum" description:"屏蔽数量"`
	HealthNum   int `json:"healthNum" description:"健康数量"`
	AbnormalNum int `json:"abnormalNum" description:"异常数量"`
}

type DecisionRecordBuilder struct {
	dbBase.DbBase
}

func CreateDecisionRecordPtr() *DecisionRecordBuilder {
	return &DecisionRecordBuilder{dbBase.DbBase{Tablename: TABLENAME_DECISION_RECORD, PrimaryKey: DECISION_RECORD_KEY, PtrDaoStruct: &DecisionRecord{}}}
}
