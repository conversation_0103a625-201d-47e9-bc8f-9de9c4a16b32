package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oquery"
	"time"

	"github.com/astaxie/beego/orm"
)

// 任务实例表
const (
	TABLENAME_SCALING_RULE_ONLINE = "rule_online"
	SCALING_RULE_ONLINE_KEY       = "ID"
)

type RuleOnline struct {
	ID       int `json:"id" orm:"column(id);size(20);auto;pk" description:"自增ID"`
	ModuleId int `json:"moduleId" orm:"column(module_id);size(11)" description:"模块ID"`
	TaskId   int `json:"taskId" orm:"column(task_id);size(11)" description:"任务ID"`
	// 规则基本信息
	Name            string `json:"name" orm:"column(name);size(128)" description:"规则名称"`
	ScalingRuleType string `json:"scalingRuleType" orm:"column(rule_type);size(256);" description:"伸缩规则类型"`
	CurrentNum      int    `json:"currentNum" orm:"column(current_num);size(11);" description:"所有实例数量"`
	AvailNum        int    `json:"availNum" orm:"column(avail_num);size(11);" description:"可用实例数量"`
	AdjustNum       int    `json:"adjustNum" orm:"column(adjust_num);size(11);" description:"调整实例数量"`
	StepCount       int    `json:"stepCount" orm:"column(step_count);size(8);" description:"步进次数,默认1"`
	StepInterval    int    `json:"stepInterval" orm:"column(step_interval);size(11);" description:"步进时间间隔,单位分钟"`
	StepDetail      string `json:"stepDetail" orm:"column(step_detail);size(1024);" description:"步进详情"`

	// 简单规则
	ManualAction  string `json:"manualAction" orm:"column(manual_action);size(64);" description:"动作 up: 增加 down: 减少"`
	BlockAutoTask bool   `json:"blockAutoTask" orm:"column(block_auto_task);size(4);" description:"禁止自动任务"`
	CooldownTime  int    `json:"cooldownTime" orm:"column(cooldown_time);size(11);" description:"冷却时间,单位分钟"`

	// 时间触发规则
	CronPeriod      string `json:"cronPeriod" orm:"column(cron_period);size(32);" description:"定时任务运行周期"`
	CronDay         string `json:"cronDay" orm:"column(cron_day);size(32);" description:"定时任务执行日期"`
	CronTime        string `json:"cronTime" orm:"column(cron_time);size(64);" description:"定时任务执行时间"`
	CronProtectType string `json:"cronProtectType" orm:"column(cron_protect_type);size(128);" description:"定时任务保护类型"`

	// 目标追踪
	TargetMetric       string `json:"targetMetric" orm:"column(target_metric);size(512);" description:"指标"`
	TargetPeriod       int    `json:"targetPeriod" orm:"column(target_period);size(8);" description:"周期"`
	TargetTriggerTimes int    `json:"targetTriggerTimes" orm:"column(target_trigger_times);size(8);" description:"触发次数"`
	TargetCanScaling   bool   `json:"targetCanScaling" orm:"column(target_can_scaling);size(4);" description:"是否可以缩容"`

	Dflag          int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type RuleOnlineBuilder struct {
	dbBase.DbBase
}

func CreateRuleOnlinePtr() *RuleOnlineBuilder {
	return &RuleOnlineBuilder{dbBase.DbBase{Tablename: TABLENAME_SCALING_RULE_ONLINE, PrimaryKey: SCALING_RULE_ONLINE_KEY, PtrDaoStruct: &RuleOnline{}}}
}

func (d *RuleOnlineBuilder) GetRuleByTaskInfoId(taskInfoIds []int) (ruleList []RuleOnline, r ocommon.ResultInfo) {
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("TaskId", oquery.OP_IN, taskInfoIds)
	query.AddConditonsByOperator("Dflag", oquery.OP_EQUAL, 0)
	r = d.SearchByQuery(&ruleList, query)
	return
}
