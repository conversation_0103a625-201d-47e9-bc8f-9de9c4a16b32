package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"

	"github.com/astaxie/beego/orm"
)

// 任务实例表
const (
	TABLENAME_ENGINE_INFO = "engine_info"
	ENGINE_INFO_KEY       = "ID"
)

type EngineInfo struct {
	ID             int        `json:"id" orm:"column(id);size(20);auto;pk" description:"自增主键"`
	ModuleId       int        `json:"moduleId" orm:"column(module_id);size(11)" description:"模块ID"`
	ServiceName    string     `json:"serviceName" orm:"column(service_name);size(128)" description:"服务名称"`
	TaskId         int        `json:"taskId" orm:"column(task_id);size(11)" description:"任务ID"`
	RuleId         int        `json:"ruleId" orm:"column(rule_id);size(11)" description:"规则ID"`
	EngineTaskId   int        `json:"engineTaskId" orm:"column(engine_task_id);size(11)" description:"引擎任务"`
	ScaleId        int        `json:"scaleId" orm:"column(scale_id);size(11)" description:"扩缩容ID"`
	IdcTag         string     `json:"idcTag" orm:"column(idc_tag);size(64)" description:"机房"`
	TaskType       string     `json:"taskType" orm:"column(task_type);size(32)" description:"任务类型"`
	TaskStatus     string     `json:"taskStatus" orm:"column(task_status);size(32)" description:"任务状态"`
	ScaleEngine    string     `json:"scaleEngine" orm:"column(scale_engine);size(32)" description:"扩缩容引擎"`
	Action         string     `json:"action" orm:"column(action);size(32)" description:"扩缩容动作"`
	Concurrency    int        `json:"concurrency" orm:"column(concurrency);size(8)" description:"并发度"`
	AdjustmentType string     `json:"adjustmentType" orm:"column(adjustment_type);size(32)" description:"伸缩模式"`
	IpList         string     `json:"ipList" orm:"column(ip_list)" description:"IP列表"`
	AdjustNum      int        `json:"adjustNum" orm:"column(adjust_num);size(8)" description:"变更实例数，缩容为负值"`
	CurrentNum     int        `json:"currentNum" orm:"column(current_num);size(8)" description:"当前的实例"`
	ExpectedNum    int        `json:"expectedNum" orm:"column(expected_num);size(8)" description:"期望的实例"`
	ActualNum      int        `json:"actualNum" orm:"column(actual_num);size(8)" description:"实际实例数"`
	SuccessNum     int        `json:"successNum" orm:"column(success_num);size(8)" description:"成功的实例数"`
	Status         int        `json:"status" orm:"column(status);size(4)" description:"备份任务状态,1:准备 2:执行中 3: 成功 4:失败 5:部分成功"`
	CreateTime     time.Time  `json:"createTime" orm:"type(datetime)" description:"创建时间"`
	UpdateTime     time.Time  `json:"updateTime" orm:"type(datetime)" description:"更新时间"`
	FinishTime     time.Time  `json:"finishTime" orm:"column(finish_time);type(datetime)" description:"完成时间"`
	RetryTimes     int        `json:"retryTimes" orm:"column(retry_times);size(4)" description:"任务重试次数"`
	FailureType    string     `json:"failureType" orm:"column(failure_type);size(127)" description:"失败类型"`
	ActionReason   string     `json:"actionReason" orm:"column(action_reason);size(1024)" description:"任务执行原因"`
	RequestInfo    string     `json:"requestInfo" orm:"column(request_info);size(1024)" description:"请求信息"`
	ResponseInfo   string     `json:"responseInfo" orm:"column(response_info);size(1024)" description:"响应信息"`
	CallbackInfo   string     `json:"callbackInfo" orm:"column(callback_info);type(text)" description:"回调信息"`
	Dflag          int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type EngineInfoBuilder struct {
	dbBase.DbBase
}

func CreateEngineInfoPtr() *EngineInfoBuilder {
	return &EngineInfoBuilder{dbBase.DbBase{Tablename: TABLENAME_ENGINE_INFO, PrimaryKey: ENGINE_INFO_KEY, PtrDaoStruct: &EngineInfo{}}}
}

func (c *EngineInfoBuilder) GetEngineInfoByStatus(idcTag string, statusList []int) (dataList []EngineInfo, r ocommon.ResultInfo) {
	if len(statusList) == 0 {
		return
	}

	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("Status", oquery.OP_IN, statusList)
	query.AddConditonsByOperator("IdcTag", oquery.OP_EQUAL, idcTag)
	r = c.SearchByQuery(&dataList, query)
	if !r.IsOk() {
		olog.Error("failed to get data from database, err:[%v]", r)
	}
	return
}

// 获取手动任务列表
func (c *EngineInfoBuilder) GetEngineInfoForManualByModuleId(moduleId int, statusList []int) (dataList []EngineInfo, r ocommon.ResultInfo) {
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("TaskType", oquery.OP_EQUAL, "task_type_manual")
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, moduleId)
	query.AddConditonsByOperator("Status", oquery.OP_IN, statusList)
	r = c.SearchByQuery(&dataList, query)
	if !r.IsOk() {
		olog.Error("failed to get data from database, err:[%v]", r)
	}
	return
}

// 获取自动化任务列表
func (c *EngineInfoBuilder) GetEngineInfoForAutoTaskByModuleId(moduleId int, statusList []int) (dataList []EngineInfo, r ocommon.ResultInfo) {
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("TaskType", oquery.OP_EQUAL, "task_type_auto_task")
	query.AddConditonsByOperator("ModuleId", oquery.OP_EQUAL, moduleId)
	query.AddConditonsByOperator("Status", oquery.OP_IN, statusList)
	r = c.SearchByQuery(&dataList, query)
	if !r.IsOk() {
		olog.Error("failed to get data from database, err:[%v]", r)
	}
	return
}
