package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"time"
)

// 任务实例表
const (
	TABLENAME_OPERATE_RECORD = "operate_record"
	OPERATE_RECORD_KEY       = "ID"
)

type OperateRecord struct {
	ID         int       `json:"id" orm:"column(id);auto;pk" description:"自增ID"`
	UserName   string    `json:"userName" orm:"column(user_name);size(64);" description:"用户名"`
	Platform   string    `json:"platform" orm:"column(platform);size(64);" description:"平台"`
	Operate    string    `json:"operate" orm:"column(operate);size(64);" description:"操作"`
	Context    string    `json:"context" orm:"column(context);size(1024);" description:"操作内容"`
	RecordTime time.Time `json:"recordTime" orm:"column(record_time);type(datetime);auto_now_add" description:"记录时间"`
}

type OperateRecordBuilder struct {
	dbBase.DbBase
}

func CreateOperateRecordPtr() *OperateRecordBuilder {
	return &OperateRecordBuilder{dbBase.DbBase{
		Tablename:    TABLENAME_OPERATE_RECORD,
		PrimaryKey:   OPERATE_RECORD_KEY,
		PtrDaoStruct: &OperateRecord{}}}
}

func (c *OperateRecordBuilder) RecordUserOperate(username, operate, data string) (r ocommon.ResultInfo) {
	_, r = c.Insert(&OperateRecord{UserName: username, Context: data, RecordTime: time.Now()})
	return
}

func (c *OperateRecordBuilder) RecordPlatformOperate(platform, operate, data string) (r ocommon.ResultInfo) {
	_, r = c.Insert(&OperateRecord{Platform: platform, Context: data, RecordTime: time.Now()})
	return
}
