package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"time"

	"github.com/astaxie/beego/orm"
)

// 集群节点信息
const (
	TABLENAME_CLUSTER_NODES = "cluster_nodes"
	CLUSTER_NODES_KEY       = "ID"
)

// 节点角色常量
const (
	NODE_ROLE_MASTER = "master"
	NODE_ROLE_SLAVE  = "slave"
)

// 节点状态常量
const (
	NODE_STATUS_OFFLINE = 0 // 离线
	NODE_STATUS_ONLINE  = 1 // 在线
)

type ClusterNodes struct {
	ID             int64      `json:"id" orm:"column(id);size(20);auto;pk" description:"自增ID"`
	Offset         int        `json:"offset" orm:"column(offset);size(11)" description:"节点偏移量"`
	Hostname       string     `json:"hostname" orm:"column(hostname);size(128)" description:"主机名"`
	IP             string     `json:"ip" orm:"column(ip);size(64)" description:"IP地址"`
	Role           string     `json:"role" orm:"column(role);size(32)" description:"节点角色：master/slave"`
	Status         int        `json:"status" orm:"column(status);size(4)" description:"节点状态：0-离线，1-在线"`
	LastHeartbeat  time.Time  `json:"lastHeartbeat" orm:"column(last_heartbeat)" description:"最后心跳时间"`
	Dflag          int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer       *orm.Ormer `json:"-"`
}

type ClusterNodesBuilder struct {
	dbBase.DbBase
}

func CreateClusterNodesPtr() *ClusterNodesBuilder {
	return &ClusterNodesBuilder{dbBase.DbBase{Tablename: TABLENAME_CLUSTER_NODES, PrimaryKey: CLUSTER_NODES_KEY, PtrDaoStruct: &ClusterNodes{}}}
}

// GetNodesByRole 根据角色获取节点列表
func (c *ClusterNodesBuilder) GetNodesByRole(role string) (nodeList []ClusterNodes, r ocommon.ResultInfo) {
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{Role: role, Dflag: 0}, []string{"Role", "Dflag"})
	return
}

// GetActiveNodes 获取在线节点列表
func (c *ClusterNodesBuilder) GetActiveNodes() (nodeList []ClusterNodes, r ocommon.ResultInfo) {
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{Status: NODE_STATUS_ONLINE, Dflag: 0}, []string{"Status", "Dflag"})
	return
}

// GetMasterNodes 获取master节点列表
func (c *ClusterNodesBuilder) GetMasterNodes() (nodeList []ClusterNodes, r ocommon.ResultInfo) {
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{Role: NODE_ROLE_MASTER, Status: NODE_STATUS_ONLINE, Dflag: 0}, []string{"Role", "Status", "Dflag"})
	return
}

// UpdateNodeHeartbeat 更新节点心跳时间
func (c *ClusterNodesBuilder) UpdateNodeHeartbeat(nodeIP string) (r ocommon.ResultInfo) {
	var nodeList []ClusterNodes
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{IP: nodeIP, Dflag: 0}, []string{"IP", "Dflag"})
	if !r.IsOk() || len(nodeList) == 0 {
		return
	}

	node := nodeList[0]
	node.LastHeartbeat = time.Now()
	node.Status = NODE_STATUS_ONLINE // 设置为在线状态
	_, r = CreateClusterNodesPtr().UpdateByPk(&node, []string{"LastHeartbeat", "Status"}, int(node.ID))
	return
}

// RegisterNode 注册节点，根据offset判断插入或更新
func (c *ClusterNodesBuilder) RegisterNode(nodeInfo ClusterNodes) (nodeID int64, r ocommon.ResultInfo) {
	// 根据offset查询是否存在该节点
	var existingNodes []ClusterNodes
	r = CreateClusterNodesPtr().SearchByColumn(&existingNodes, &ClusterNodes{Offset: nodeInfo.Offset, Dflag: 0}, []string{"Offset", "Dflag"})
	if !r.IsOk() {
		return
	}

	// 设置当前时间
	now := time.Now()
	nodeInfo.LastHeartbeat = now
	nodeInfo.LastModifyTime = now

	if len(existingNodes) > 0 {
		// 存在则更新
		existingNode := existingNodes[0]
		nodeInfo.ID = existingNode.ID

		// 更新节点信息
		_, r = CreateClusterNodesPtr().UpdateByPk(&nodeInfo, []string{
			"Hostname", "IP", "Role", "Status", "LastHeartbeat", "LastModifyTime",
		}, int(existingNode.ID))
		nodeID = existingNode.ID
	} else {
		// 不存在则插入
		nodeInfo.Status = NODE_STATUS_ONLINE // 新注册的节点默认在线
		var insertID int
		insertID, r = CreateClusterNodesPtr().Insert(&nodeInfo)
		nodeID = int64(insertID)
	}

	return
}

// GetNodeByOffset 根据offset获取节点信息
func (c *ClusterNodesBuilder) GetNodeByOffset(offset int) (node ClusterNodes, r ocommon.ResultInfo) {
	var nodeList []ClusterNodes
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{Offset: offset, Dflag: 0}, []string{"Offset", "Dflag"})
	if !r.IsOk() || len(nodeList) == 0 {
		return
	}
	node = nodeList[0]
	return
}

// GetNodeByIP 根据IP获取节点信息
func (c *ClusterNodesBuilder) GetNodeByIP(ip string) (node ClusterNodes, r ocommon.ResultInfo) {
	var nodeList []ClusterNodes
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{IP: ip, Dflag: 0}, []string{"IP", "Dflag"})
	if !r.IsOk() || len(nodeList) == 0 {
		return
	}
	node = nodeList[0]
	return
}

// GetActiveNodesOrderByOffset 获取在线节点列表，按offset排序
func (c *ClusterNodesBuilder) GetActiveNodesOrderByOffset() (nodeList []ClusterNodes, r ocommon.ResultInfo) {
	// 使用原生SQL查询并按offset排序
	o := orm.NewOrm()
	sql := "SELECT * FROM cluster_nodes WHERE status = ? AND dflag = 0 ORDER BY offset ASC"
	_, err := o.Raw(sql, NODE_STATUS_ONLINE).QueryRows(&nodeList)
	if err != nil {
		r = ocommon.GenResultInfo(500, "query failed", nil, err)
		return
	}
	r = ocommon.GenResultInfo(0, "success", nil, nil)
	return
}

// UpdateNodeRole 更新节点角色
func (c *ClusterNodesBuilder) UpdateNodeRole(nodeIP string, role string) (r ocommon.ResultInfo) {
	var nodeList []ClusterNodes
	r = CreateClusterNodesPtr().SearchByColumn(&nodeList, &ClusterNodes{IP: nodeIP, Dflag: 0}, []string{"IP", "Dflag"})
	if !r.IsOk() || len(nodeList) == 0 {
		return
	}

	node := nodeList[0]
	node.Role = role
	node.LastModifyTime = time.Now()
	_, r = CreateClusterNodesPtr().UpdateByPk(&node, []string{"Role", "LastModifyTime"}, int(node.ID))
	return
}

// GetMasterNodeByElection 根据选举规则获取master节点（心跳存在且offset最小）
func (c *ClusterNodesBuilder) GetMasterNodeByElection(heartbeatTimeout time.Duration) (masterNode ClusterNodes, r ocommon.ResultInfo) {
	// 获取心跳有效的在线节点，按offset排序
	activeNodes, r := c.GetActiveNodesOrderByOffset()
	if !r.IsOk() {
		return
	}

	// 过滤心跳超时的节点
	now := time.Now()
	for _, node := range activeNodes {
		if now.Sub(node.LastHeartbeat) <= heartbeatTimeout {
			masterNode = node
			return
		}
	}

	// 没有找到有效的master节点
	r = ocommon.GenResultInfo(404, "no valid master node found", nil, nil)
	return
}

// CleanupExpiredMasterNodes 清理心跳过期的master节点，将其角色更新为slave
func (c *ClusterNodesBuilder) CleanupExpiredMasterNodes(heartbeatTimeout time.Duration, excludeIP string) (cleanedCount int, r ocommon.ResultInfo) {
	// 获取所有master节点
	masterNodes, r := c.GetNodesByRole(NODE_ROLE_MASTER)
	if !r.IsOk() {
		return
	}

	now := time.Now()
	cleanedCount = 0

	for _, node := range masterNodes {
		// 跳过当前新master节点
		if node.IP == excludeIP {
			continue
		}

		// 检查心跳是否过期
		if now.Sub(node.LastHeartbeat) > heartbeatTimeout {
			// 更新过期的master节点为slave
			node.Role = NODE_ROLE_SLAVE
			node.LastModifyTime = now
			_, updateResult := CreateClusterNodesPtr().UpdateByPk(&node, []string{"Role", "LastModifyTime"}, int(node.ID))
			if updateResult.IsOk() {
				cleanedCount++
			}
		}
	}

	r = ocommon.GenResultInfo(0, "success", cleanedCount, nil)
	return
}
