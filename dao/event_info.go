package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"time"
)

// 任务决策记录表
const (
	TABLENAME_EVENT_INFO = "event_info"
	EVENT_INFO_KEY       = "ID"
)

type EventInfo struct {
	Id             int       `orm:"column(id);size(20);auto;pk" json:"id" description:"ID"`
	ModuleId       int       `orm:"column(module_id);size(11)" json:"moduleId" description:"模块ID"`
	ServiceName    string    `orm:"column(service_name);size(64)" json:"serviceName" description:"服务名称"`
	LogicIdc       string    `orm:"column(logic_idc);size(32)" json:"logicIdc" description:"逻辑机房"`
	TaskId         int       `orm:"column(task_id);size(11)" json:"taskId" description:"任务ID"`
	RuleId         int       `orm:"column(rule_id);size(11)" json:"ruleId" description:"规则ID"`
	RuleInfo       string    `orm:"column(rule_info);size(512)" json:"ruleInfo" description:"规则信息"`
	Type           string    `orm:"column(type);size(64)" json:"type" description:"事件类型"`
	Action         string    `orm:"column(action);size(32)" json:"action" description:"动作"`
	CreateTime     time.Time `orm:"column(create_time)" json:"createTime" description:"创建时间"`
	LastModifyTime time.Time `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
}

type EventInfoBuilder struct {
	dbBase.DbBase
}

func CreateEventInfoPtr() *EventInfoBuilder {
	return &EventInfoBuilder{dbBase.DbBase{Tablename: TABLENAME_EVENT_INFO, PrimaryKey: EVENT_INFO_KEY, PtrDaoStruct: &EventInfo{}}}
}
