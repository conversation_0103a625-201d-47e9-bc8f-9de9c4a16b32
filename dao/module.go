package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/ocommon"
	"time"

	"github.com/astaxie/beego/orm"
)

// 模块信息
const (
	TABLENAME_MODULE = "module"
	MODULE_KEY       = "ID"
)

type Module struct {
	ID              int        `json:"id" orm:"column(id);size(20);auto;pk" description:"自增ID"`
	Name            string     `json:"name" orm:"column(name);size(128)" description:"模块名称"`
	ServiceName     string     `json:"serviceName" orm:"column(service_name);size(64)" description:"服务名称"`
	ModuleType      string     `json:"moduleType" orm:"column(module_type);size(127);" description:"模块类型 模型、POD、BCC"`
	TaskIdList      string     `json:"taskIdList" orm:"column(task_id_list);size(127);" description:"任务列表"`
	InstanceInfo    string     `json:"instanceInfo" orm:"column(instance_info);size(512)" description:"实例数"`
	CoolDown        int        `json:"coolDown" orm:"column(cool_down);size(32)" description:"执行冷却时间,默认300"`
	NextTime        time.Time  `json:"nextTime" orm:"column(next_time)" description:"下次执行时间"`
	DisableAutoTask bool       `json:"disableAutoTask" orm:"column(disable_auto_task);size(4)" description:"禁止自动任务执行"`
	DisableTime     time.Time  `json:"disableTime" orm:"column(disable_time)" description:"禁止自动任务截止时间，若为初始时间则永久禁止"`
	ScaleEngine     string     `json:"scaleEngine" orm:"column(scale_engine);size(64)" description:"扩缩容引擎"`
	OpOwnerList     string     `json:"opOwnerList" orm:"column(op_owner_list);size(512)" description:"运维负责人列表,逗号分割"`
	RdOwnerList     string     `json:"rdOwnerList" orm:"column(rd_owner_list);size(512)" description:"研发负责人列表,逗号分割"`
	Dflag           int        `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
	LastModifyTime  time.Time  `json:"lastModifyTime" orm:"column(last_modify_time);type(timestamp);auto_now;" description:"最后修改时间"`
	ptrOrmer        *orm.Ormer `json:"-"`
}

// module表中为该结构体的数组格式
type ModuleInstanceInfo struct {
	Tag       string  `json:"tag"`
	MaxNum    int     `json:"maxNum"`
	MinNum    int     `json:"minNum"`
	CurrNum   int     `json:"currNum"`
	MaxFactor float64 `json:"maxFactor"`
	MinFactor float64 `json:"minFactor"`
}

type ModuleBuilder struct {
	dbBase.DbBase
}

func CreateModulePtr() *ModuleBuilder {
	return &ModuleBuilder{dbBase.DbBase{Tablename: TABLENAME_MODULE, PrimaryKey: MODULE_KEY, PtrDaoStruct: &Module{}}}
}

func (m *ModuleBuilder) GetModelByType(moduleType string) (modelList []Module, r ocommon.ResultInfo) {
	r = CreateModulePtr().SearchByColumn(&modelList, &Module{ModuleType: moduleType, Dflag: 0}, []string{"Dflag", "ModuleType"})
	return
}
