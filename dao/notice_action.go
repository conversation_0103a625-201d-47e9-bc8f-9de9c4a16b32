package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"encoding/json"
	"errors"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"

	"github.com/astaxie/beego/orm"
)

type NotifyAction struct {
	Id         int           `orm:"column(id);auto" json:"id"`
	Name       string        `orm:"column(name);size(45);null" json:"name"`
	Alias      string        `orm:"column(alias);size(45);null" json:"alias"`
	Action     string        `orm:"column(action);size(45);null" json:"action"`
	ActionList string        `orm:"column(action_list);size()" json:"-"`
	ActionJson []ActionParam `orm:"-" json:"action_json"`
	CreateTime time.Time     `orm:"column(create_time);type(datetime);null" json:"create_time"`
	Creator    string        `orm:"column(creator);size(45);null" json:"creator"`
	Dflag      int           `json:"dflag" orm:"column(dflag);size(4)" description:"软删标志"`
}

// ActionParam action的json结构
type ActionParam struct {
	Channel     string   `json:"notify_type"`
	Receiver    string   `json:"receiver"`
	AtUsers     []string `json:"at_users,omitempty"`
	ReceiverArr any      `json:"receiver_arr,omitempty"`
}

type MessageNotifyBuilder struct {
	dbBase.DbBase
}

func CreateNotifyPtr() *MessageNotifyBuilder {
	return &MessageNotifyBuilder{dbBase.DbBase{
		Tablename:    "notify_action",
		PrimaryKey:   "Id",
		IsDFlag:      false,
		PtrDaoStruct: &NotifyAction{},
	}}
}

func (p *MessageNotifyBuilder) Add(f NotifyAction) (int, error) {

	if f.ActionList == "" {
		actionStr, _ := json.Marshal(f.ActionJson)
		f.ActionList = string(actionStr)
	}

	id, r := p.Insert(&f)
	if !r.IsOk() {
		return id, errors.New(r.ErrMsg)
	}

	return id, nil
}

func (p *MessageNotifyBuilder) Update(f NotifyAction) error {

	if f.ActionList == "" {
		actionStr, _ := json.Marshal(f.ActionJson)
		f.ActionList = string(actionStr)
	}

	_, r := p.UpdateByPk(&f, []string{"Name", "Alias", "ActionList"}, f.Id)
	if !r.IsOk() {
		return errors.New(r.ErrMsg)
	}

	return nil
}

func (p *MessageNotifyBuilder) NotifyDel(ids []int) (total int64, err error) {
	var (
		query = oquery.NewQueryStructOfTable()
	)

	query.AddConditonsByOperator("id", oquery.OP_IN, ids)
	cnt, r := p.DeleteByQuery(query)
	if !r.IsOk() {
		err = errors.New(r.ErrMsg)
		return
	}

	total = cnt
	return

}

func (p *MessageNotifyBuilder) NotifyList(cond *orm.Condition, page, pageSize int64) (total int64, list []NotifyAction, err error) {
	var (
		query = oquery.NewQueryStructOfTable()
	)

	query.AddCustomCondition(cond)
	cnt, r := p.GetCountByQuery(query)
	if !r.IsOk() {
		err = errors.New(r.ErrMsg)
		return
	}

	total = cnt
	query.SetPageInfo(page, pageSize)
	query.AddOrders([]oquery.Order{{Name: "Id", IsASC: false}}...)
	r = p.SearchByQuery(&list, query)

	if !r.IsOk() {
		err = errors.New(r.ErrMsg)
		return
	}

	for i := 0; i < len(list); i++ {
		_ = json.Unmarshal([]byte(list[i].ActionList), &list[i].ActionJson)
	}

	return
}

func (p *MessageNotifyBuilder) NotifyGet(moduleId int) (list []NotifyAction, err error) {

	notifyRelList, er := CreateModuleNotifyRelPtr().Get(moduleId)
	if er != nil {
		err = er
		return
	}

	ids := make([]int, 0, len(notifyRelList))
	for _, rel := range notifyRelList {
		ids = append(ids, rel.NotifyId)
	}

	var (
		query = oquery.NewQueryStructOfTable()
		ptr   = CreateNotifyPtr()
		cond  = orm.NewCondition()
	)

	cond = cond.And("id"+"__"+oquery.OP_IN, ids)
	query.AddCustomCondition(cond)
	r := ptr.SearchByQuery(&list, query)
	if !r.IsOk() {
		err = errors.New(r.ErrMsg)
		return
	}

	for i := 0; i < len(list); i++ {
		_ = json.Unmarshal([]byte(list[i].ActionList), &list[i].ActionJson)
	}

	return
}
