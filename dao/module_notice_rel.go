package dao

import (
	"dxm/siod-cloud/go-common-lib/dbBase"
	"dxm/siod-cloud/go-common-lib/oquery"
	"errors"

	"github.com/astaxie/beego/orm"
)

type ModuleNotifyRel struct {
	Id       int `orm:"column(id);auto" json:"id" description:"主键id"`
	ModuleId int `orm:"column(module_id);null" json:"scene_id"`
	NotifyId int `orm:"column(notify_id);null" json:"notify_id"`
}

type ModuleNotifyRelBuilder struct {
	dbBase.DbBase
}

func CreateModuleNotifyRelPtr() *ModuleNotifyRelBuilder {
	return &ModuleNotifyRelBuilder{dbBase.DbBase{
		Tablename:    "module_notify_rel",
		PrimaryKey:   "Id",
		IsDFlag:      false,
		PtrDaoStruct: &ModuleNotifyRel{},
	}}
}

func (m *ModuleNotifyRelBuilder) Add(list []ModuleNotifyRel) error {
	_, r := m.InsertMulti(&list)
	if !r.IsOk() {
		return errors.New(r.ErrMsg)
	}

	return nil
}

func (m *ModuleNotifyRelBuilder) Del(list []ModuleNotifyRel) error {
	_, r := m.InsertMulti(&list)
	if !r.IsOk() {
		return errors.New(r.ErrMsg)
	}

	return nil
}

func (m *ModuleNotifyRelBuilder) Get(moduleId int) (list []ModuleNotifyRel, err error) {

	var (
		query = oquery.NewQueryStructOfTable()
		cond  = orm.NewCondition()
	)

	cond = cond.And("module_id"+"__"+oquery.OP_EQUAL, moduleId)
	query.AddCustomCondition(cond)
	r := m.SearchByQuery(&list, query)
	if !r.IsOk() {
		err = errors.New(r.ErrMsg)
		return
	}

	return
}
