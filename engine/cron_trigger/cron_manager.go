package cron_trigger

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"dxm/siod-cloud/go-common-lib/ocommon"

	"github.com/astaxie/beego"
)

type TaskManager struct {
	CT       *CronTask
	CronInfo map[string]string
	// 控制信号
	startChan chan bool
	stopChan  chan bool
	isRunning bool
	mu        sync.Mutex
}

var CronTaskManager *TaskManager

func init() {
	CronTaskManager = NewTaskManager()
}

func NewTaskManager() *TaskManager {
	return &TaskManager{
		CT:        &CronTask{},
		CronInfo:  make(map[string]string, 10),
		startChan: make(chan bool, 1),
		stopChan:  make(chan bool, 1),
		isRunning: false,
	}
}

func (t *TaskManager) Run() {
	plog.EngineLG.Info("cron task manager run - waiting for master signal")

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-t.startChan:
			t.mu.Lock()
			if !t.isRunning {
				plog.EngineLG.Info("cron task manager received start signal - starting cron tasks")
				t.isRunning = true
				t.StartAllCron()
			}
			t.mu.Unlock()

		case <-t.stopChan:
			t.mu.Lock()
			if t.isRunning {
				plog.EngineLG.Info("cron task manager received stop signal - stopping cron tasks")
				t.isRunning = false
				if t.CT != nil {
					t.CT.Stop()
				}
			}
			t.mu.Unlock()

		case tc := <-ticker.C:
			t.mu.Lock()
			if t.isRunning && tc.Second() == 1 {
				// 每分钟检查一次任务是否新增加任务
				t.RefreshAllCron()
			}
			t.mu.Unlock()
		}
	}
}

// SendStartSignal 发送启动信号
func (t *TaskManager) SendStartSignal() {
	select {
	case t.startChan <- true:
		plog.EngineLG.Info("sent start signal to cron task manager")
	default:
		plog.EngineLG.Debug("start signal already pending for cron task manager")
	}
}

// SendStopSignal 发送停止信号
func (t *TaskManager) SendStopSignal() {
	select {
	case t.stopChan <- true:
		plog.EngineLG.Info("sent stop signal to cron task manager")
	default:
		plog.EngineLG.Debug("stop signal already pending for cron task manager")
	}
}
func (t *TaskManager) StartAllCron() {
	// 创建任务管理器
	var err error
	t.CT, err = NewCronTask(beego.AppConfig.String("log_filename"))
	if err != nil {
		plog.EngineLG.WithFields(CronFields()).Errorf("failed to create task manager: %v", err)
		return
	}
	taskList, r := comm.GetAllCronTask()
	if !r.IsOk() {
		plog.EngineLG.WithFields(CronFields()).Errorf("failed to get cron task when start task, err:[%v]", r)
		return
	}

	for _, task := range taskList {
		var cronNames []string
		var cronExprs []string
		cronNames, cronExprs = getCronInfo(task)
		// 统一处理添加任务和错误日志
		for i, expr := range cronExprs {
			if expr == "" {
				plog.EngineLG.WithFields(CronFields()).Errorf("failed to get cron task, task_name:[%s]", cronNames[i])
			} else {
				plog.EngineLG.WithFields(CronFields()).Debugf("add cron task, task_name:[%s], cron_expr:[%s]", cronNames[i], expr)
				t.CT.AddTask(cronNames[i], expr, cronTrigger(cronNames[i]))
				t.CronInfo[cronNames[i]] = expr
			}
		}
	}

	t.CT.Start()
}

func (t *TaskManager) RefreshAllCron() {
	plog.EngineLG.WithFields(CronFields()).Debugf("start to check cron task")
	taskList, r := comm.GetAllCronTask()
	if !r.IsOk() {
		plog.EngineLG.WithFields(CronFields()).Errorf("failed to get cron task when check task, err:[%v]", r)
		return
	}
	for _, task := range taskList {
		cronNames, cronExprs := getCronInfo(task)
		for i, taskName := range cronNames {
			if cronValue, ok := t.CronInfo[taskName]; ok {
				plog.EngineLG.WithFields(CronFields()).Debugf("check exist task, task_name:[%s]", taskName)
				if task.TaskStatus == base.TASK_STATUS_OFFLINE || task.TaskStatus == base.TASK_STATUS_DISABLE {
					plog.EngineLG.WithFields(CronFields()).Debugf("offline exist task, task_name:[%s]", taskName)
					t.CT.RemoveTask(taskName)
					delete(t.CronInfo, taskName)
				}

				if cronValue != cronExprs[i] {
					plog.EngineLG.WithFields(CronFields()).Debugf("refresh exist task, task_name:[%s], cron_expr:[%s]", taskName, cronExprs[i])
					t.CT.RemoveTask(taskName)
					t.CT.AddTask(taskName, cronExprs[i], cronTrigger(cronNames[i]))
					t.CronInfo[taskName] = cronExprs[i]
				}

			} else {
				plog.EngineLG.WithFields(CronFields()).Debugf("refresh add task, task_name:[%s], cron_expr:[%s]", taskName, cronExprs[i])
				t.CT.AddTask(taskName, cronExprs[i], cronTrigger(cronNames[i]))
				t.CronInfo[taskName] = task.SchedType
			}
		}
	}
}

func getCronInfo(task dao.TaskInfo) (cronNames, cronExprs []string) {
	switch task.SchedType {
	case base.TASK_CRONTAB_TYPE_CRON2:
		// 处理同时有ScaleIn和ScaleOut规则的情况
		cronNameIn, cronExprIn := getCronExprByRule(task.SchedScaleInRule)
		cronNameOut, cronExprOut := getCronExprByRule(task.SchedScaleOutRule)
		cronNames = append(cronNames, cronNameIn, cronNameOut)
		cronExprs = append(cronExprs, cronExprIn, cronExprOut)

	case base.TASK_CRONTAB_TYPE_CRON_EVENT:
		// 只处理ScaleIn规则
		cronName, cronExpr := getCronExprByRule(task.SchedScaleInRule)
		cronNames = append(cronNames, cronName)
		cronExprs = append(cronExprs, cronExpr)

	case base.TASK_CRONTAB_TYPE_EVENT_CRON:
		// 只处理ScaleOut规则
		cronName, cronExpr := getCronExprByRule(task.SchedScaleOutRule)
		cronNames = append(cronNames, cronName)
		cronExprs = append(cronExprs, cronExpr)
	}
	return
}

func getCronExprByRule(ruleId int) (cronName, cronExpr string) {
	var r ocommon.ResultInfo
	var ruleInfo dao.RuleOnline
	r = dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, ruleId)
	if !r.IsOk() {
		plog.EngineLG.WithFields(CronFields()).Errorf("failed to get rule info from database, rule_id:[%d], err:[%v]", ruleId, r)
		return
	}
	cronName = fmt.Sprintf("%s_%d_%d_%d", base.TASK_TYPE_CRONTAB, ruleInfo.ModuleId, ruleInfo.TaskId, ruleInfo.ID)
	arr := strings.Split(ruleInfo.CronTime, ":")
	if len(arr) < 3 {
		plog.EngineLG.WithFields(CronFields()).Errorf("failed to parse cron time, rule_id:[%d], cron_time:[%s]", ruleId, ruleInfo.CronTime)
		return
	}

	switch ruleInfo.CronPeriod {
	case base.CRON_PERIOD_DAY:
		cronExpr = fmt.Sprintf("%s %s * * *", arr[1], arr[0])
	case base.CRON_PERIOD_MONTH:
		cronExpr = fmt.Sprintf("%s %s %s * *", arr[1], arr[0], ruleInfo.CronDay)
	case base.CRON_PERIOD_WEEK:
		weekDay := convertChineseWeekToNumber(ruleInfo.CronDay)
		cronExpr = fmt.Sprintf("%s %s * * %s", arr[1], arr[0], weekDay)
	}
	return
}

func cronTrigger(cronName string) func() {
	return func() {
		parts := strings.Split(cronName, "_")
		lastPartStr := parts[len(parts)-1]
		ruleId, err := strconv.Atoi(lastPartStr)
		if err != nil {
			plog.EngineLG.WithFields(CronFields()).Errorf("failed to parse cron name, cronName:[%s], lastPart:[%s], err:[%v]", cronName, lastPartStr, err)
			return
		}
		var ruleInfo dao.RuleOnline
		r := dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, ruleId)
		if !r.IsOk() {
			plog.EngineLG.WithFields(CronFields()).Errorf("failed to get rule info from database, cronName:[%s], ruleId:[%d], err:[%v]", cronName, ruleId, r)
			return
		}
		plog.EngineLG.WithFields(CronFields()).Debugf("trigger cron, cronName:[%s], ruleId:[%d]", cronName, ruleId)
		comm.ScalerCh.EventCornChan <- ruleInfo
	}
}

func CronFields() map[string]interface{} {
	return map[string]interface{}{
		"trace_id": "crontab_0123456789abcdef",
	}
}

// convertChineseWeekToNumber 将中文星期转换为数字
// 一、二、三、四、五、六、日 -> 1、2、3、4、5、6、7
func convertChineseWeekToNumber(chineseWeek string) string {
	weekMap := map[string]string{
		"一": "1", // 星期一
		"二": "2", // 星期二
		"三": "3", // 星期三
		"四": "4", // 星期四
		"五": "5", // 星期五
		"六": "6", // 星期六
		"日": "7", // 星期日
	}

	if number, exists := weekMap[chineseWeek]; exists {
		return number
	}

	// 如果已经是数字格式，直接返回
	return chineseWeek
}
