package cron_trigger

import (
	"context"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

// TaskManager 管理所有定时任务
type CronTask struct {
	cron       *cron.Cron
	tasks      map[string]cron.EntryID
	mu         sync.Mutex
	cancelFunc context.CancelFunc
}

// NewTaskManager 创建新的任务管理器
func NewCronTask(logFilePath string) (*CronTask, error) {
	_, cancel := context.WithCancel(context.Background())

	// 创建cron实例
	c := cron.New()

	return &CronTask{
		cron:       c,
		tasks:      make(map[string]cron.EntryID),
		cancelFunc: cancel,
	}, nil
}

func (tm *CronTask) AddTask(name, schedule string, task func()) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// 包装任务函数，添加任务名称日志
	wrappedTask := func() {
		start := time.Now()
		plog.EngineLG.WithFields(CronFields()).Infof("[BEGIN] task: %s", name)
		defer func() {
			duration := time.Since(start)
			plog.EngineLG.WithFields(CronFields()).Infof("[END] task: %s, Duration: %v", name, duration)
		}()

		task()
	}

	id, err := tm.cron.AddFunc(schedule, wrappedTask)
	if err != nil {
		return err
	}

	tm.tasks[name] = id
	plog.EngineLG.WithFields(CronFields()).Infof("task added - Name: %s, Schedule: %s, EntryID: %d", name, schedule, id)
	return nil
}

func (tm *CronTask) RemoveTask(name string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	id, exists := tm.tasks[name]
	if !exists {
		return fmt.Errorf("task %s not found", name)
	}

	tm.cron.Remove(id)
	delete(tm.tasks, name)

	plog.EngineLG.WithFields(CronFields()).Infof("task removed - Name: %s, EntryID: %d", name, id)
	return nil
}

// Start 启动所有定时任务
func (tm *CronTask) Start() {
	plog.EngineLG.WithFields(CronFields()).Info("starting task manager...")
	tm.cron.Start()
}

// Stop 优雅停止所有定时任务
func (tm *CronTask) Stop() {
	plog.EngineLG.WithFields(CronFields()).Info("stopping task manager...")

	// 发送停止信号
	tm.cancelFunc()

	// 停止cron调度器(会等待正在执行的任务完成)
	ctx := tm.cron.Stop()

	// 等待所有任务完成
	select {
	case <-ctx.Done():
		plog.EngineLG.WithFields(CronFields()).Info("all tasks finished gracefully")
	case <-time.After(30 * time.Second):
		plog.EngineLG.Info("timeout waiting for tasks to finish")
	}
}
