package engine

import (
	libapptreeV2 "dxm/noah-sdk/noah_golang_sdk/src/libapptree_v2"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/collecter"
	"dxm/siod_sre/auto-scaler/engine/cron_trigger"
	"dxm/siod_sre/auto-scaler/engine/decision_maker"
	"dxm/siod_sre/auto-scaler/engine/event_generator"
	"dxm/siod_sre/auto-scaler/engine/executor"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/tool_public"
	"fmt"
	"os"
	"time"
)

var (
	Engine *ScalerEngine
)

type ScalerEngine struct {
	// 指标采集器
	CollectorManager *collecter.CollecterManager
	// 时间触发器
	CronManager *cron_trigger.TaskManager
	// 事件生成器
	EventManager *event_generator.EventGeneratorManager
	// 决策器
	DecisionManager *decision_maker.DecisionMakerManager
	// 执行器
	ExecutorManager *executor.IExecutorManager
}

func NewEngine() *ScalerEngine {
	engine := &ScalerEngine{
		CollectorManager: collecter.CollectEngine,
		CronManager:      cron_trigger.CronTaskManager,
		EventManager:     event_generator.EventManager,
		DecisionManager:  decision_maker.DecisionManager,
		ExecutorManager:  executor.ExecutorManager,
	}

	return engine
}

func init() {
	Engine = NewEngine()
}

func (c *ScalerEngine) Run() {
	// 启动时先进行节点注册
	if !registerCurrentNode() {
		fmt.Println("failed to register current node, exiting CheckMaster")
		plog.EngineLG.Errorf("failed to register current node, exiting CheckMaster")
		os.Exit(1)
	}

	go c.CollectorManager.Run()
	go c.CronManager.Run()
	go c.EventManager.Run()
	go c.DecisionManager.Run()
	go c.ExecutorManager.Run()
	go CheckMaster()
}

func CheckMaster() {
	ticker := time.NewTicker(time.Duration(global.MasterCheckInterval) * time.Second)
	defer ticker.Stop()
	time.Sleep(3 * time.Second)
	for range ticker.C {
		// 执行心跳上报和主节点选择
		if isMaster := performHeartbeatAndElection(); isMaster {
			sendManagerSignals(true)
		} else {
			sendManagerSignals(false)
		}
	}
}

// sendManagerSignals 发送管理器信号
func sendManagerSignals(start bool) {
	if start {
		Engine.CollectorManager.SendStartSignal()
		Engine.CronManager.SendStartSignal()
	} else {
		Engine.CollectorManager.SendStopSignal()
		Engine.CronManager.SendStopSignal()
	}
}

// registerCurrentNode 注册当前节点
func registerCurrentNode() bool {
	// 获取本机IP地址
	localIP, err := tool_public.GetLocalIP()
	if err != nil {
		plog.EngineLG.Errorf("failed to get local IP address, err:%v", err)
		return false
	}

	// 获取实例信息进行验证
	instanceList, err := apptree.GetInstanceByBNSWithDisabledV2(global.MasterBns)
	if err != nil {
		plog.EngineLG.Errorf("failed to get instance from apptree, err:%v", err)
		return false
	}

	// 检查本机IP是否在实例列表中
	var foundInstance *libapptreeV2.InstanceInfo
	for _, ins := range instanceList {
		if ins.PrivateIp == localIP {
			foundInstance = &ins
			break
		}
	}

	if foundInstance == nil {
		plog.EngineLG.Errorf("current IP %s not found in instance list", localIP)
		return false
	}

	// 获取主机名
	hostname, _ := os.Hostname()
	if hostname == "" {
		hostname = "unknown"
	}

	// 创建节点信息
	nodeInfo := dao.ClusterNodes{
		Offset:   int(foundInstance.Offset),
		Hostname: hostname,
		IP:       localIP,
		Role:     dao.NODE_ROLE_SLAVE, // 初始注册为slave
	}

	// 注册节点
	nodeID, r := dao.CreateClusterNodesPtr().RegisterNode(nodeInfo)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to register node, err:%v", r)
		return false
	}

	plog.EngineLG.Infof("node registered successfully, ID:%d, IP:%s, Offset:%d", nodeID, localIP, nodeInfo.Offset)
	return true
}

// performHeartbeatAndElection 执行心跳上报和主节点选择
func performHeartbeatAndElection() bool {
	localIP := getCurrentNodeIP()
	if localIP == "" {
		return false
	}

	if !updateHeartbeat(localIP) {
		return false
	}

	masterNode := electMasterNode()
	if masterNode == nil {
		return false
	}

	currentNode := getCurrentNodeInfo(localIP)
	if currentNode == nil {
		return false
	}

	return updateNodeRole(localIP, masterNode, currentNode)
}

// getCurrentNodeIP 获取当前节点IP
func getCurrentNodeIP() string {
	localIP, err := tool_public.GetLocalIP()
	if err != nil {
		plog.EngineLG.Errorf("failed to get local IP address, err:%v", err)
		return ""
	}
	return localIP
}

// updateHeartbeat 更新节点心跳
func updateHeartbeat(localIP string) bool {
	r := dao.CreateClusterNodesPtr().UpdateNodeHeartbeat(localIP)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to update heartbeat, err:%v", r)
		return false
	}
	return true
}

// electMasterNode 选举master节点
func electMasterNode() *dao.ClusterNodes {
	heartbeatTimeout := time.Duration(global.MasterCheckInterval*3) * time.Second
	masterNode, r := dao.CreateClusterNodesPtr().GetMasterNodeByElection(heartbeatTimeout)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to get master node by election, err:%v", r)
		return nil
	}
	return &masterNode
}

// getCurrentNodeInfo 获取当前节点信息
func getCurrentNodeInfo(localIP string) *dao.ClusterNodes {
	currentNode, r := dao.CreateClusterNodesPtr().GetNodeByIP(localIP)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to get current node info, err:%v", r)
		return nil
	}
	return &currentNode
}

// updateNodeRole 更新节点角色
func updateNodeRole(localIP string, masterNode, currentNode *dao.ClusterNodes) bool {
	isMaster := (masterNode.IP == localIP)

	if isMaster {
		return handleMasterRole(localIP, currentNode)
	} else {
		return handleSlaveRole(localIP, currentNode, masterNode)
	}
}

// handleMasterRole 处理master角色
func handleMasterRole(localIP string, currentNode *dao.ClusterNodes) bool {
	if currentNode.Role != dao.NODE_ROLE_MASTER {
		if !updateRoleInDB(localIP, dao.NODE_ROLE_MASTER) {
			return false
		}

		// 清理心跳过期的旧master节点
		cleanupExpiredMasterNodes(localIP)

		plog.EngineLG.Infof("current node promoted to master, IP:%s, Offset:%d",
			localIP, currentNode.Offset)
	} else {
		plog.EngineLG.Debugf("current node is master, IP:%s, Offset:%d",
			localIP, currentNode.Offset)
	}
	return true
}

// handleSlaveRole 处理slave角色
func handleSlaveRole(localIP string, currentNode, masterNode *dao.ClusterNodes) bool {
	if currentNode.Role != dao.NODE_ROLE_SLAVE {
		if !updateRoleInDB(localIP, dao.NODE_ROLE_SLAVE) {
			return false
		}
		plog.EngineLG.Infof("current node demoted to slave, IP:%s, Offset:%d, Master IP:%s, Master Offset:%d",
			localIP, currentNode.Offset, masterNode.IP, masterNode.Offset)
	} else {
		plog.EngineLG.Debugf("current node is slave, IP:%s, Offset:%d, Master IP:%s, Master Offset:%d",
			localIP, currentNode.Offset, masterNode.IP, masterNode.Offset)
	}
	return false
}

// updateRoleInDB 更新数据库中的角色
func updateRoleInDB(localIP, role string) bool {
	r := dao.CreateClusterNodesPtr().UpdateNodeRole(localIP, role)
	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to update node role to %s, err:%v", role, r)
		return false
	}
	return true
}

// cleanupExpiredMasterNodes 清理心跳过期的旧master节点
func cleanupExpiredMasterNodes(excludeIP string) {
	heartbeatTimeout := time.Duration(global.MasterCheckInterval*3) * time.Second
	cleanedCount, r := dao.CreateClusterNodesPtr().CleanupExpiredMasterNodes(heartbeatTimeout, excludeIP)

	if !r.IsOk() {
		plog.EngineLG.Errorf("failed to cleanup expired master nodes, err:%v", r)
		return
	}

	if cleanedCount > 0 {
		plog.EngineLG.Infof("cleaned up %d expired master nodes, excluded IP:%s", cleanedCount, excludeIP)
	} else {
		plog.EngineLG.Debugf("no expired master nodes found to cleanup, excluded IP:%s", excludeIP)
	}
}

/*
	指标采集器(collector)
	1. 不同组件 采集不同的指标项 抽象化
	2. 存储近1个月的信息，并自动删除

	时间触发器(cron_trigger)
	1. 定时任务管理，管理所有定时任务
	2. 定时任务触发，将信号通过管道传给event_generator，生成对应的事件

	事件生成器(event_generator)
	1. 根据采集数据和规则信息生成事件；或者根据外部输入生成事件
	2. 识别扩容或缩容事件，并存储相关事件信息

	决策器(decision_maker)
	1. 事件通知到决策器根据规则决策，根据生成的事件和当前的容量状态决策是否需要扩缩容（二次决策）
	2. 计算需要扩容或缩容的数量

	执行器（executor）
	1. 根据决策情况及组件类型执行相应的 扩缩容动作

	扩缩容事件生成逻辑
	1. 采集器独立运行，通过采集各个系统的监控或容量指标，存储到数据库（后续改为时序数据库）。每一个模块为一个采集协程，并通过数据库全局控制并发度（后续实现，目前单个进行执行）
	2. 采集器通过管道将采集数据点传递给事件生成器，并根据规则信息生成相关的扩容或缩容事件。事件生成器会根据模块对应的任务列表生成任务和模块关联的扩容和缩容事件。

	扩缩容决策及执行逻辑
	1. 决策器将每一个模块作为一个协程，根据其对应任务的规则配置和对应的扩缩容事件，综合决策扩容或缩容。
	2. 根据决策信息和当前服务容量情况计算扩缩容的数据量，计算扩缩容的预期实例数量。（可能发生执行动作和预期实例数相反的情况，这种记录并终止此次动作）
	3. 执行信息通过管道传递到保护逻辑，根据相关保护逻辑判断是否需要拦截。拦截信息存储并推送到群
	4. 若需要执行，则调用相应的执行器，执行相关的扩容逻辑。

*/
