package executor

import (
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"fmt"
	"time"
)

type ExecutorModel struct {
	comm.ModuleOneTaskParam
	EngineData dao.EngineInfo
}

var _ IExecutor = &ExecutorModel{}

func (e *ExecutorModel) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", e.TaskInfo.TaskType, e.ModuleInfo.ID, e.TaskInfo.ID, e.RuleInfo.ID)
}

func (e *ExecutorModel) CanExecute() bool {
	// 实现具体的可执行性检查逻辑
	// 例如：检查资源是否可用、依赖是否满足等
	return true
}

func (e *ExecutorModel) Execute() (bool, string) {
	// 实现具体的执行逻辑
	// 例如：调用API、执行命令、处理数据等
	return true, ""
}

func (e *ExecutorModel) ExecuteWithRetry(maxRetries int) (success bool, failedReason string) {
	for i := 0; i < maxRetries; i++ {
		if success, failedReason = e.Execute(); success {
			return true, ""
		}

		time.Sleep(time.Duration(i) * time.Minute)
	}
	return false, NOTICE_ERROR_REQUEST_FAILED
}

func (e *ExecutorModel) Record() (bool, string) {

	return true, ""
}

func (e *ExecutorModel) UpdateStatus(status int, reason string) bool {
	return true
}

func (e *ExecutorModel) Notify(string) bool {
	return true
}

func (e *ExecutorModel) Init() bool {
	return true
}

func (e *ExecutorModel) RunOne() {

}

func (e *ExecutorModel) HandleCallback(input interface{}) error {
	return nil
}

func (e *ExecutorModel) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   e.ModuleInfo.ID,
		"module_name": e.ModuleInfo.Name,
		"task_id":     e.TaskInfo.ID,
		"rule_id":     e.RuleInfo.ID,
		"trace_id":    e.TraceID,
	}
}
