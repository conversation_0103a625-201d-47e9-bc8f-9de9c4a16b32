package executor

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/oerrno"
	"dxm/siod-cloud/go-common-lib/ointeraction"
	"dxm/siod-cloud/go-common-lib/olog"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/errno"
	"dxm/siod_sre/auto-scaler/pkg/httpx"
)

type ModelEngineData struct {
	Name    string   `json:"name"`
	AppId   int      `json:"app_id"`
	StateId int      `json:"state_id"`
	Version string   `json:"version"`
	Type    string   `json:"type" decription:"模型类型, 扩容:up,缩容:down"`
	Num     int      `json:"num" decription:"扩容数量"`
	IpList  []string `json:"ip_list"`

	Res ModelEngineRes
}

type ModelEngineRes struct {
	ResCode   string `json:"res_code"`
	ResMsg    string `json:"res_msg"`
	Timestamp int    `json:"timestamp"`
	Data      struct {
		TaskId int `json:"task_id"`
	} `json:"data"`
}

func (m *ModelEngineData) init(e EngineRunData) (r ocommon.ResultInfo) {
	m.Name = e.ServiceName
	arr := strings.Split(m.Name, "-")
	if len(arr) < 2 {
		olog.Error("model name error when exec model engine, name:[%s]", m.Name)
		return
	}
	appId, err1 := strconv.Atoi(arr[0])
	stateId, err2 := strconv.Atoi(arr[1])
	if err1 != nil || err2 != nil {
		olog.Error("failed to strconv string to int when exec model engine, name:[%s], err1:[%v], err2:[%v]", m.Name, err1, err2)
		return
	}
	m.AppId = appId
	m.StateId = stateId

	// 获取模型版本
	riskModelInfo, r := dao_ocean.CreateRiskModelInfoPtr().GetRiskModelInfoByAppId(m.AppId, m.StateId, "")
	if !r.IsOk() {
		olog.Error("failed to get risk model info when exec model engine, name:[%s], err: %v", m.Name, r)
		return
	}
	m.Version = riskModelInfo.Version
	m.Type = e.Action
	// m.Num = e.ExpectedTotalNum
	return
}

func (m *ModelEngineData) exec() (r ocommon.ResultInfo) {
	var res ModelEngineRes
	type modelInput struct {
		AppId   string `json:"app_id"`
		StateId string `json:"state_id"`
		Version string `json:"version"`
		FsPath  string `json:"fs_path"`
		Type    string `json:"type" decription:"模型类型, 扩容:up,缩容:down"`
		Num     int    `json:"num" decription:"扩容数量"`
	}
	var inputData = modelInput{
		AppId:   fmt.Sprintf("%d", m.AppId),
		StateId: fmt.Sprintf("%d", m.StateId),
		Version: m.Version,
		FsPath:  "-",
		Type:    m.Type,
		Num:     m.Num,
	}
	postJson, _ := json.Marshal(&inputData)
	// url := fmt.Sprintf("http://%s/api/scale_model", apptree.GetServicePort("predictor-proxy.risk-mdm"))
	url := fmt.Sprintf("http://%s/api/scale_model", "************:8900") // 测试环境
	// url := fmt.Sprintf("http://%s/api/scale_model", "***********:8900")
	resp, r := httpx.SendHttpBodyRequest(url, ointeraction.HTTP_METHOD_POST_BODY_BY_CONTENT, postJson)
	if !r.IsOk() {
		return
	}
	err := json.Unmarshal([]byte(resp), &res)
	if err != nil {
		r.ErrNo = oerrno.ERR_JSON_UNMARSHAL
		r.ErrMsg = oerrno.ErrnoMap[r.ErrNo]
		return
	}

	m.Res = res
	return
}

func (m *ModelEngineData) resJudge() (r ocommon.ResultInfo) {
	if m.Res.ResCode != "0" {
		r.ErrNo = errno.ERR_MODEL_ENGINE_TALK_FAILED
		r.ErrMsg = m.Res.ResMsg
	}

	return
}
