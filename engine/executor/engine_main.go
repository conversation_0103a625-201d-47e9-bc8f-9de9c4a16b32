package executor

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/errno"
	"fmt"
	"time"

	"dxm/siod-cloud/go-common-lib/oquery"
)

// todo：子任务管理

type EngineRunData struct {
	dao.EngineInfo
}

// 定时任务触发
func EngineRun() {
	// 查询engine任务,手动任务优先执行
	ExecuteManualTask()
	// 执行其他类型任务
	ExecuteOtherTask()

}

// 执行手动任务
func ExecuteManualTask() (r ocommon.ResultInfo) {
	var engineInfo []dao.EngineInfo
	r = dao.CreateEngineInfoPtr().SearchByColumn(&engineInfo, &dao.EngineInfo{
		TaskType: base.TASK_TYPE_MANUAL,
		Status:   base.ENGINE_TASK_STATUS_READY,
		Dflag:    0,
	}, []string{"TaskType", "Status", "Dflag"})
	if !r.IsOk() {
		olog.Error("failed to get engine data from database when executing manual task, error: %v", r)
		return
	}

	for _, v := range engineInfo {
		// 执行任务
		var object EngineRunData
		object.EngineInfo = v
		object.execTask()
	}
	return
}

// 执行非手动任务
func ExecuteOtherTask() {
	var engineInfo []dao.EngineInfo
	query := oquery.NewQueryStructOfTable()
	query.AddConditonsByOperator("TaskType", oquery.OP_NOT_EQUAL, base.TASK_TYPE_MANUAL)
	query.AddConditonsByOperator("Status", oquery.OP_EQUAL, base.ENGINE_TASK_STATUS_READY)
	query.AddConditonsByOperator("Dflag", oquery.OP_EQUAL, 0)
	r := dao.CreateEngineInfoPtr().SearchByQuery(&engineInfo, query)
	if !r.IsOk() {
		olog.Error("failed to get engine data from database when executing other task, error: %v", r)
		return
	}
	for _, v := range engineInfo {
		// 执行任务
		var object EngineRunData
		object.EngineInfo = v
		object.execTask()

	}
}

// 执行任务
func (e *EngineRunData) execTask() (r ocommon.ResultInfo) {
	switch e.TaskStatus {
	case base.TASK_STATUS_DISABLE: // 已下线
		// 不通告不执行
		olog.Warn("task status disable, module_id:[%d], task_id:[%d], rule_id:[%d]", e.ModuleId, e.TaskId, e.RuleId)
		return
	case base.TASK_STATUS_TEST_RUN: // 试运行
		// 通告不执行
		e.SendStartSuccessMessageHi("")
	case base.TASK_STATUS_ENABLE: // 执行任务
		e.SendStartSuccessMessageHi("")
		switch e.ScaleEngine {
		case base.ENGINE_NAME_MODEL:
			if !e.execTaskCheckData() {
				olog.Warn("task check data failed, service_name:[%s], module_id:[%d], task_id:[%d], rule_id:[%d]",
					e.ServiceName, e.ModuleId, e.TaskId, e.RuleId)
				return
			}
			var object = new(ModelEngineData)
			r = object.init(*e)
			if !r.IsOk() {
				olog.Error("failed to init model engine, module_id:[%d], task_id:[%d], rule_id:[%d], err:[%v]", e.ModuleId, e.TaskId, e.RuleId, r)
				return
			}
			r = object.exec()
			if !r.IsOk() {
				olog.Error("failed to exec model engine, module_id:[%d], task_id:[%d], rule_id:[%d], err:[%v]", e.ModuleId, e.TaskId, e.RuleId, r)
				e.SendEndFailedMessageHi("request model engine failed")
				return
			}
			if object.Res.ResCode != "0" {
				r.ErrNo = errno.ERR_MODEL_ENGINE_TALK_FAILED
				r.ErrMsg = object.Res.ResMsg
				e.SendEndFailedMessageHi(fmt.Sprintf("model engine talk failed: %s", r.ErrMsg))
				return
			}
			e.SendEndSuccessMessageHi("")
			// 更新状态为成功
			_, r = dao.CreateEngineInfoPtr().UpdateByPk(&dao.EngineInfo{Status: base.ENGINE_TASK_STATUS_SUCCESS, EngineTaskId: object.Res.Data.TaskId,
				UpdateTime: time.Now(), FinishTime: time.Now()},
				[]string{"Status", "EngineTaskId", "UpdateTime", "FinishTime"}, e.ID)
			if !r.IsOk() {
				olog.Error("failed to update engine info for database, engine_id:[%d], service:[%s], err:[%v]", e.ID, e.ServiceName, r)
				return
			}
		case base.ENGINE_NAME_SERVICE_CAPACITY:
			// todo: 扩缩容平台
		default:
			olog.Error("engine name invailed, module_id:[%d], task_id:[%d], rule_id:[%d], engine_name:[%s]", e.ModuleId, e.TaskId, e.RuleId, e.ScaleEngine)
		}
	default:
		r = ocommon.GenResultInfo(errno.ERR_TASK_PARA_INVAILED, "para invailed", nil, nil)
		olog.Error("task status error, module_id:[%d], task_id:[%d], rule_id:[%d], task_status:[%s]",
			e.ModuleId, e.TaskId, e.RuleId, e.TaskStatus)
		return
	}
	return
}

func (e *EngineRunData) execTaskCheckData() (isContinue bool) {
	// 校验时间是否满足
	if time.Now().Add(-20 * time.Minute).After(e.CreateTime) {
		olog.Warn("task create time is too old, service_name:[%s], module_id:[%d], task_id:[%d], rule_id:[%d]",
			e.ServiceName, e.ModuleId, e.TaskId, e.RuleId)
		e.SendEndFailedMessageHi("task create time is too old")
		return
	}

	// 校验执行动作，缩容动作不可执行
	if e.Action == base.ACTION_DOWN {
		olog.Warn("should not exec down task, service_name:[%s], module_id:[%d], task_id:[%d], rule_id:[%d]",
			e.ServiceName, e.ModuleId, e.TaskId, e.RuleId)
		e.SendEndFailedMessageHi("should not exec down task")
		return
	}
	return true
}
