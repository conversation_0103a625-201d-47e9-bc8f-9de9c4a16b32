package executor

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/engine/comm"
)

type IExecutor interface {
	// 判断执行器是否可以执行
	CanExecute() bool                               // 执行器是否可以执行
	Execute() (bool, string)                        // 执行函数
	ExecuteWithRetry(maxRetries int) (bool, string) // 带重试的执行函数

	// 记录执行器执行状态
	Record() (bool, string)                      // 记录状态
	UpdateStatus(status int, reason string) bool // 更新状态
	Notify(failedReason string) bool             // 通知状态

	// 回调
	HandleCallback(interface{}) error // 回调函数

	// 初始化执行器
	Init() bool
	RunOne()
	GenName() string
}

var ExecutorManager *IExecutorManager

type IExecutorManager struct {
	ExecutorInfo map[string]IExecutor
}

func NewExecutorManager() {
	ExecutorManager = &IExecutorManager{
		ExecutorInfo: make(map[string]IExecutor, 10),
	}
}

func (t *IExecutorManager) Run() {
	for task := range comm.ScalerCh.ExecutorChan {
		go func(task *comm.ModuleOneTaskParam) {
			executor := NewExecutor(*task)
			if executor == nil {
				return
			}
			executor.Init()
			name := executor.GenName()
			t.ExecutorInfo[name] = executor
			executor.RunOne()
		}(task)
	}
}

func NewExecutor(p comm.ModuleOneTaskParam) IExecutor {
	switch p.ModuleInfo.ScaleEngine {
	case base.ENGINE_NAME_SERVICE_CAPACITY:
		return &ExecutorService{
			ModuleOneTaskParam: p,
		}
	case base.ENGINE_NAME_MODEL:
		return &ExecutorModel{
			ModuleOneTaskParam: p,
		}
	}
	return nil
}

// 初始化管理器（在init函数或main中调用）
func init() {
	NewExecutorManager()
}
