package executor

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"dxm/siod_sre/auto-scaler/pkg/apptree"
	"dxm/siod_sre/auto-scaler/pkg/service_capacity"
	"encoding/json"
	"fmt"
	"strings"
	"time"
)

type ExecutorService struct {
	comm.ModuleOneTaskParam
}

var _ IExecutor = &ExecutorService{}

func (e *ExecutorService) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", e.TaskInfo.TaskType, e.ModuleInfo.ID, e.TaskInfo.ID, e.RuleInfo.ID)
}

func (e *ExecutorService) CanExecute() (canRun bool) {
	// 检查资源是否可用、依赖是否满足等(待todo)
	// 查询弹性伸缩记录，如果存在运行任务且运行时间小于1天，则不执行当前任务，发送通告提醒
	dataList, r := dao.CreateEngineInfoPtr().GetEngineInfoByStatus(e.TaskInfo.IdcTag, []int{base.ENGINE_TASK_STATUS_RUNNING})
	if !r.IsOk() {
		plog.EngineLG.Errorf("query engine info failed, err: %v", r)
		return
	}
	for _, d := range dataList {
		if time.Since(d.CreateTime).Hours() < 24 {
			e.Notify(NOTICE_ERROR_SAME_TAG_RUNNING)
			plog.EngineLG.Warnf("engine task is running")
			return false
		}
	}
	return true
}

func (e *ExecutorService) Execute() (bool, string) {
	reqObject := new(service_capacity.ExecuteRequest)
	arr := strings.Split(e.ModuleInfo.ServiceName, ".")
	if len(arr) != 2 {
		plog.EngineLG.WithFields(e.Fields()).Errorf("service name format error")
		return false, NOTICE_ERROR_SERVICE_NAME_FORMAT
	}
	var tagMap = make(map[string]string)
	tagMap["idc"] = e.TaskInfo.IdcTag
	allTagMap := apptree.GetInstanceTag(e.ModuleInfo.ServiceName, tagMap)
	reqObject = &service_capacity.ExecuteRequest{
		CapacityTaskID: e.EngineInfo.ID,
		ProductName:    arr[1],
		AppName:        arr[0],
		PlatformName:   "capacity",
		ScaleType:      base.ScaleActionMap[e.DecisionInfo.Action],
		ScaleModel:     base.ScaleModelMap[e.TaskInfo.SchedMode],
		NoahTag:        tagMap,
		CustomTag:      allTagMap,
		TargetNum:      e.EngineInfo.ExpectedNum,
	}

	reqInfoJson, _ := json.Marshal(&reqObject)
	plog.EngineLG.WithFields(e.Fields()).Debugf("request service capacity, req: %s", string(reqInfoJson))
	res, err := reqObject.Request()
	if err != nil {
		plog.EngineLG.WithFields(e.Fields()).Errorf("request service capacity failed, err: %v", err)
		return false, NOTICE_ERROR_REQUEST_FAILED
	}
	responseJson, _ := json.Marshal(&res)
	e.EngineInfo.ResponseInfo = string(responseJson)
	dao.CreateEngineInfoPtr().UpdateByPk(&dao.EngineInfo{
		ScaleId: res.Data.ScaleID, ResponseInfo: string(responseJson), RequestInfo: string(reqInfoJson)},
		[]string{"ScaleId", "ResponseInfo", "RequestInfo"}, e.EngineInfo.ID)

	plog.EngineLG.WithFields(e.Fields()).Debugf("request service capacity, res: %s", string(responseJson))
	if res.ErrNo == service_capacity.ERROR_CODE_NO_NEED_TO_EXECUTE {
		e.UpdateStatus(base.ENGINE_TASK_STATUS_SUCCESS, NOTICE_ERROR_SERVICE_CAPACITY_RESPONSE)
		plog.EngineLG.WithFields(e.Fields()).Debug("service capacity response no need to execute")
		return true, ""
	}

	if res.ErrNo != 0 {
		// todo: 后续需要细化错误码处理动作
		plog.EngineLG.WithFields(e.Fields()).Errorf("service capacity response failed, err: %v", res)
		return false, NOTICE_ERROR_SCALE_ENGINE_FAILED
	}

	// todo：返回无错误，对扩容实例进行验证
	return true, ""
}

func (e *ExecutorService) ExecuteWithRetry(maxRetries int) (success bool, failedReason string) {
	for i := 0; i < maxRetries; i++ {
		if success, failedReason = e.Execute(); success {
			plog.EngineLG.Debug("request service capacity succeeded")
			return true, ""
		}
		plog.EngineLG.WithFields(e.Fields()).Warnf("request service capacity failed, retry: %d, reason: %s", i, failedReason)
		time.Sleep(time.Duration(i) * time.Minute)
	}
	return false, NOTICE_ERROR_REQUEST_FAILED
}

func (e *ExecutorService) Record() (bool, string) {
	var (
		finalNumInfo dao.ProcessInfo
		r            ocommon.ResultInfo
		engineId     int
	)
	err := json.Unmarshal([]byte(e.DecisionInfo.ProcessInfo), &finalNumInfo)
	if finalNumInfo.FinalNum == 0 || err != nil {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to parse decision info: %v", err)
		return false, NOTICE_ERROR_GET_DECISION_DATA
	}

	e.EngineInfo = dao.EngineInfo{
		ModuleId:       e.ModuleInfo.ID,
		ServiceName:    e.ModuleInfo.Name,
		TaskId:         e.TaskInfo.ID,
		RuleId:         e.RuleInfo.ID,
		IdcTag:         e.TaskInfo.IdcTag,
		TaskType:       e.TaskInfo.TaskType,
		TaskStatus:     e.TaskInfo.TaskStatus,
		ScaleEngine:    e.ModuleInfo.ModuleType,
		Action:         e.DecisionInfo.Action,
		Concurrency:    1,
		AdjustmentType: e.TaskInfo.SchedMode,
		AdjustNum:      e.DecisionInfo.AdjustmentValue,
		CurrentNum:     e.DecisionInfo.CurrentNum,
		ExpectedNum:    finalNumInfo.FinalNum,
		Status:         base.ENGINE_TASK_STATUS_RUNNING,
		CreateTime:     time.Now(),
		UpdateTime:     time.Now(),
		FinishTime:     global.ZeroTime,
		LastModifyTime: time.Now(),
	}
	engineId, r = dao.CreateEngineInfoPtr().Insert(&e.EngineInfo)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to insert engine info: %v", r)
		return false, NOTICE_ERROR_REQUEST_DATABASE
	}
	e.EngineInfo.ID = engineId
	dao.CreateDecisionRecordPtr().UpdateByPk(&dao.DecisionRecord{EngineId: engineId}, []string{"EngineId"}, e.DecisionInfo.ID)
	return true, ""
}

func (e *ExecutorService) UpdateStatus(status int, reason string) bool {
	var (
		r ocommon.ResultInfo
	)
	e.EngineInfo.Status = status
	_, r = dao.CreateEngineInfoPtr().UpdateByPk(
		&dao.EngineInfo{Status: status, UpdateTime: time.Now(), FinishTime: time.Now(), FailureType: reason},
		[]string{"Status", "FailureType", "UpdateTime", "FinishTime"}, e.EngineInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("failed to update engine status: %v", r)
		return false
	}
	_, r = dao.CreateDecisionRecordPtr().UpdateByColumn(
		&dao.DecisionRecord{Status: status}, []string{"Status"},
		&dao.DecisionRecord{EngineId: e.EngineInfo.ID}, []string{"EngineId"})

	if e.TaskInfo.TaskType == base.TASK_TYPE_MANUAL {
		switch status {
		case base.ENGINE_TASK_STATUS_SUCCESS:
			dao.CreateTaskInfoPtr().UpdateTaskStatus(e.TaskInfo.ID, base.TASK_MANUAL_STATUS_SUCCESS)
		case base.ENGINE_TASK_STATUS_FAILED:
			dao.CreateTaskInfoPtr().UpdateTaskStatus(e.TaskInfo.ID, base.TASK_MANUAL_STATUS_FAILED)
		}
	}
	return true
}

func (e *ExecutorService) Notify(failedReason string) bool {
	e.ModuleOneTaskParam.Notify()
	return true
}

func (e *ExecutorService) Init() bool {
	e.Phase = comm.EngineStartPhase
	return true
}

func (e *ExecutorService) RunOne() {
	var (
		success    bool
		execResult string
	)

	if e.CanExecute() {
		if success, execResult = e.Record(); !success {
			e.UpdateStatus(base.ENGINE_TASK_STATUS_FAILED, execResult)
			plog.EngineLG.WithFields(e.Fields()).Error("record failed")
		}
		if success, execResult = e.ExecuteWithRetry(3); success {
			plog.EngineLG.WithFields(e.Fields()).Debugf("execution succeeded")
		} else {
			e.UpdateStatus(base.ENGINE_TASK_STATUS_FAILED, execResult)
			plog.EngineLG.WithFields(e.Fields()).Error("execution failed after retries")
		}
		e.Notify(execResult)
	} else {
		e.Notify(NOTICE_ERROR_SAME_TAG_RUNNING)
		plog.EngineLG.WithFields(e.Fields()).Warn("execution not allowed")
	}
}
func (e *ExecutorService) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   e.ModuleInfo.ID,
		"module_name": e.ModuleInfo.Name,
		"task_id":     e.TaskInfo.ID,
		"rule_id":     e.RuleInfo.ID,
		"trace_id":    e.TraceID,
	}
}
func (e *ExecutorService) JobFields() map[string]interface{} {
	return map[string]interface{}{
		"module_id": e.EngineInfo.ModuleId,
		"task_id":   e.EngineInfo.TaskId,
		"rule_id":   e.EngineInfo.RuleId,
		"trace_id":  fmt.Sprintf("%d_%d_%d_%d", e.EngineInfo.ModuleId, e.EventInfo.TaskId, e.EngineInfo.RuleId, e.EngineInfo.ID),
	}
}

func (e *ExecutorService) HandleCallback(input interface{}) error {
	var (
		inputData    = input.(service_capacity.ScaleCallbackResponse)
		r            ocommon.ResultInfo
		successNum   int
		actualNum    int
		callbackData string
	)

	r = dao.CreateEngineInfoPtr().SearchByColumn(&e.EngineInfo, &dao.EngineInfo{ScaleId: inputData.ScaleID}, []string{"ScaleId"})
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.JobFields()).Errorf("failed to search engine info: %v", r)
		return fmt.Errorf("failed to search engine info: %v", r)
	}

	if e.EngineInfo.ScaleId == 0 {
		plog.EngineLG.WithFields(e.JobFields()).Errorf("failed to search engine info: %v", r)
		return fmt.Errorf("failed to search engine info: %v", r)
	}

	e.ModuleInfo, e.TaskInfo, e.RuleInfo, _ = comm.GetMoudleOneConfig(e.EngineInfo.ModuleId, e.EngineInfo.TaskId, e.EngineInfo.RuleId)

	for _, v := range inputData.InstanceList {
		if v.Status == "SUCCESS" {
			successNum++
		}
	}

	if e.EngineInfo.Action == base.ACTION_UP {
		actualNum = e.EngineInfo.CurrentNum + successNum
	} else {
		actualNum = e.EngineInfo.CurrentNum - successNum
	}

	if e.EngineInfo.AdjustNum == successNum {
		e.EngineInfo.Status = base.ENGINE_TASK_STATUS_SUCCESS
		e.EngineInfo.ActionReason = NOTICE_SUCCESS
	} else {
		e.EngineInfo.Status = base.ENGINE_TASK_STATUS_PARTIAL_FAILED
		e.EngineInfo.ActionReason = NOTICE_ERROR_INSTANCE_COUNT_NOT_MATCH
	}

	if successNum == 0 {
		e.EngineInfo.Status = base.ENGINE_TASK_STATUS_FAILED
		e.EngineInfo.ActionReason = NOTICE_ERROR_ALL_INSTANCE_FAILED
	}

	callJson, _ := json.Marshal(&inputData)
	callbackData = string(callJson)
	e.EngineInfo.SuccessNum = successNum
	e.EngineInfo.ActualNum = actualNum
	e.EngineInfo.CallbackInfo = callbackData
	e.EngineInfo.UpdateTime = time.Now()
	e.EngineInfo.FinishTime = time.Now()
	e.Phase = comm.EngineFinishPhase

	// 更新执行结果
	_, r = dao.CreateEngineInfoPtr().UpdateByPk(&e.EngineInfo,
		[]string{"Status", "SuccessNum", "ActualNum", "CallbackInfo", "UpdateTime", "FinishTime"},
		e.EngineInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.JobFields()).Errorf("failed to update engine info: %v", r)
		return fmt.Errorf("failed to update engine info: %v", r)
	}

	e.Notify("")
	e.UpdateStatus(e.EngineInfo.Status, e.EngineInfo.ActionReason)
	return nil
}
