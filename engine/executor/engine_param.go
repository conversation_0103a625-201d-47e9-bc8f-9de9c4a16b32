package executor

// 发送通告原因
const (
	NOTICE_SUCCESS                         = ""
	NOTICE_ERROR_SAME_TAG_RUNNING          = "有相同机房的任务正在运行"
	NOTICE_ERROR_REQUEST_FAILED            = "请求扩缩容引擎失败"
	NOTICE_ERROR_SCALE_ENGINE_FAILED       = "扩缩容引擎返回失败"
	NOTICE_ERROR_REQUEST_DATABASE          = "请求数据库失败"
	NOTICE_ERROR_GET_DECISION_DATA         = "获取决策数据失败"
	NOTICE_ERROR_SERVICE_NAME_FORMAT       = "服务名格式不正确"
	NOTICE_ERROR_INSTANCE_COUNT_NOT_MATCH  = "执行实例数不符合预期"
	NOTICE_ERROR_ALL_INSTANCE_FAILED       = "执行实例全部失败"
	NOTICE_ERROR_SERVICE_CAPACITY_RESPONSE = "服务容量响应不需要执行"
)
