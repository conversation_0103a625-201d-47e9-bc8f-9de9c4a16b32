package executor

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/pkg/send_hi"
	"fmt"
)

var (
	EngineTaskStartNum = "【扩缩容任务】开始执行\n[服务名称]：%s\n[生效状态]：%s\n[执行动作]：%s\n[执行信息]：%d个实例\n[失败原因]:%s"
	EngineTaskStartIP  = "【扩缩容任务】开始执行\n[服务名称]：%s\n[生效状态]：%s\n[执行动作]：%s\n[执行信息]：%d个实例,IP：%s\n[失败原因]:%s"

	EngineTaskEndNum = "【扩缩容任务】结束执行\n[服务名称]：%s\n[生效状态]：%s\n[执行动作]：%s\n[执行信息]：%d个实例\n[失败原因]:%s"
	EngineTaskEndIP  = "【扩缩容任务】结束执行\n[服务名称]：%s\n[生效状态]：%s\n[执行动作]：%s\n[执行信息]：%d个实例,IP：%s\n[失败原因]:%s"
)

func (e *EngineRunData) SendStartSuccessMessageHi(failedReason string) {
	var msg string
	if e.AdjustmentType == base.ADJUSTMENT_TYPE_INSTANCE_IP {
		msg = fmt.Sprintf(EngineTaskStartIP, e.ServiceName, base.TaskStatusMap[e.TaskStatus],
			base.ActionMap[e.Action], e.AdjustNum, e.IpList, failedReason)
	} else {
		msg = fmt.Sprintf(EngineTaskStartNum, e.ServiceName, base.TaskStatusMap[e.TaskStatus], base.ActionMap[e.Action], e.AdjustNum, failedReason)
	}
	send_hi.SendEventSuccess(msg)
}

func (e *EngineRunData) SendEndSuccessMessageHi(failedReason string) {
	var msg string
	if e.AdjustmentType == base.ADJUSTMENT_TYPE_INSTANCE_IP {
		msg = fmt.Sprintf(EngineTaskEndIP, e.ServiceName, base.TaskStatusMap[e.TaskStatus],
			base.ActionMap[e.Action], e.AdjustNum, e.IpList, failedReason)
	} else {
		msg = fmt.Sprintf(EngineTaskEndNum, e.ServiceName, base.TaskStatusMap[e.TaskStatus], base.ActionMap[e.Action], e.AdjustNum, failedReason)
	}
	send_hi.SendEventSuccess(msg)
}

func (e *EngineRunData) SendStartFailedMessageHi(failedReason string) {
	var msg string
	if e.AdjustmentType == base.ADJUSTMENT_TYPE_INSTANCE_IP {
		msg = fmt.Sprintf(EngineTaskStartIP, e.ServiceName, base.TaskStatusMap[e.TaskStatus],
			base.ActionMap[e.Action], e.AdjustNum, e.IpList, failedReason)
	} else {
		msg = fmt.Sprintf(EngineTaskStartNum, e.ServiceName, base.TaskStatusMap[e.TaskStatus], base.ActionMap[e.Action], e.AdjustNum, failedReason)
	}
	send_hi.SendEventFailed(msg)
}

func (e *EngineRunData) SendEndFailedMessageHi(failedReason string) {
	var msg string
	if e.AdjustmentType == base.ADJUSTMENT_TYPE_INSTANCE_IP {
		msg = fmt.Sprintf(EngineTaskEndIP, e.ServiceName, base.TaskStatusMap[e.TaskStatus],
			base.ActionMap[e.Action], e.AdjustNum, e.IpList, failedReason)
	} else {
		msg = fmt.Sprintf(EngineTaskEndNum, e.ServiceName, base.TaskStatusMap[e.TaskStatus], base.ActionMap[e.Action], e.AdjustNum, failedReason)
	}
	send_hi.SendEventFailed(msg)
}
