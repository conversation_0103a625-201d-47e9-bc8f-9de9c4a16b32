package comm

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
)

// 获取模块、任务、规则信息
func GetOneModelConfig(moduleId int, taskType, IdcTag string) (moduleInfo dao.Module, taskInfo dao.TaskInfo, ruleInfo dao.RuleOnline, r ocommon.ResultInfo) {
	// 获取module信息
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, moduleId)
	if !r.IsOk() {
		olog.Error("failed to get module info from database, module_id:[%d], err:[%v]", moduleId, r)
		return
	}

	// 获取任务列表信息
	var taskInfoList []dao.TaskInfo
	taskInfoList, r = dao.CreateTaskInfoPtr().GetTaskInfoByModuleID(moduleId, taskType, IdcTag)
	if !r.<PERSON>() {
		olog.Error("failed to get task info from database, module_id:[%d], err:[%v]", moduleId, r)
		return
	}
	if len(taskInfoList) == 0 {
		return
	}
	taskInfo = taskInfoList[0]
	// 获取规则信息
	var taskInfoIds []int
	for _, v := range taskInfoList {
		taskInfoIds = append(taskInfoIds, v.ID)
	}

	var ruleList []dao.RuleOnline
	ruleList, r = dao.CreateRuleOnlinePtr().GetRuleByTaskInfoId(taskInfoIds)
	if !r.IsOk() {
		olog.Error("failed to get online rule from database, module_id:[%d], task_info_id:[%v], err:[%v]", moduleId, taskInfoIds, r)
		return
	}

	if len(ruleList) == 0 {
		return
	}
	ruleInfo = ruleList[0]
	return
}

func GetMoudleOneConfig(moduleId, taskId, ruleId int) (moduleInfo dao.Module, taskInfo dao.TaskInfo, ruleInfo dao.RuleOnline, r ocommon.ResultInfo) {
	// 获取module信息
	r = dao.CreateModulePtr().SearchByPk(&moduleInfo, moduleId)
	if !r.IsOk() {
		olog.Error("failed to get module info from database, module_id:[%d], err:[%v]", moduleId, r)
		return
	}

	// 获取task信息
	r = dao.CreateTaskInfoPtr().SearchByPk(&taskInfo, taskId)
	if !r.IsOk() {
		olog.Error("failed to get task info from database, task_id:[%d], err:[%v]", taskId, r)
		return
	}

	// 获取rule信息
	r = dao.CreateRuleOnlinePtr().SearchByPk(&ruleInfo, ruleId)
	if !r.IsOk() {
		olog.Error("failed to get rule info from database, rule_id:[%d], err:[%v]", ruleId, r)
		return
	}

	return
}

func GetAllCronTask() (taskList []dao.TaskInfo, r ocommon.ResultInfo) {
	// 获取定时任务列表
	var taskListT []dao.TaskInfo
	taskListT, r = dao.CreateTaskInfoPtr().GetTaskInfoByType(base.TASK_TYPE_CRONTAB)
	if !r.IsOk() {
		olog.Error("failed to get cron task from database, err:[%v]", r)
		return
	}

	for _, task := range taskListT {
		var taskD dao.TaskInfo
		dao.CreateModulePtr().SearchByPk(&taskD, task.ModuleId)
		if taskD.Dflag == 0 {
			taskList = append(taskList, task)
		}
	}

	return

}

// 计算扩容和缩容上下限
func ComputeMetricLimit(m base.MetricInfo) (upLimit, downLimt float64) {
	upLimit = m.TargetValue * (1 + m.TargetRateMax)
	downLimt = m.TargetValue * (1 - m.TargetRateMin)
	return
}
