package comm

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"reflect"
	"testing"
)

func TestGetCurrentMetricValue(t *testing.T) {
	tests := []struct {
		name       string
		metricName string
		monitor    dao.MonitorInfo
		want       *float64
	}{
		{
			name:       "CPU Percent",
			metricName: base.METRIC_NAME_CPU_PERCENT,
			monitor:    dao.MonitorInfo{CPUAvg: 75.5},
			want:       floatPtr(75.5),
		},
		{
			name:       "App CPU Percent",
			metricName: base.METRIC_NAME_APP_CPU_PERCENT,
			monitor:    dao.MonitorInfo{CPUServ: 80.2},
			want:       floatPtr(80.2),
		},
		{
			name:       "Average Cost",
			metricName: base.METRIC_NAME_AVERAGE_COST,
			monitor:    dao.MonitorInfo{CostAvg: 120.3},
			want:       floatPtr(120.3),
		},
		{
			name:       "Single Instance QPS",
			metricName: base.METRIC_NAME_SINGLE_INSTANCE_QPS,
			monitor:    dao.MonitorInfo{QPSAvg: 150.4},
			want:       floatPtr(150.4),
		},
		{
			name:       "Water Level",
			metricName: base.METRIC_NAME_WATER_LEVEL,
			monitor:    dao.MonitorInfo{Level: 60.1},
			want:       floatPtr(60.1),
		},
		{
			name:       "Unknown Metric",
			metricName: "unknown_metric",
			monitor:    dao.MonitorInfo{},
			want:       nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getCurrentMetricValue(tt.metricName, tt.monitor)
			if got == nil && tt.want == nil {
				return
			}
			if got == nil || tt.want == nil || *got != *tt.want {
				t.Errorf("getCurrentMetricValue(%q, %v) = %v, want %v", tt.metricName, tt.monitor, got, tt.want)
			}
		})
	}
}

func floatPtr(f float64) *float64 {
	return &f
}

func TestModuleOneTaskParam_Fields(t *testing.T) {
	moduleInfo := dao.Module{
		ID:   1,
		Name: "ModuleOne",
	}
	taskInfo := dao.TaskInfo{
		ID: 1,
	}
	ruleInfo := dao.RuleOnline{
		ID: 1,
	}
	traceID := "trace101"

	testCases := []struct {
		name     string
		param    *ModuleOneTaskParam
		expected map[string]interface{}
	}{
		{
			name: "Test with data",
			param: &ModuleOneTaskParam{
				ModuleInfo: moduleInfo,
				TaskInfo:   taskInfo,
				RuleInfo:   ruleInfo,
				TraceID:    traceID,
			},
			expected: map[string]interface{}{
				"module_id":   1,
				"module_name": "ModuleOne",
				"task_id":     1,
				"rule_id":     1,
				"trace_id":    "trace101",
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.param.Fields()
			if !reflect.DeepEqual(result, tc.expected) {
				t.Errorf("Fields() = %v, want %v", result, tc.expected)
			}
		})
	}
}

func TestGetRuleInfoForNotice(t *testing.T) {
	tests := []struct {
		name        string
		eventInfo   dao.EventInfo
		monitorInfo dao.MonitorInfo
		want        string
	}{
		{
			name: "Test with CPU Percent",
			eventInfo: dao.EventInfo{
				RuleInfo: `[{"MetricName":"cpuPercent","TargetValue":80}]`,
			},
			monitorInfo: dao.MonitorInfo{
				CPUAvg: 85.0,
			},
			want: "CPU使用率, 当前值85.00，目标值80.00，目标上限80.00，目标下限80.00",
		},
		{
			name: "Test with App CPU Percent",
			eventInfo: dao.EventInfo{
				RuleInfo: `[{"MetricName":"appCpuPercent","TargetValue":70}]`,
			},
			monitorInfo: dao.MonitorInfo{
				CPUServ: 75.0,
			},
			want: "服务CPU使用率, 当前值75.00，目标值70.00，目标上限70.00，目标下限70.00",
		},
		{
			name: "Test with No Metrics",
			eventInfo: dao.EventInfo{
				RuleInfo: `[]`,
			},
			monitorInfo: dao.MonitorInfo{},
			want:        "",
		},
		{
			name: "Test with Empty RuleInfo",
			eventInfo: dao.EventInfo{
				RuleInfo: "",
			},
			monitorInfo: dao.MonitorInfo{},
			want:        "",
		},
		{
			name: "Test with Metric Not Found",
			eventInfo: dao.EventInfo{
				RuleInfo: `[{"MetricName":"unknown_metric","TargetValue":50}]`,
			},
			monitorInfo: dao.MonitorInfo{},
			want:        "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getRuleInfoForNotice(tt.eventInfo, tt.monitorInfo)
			if got != tt.want {
				t.Errorf("GetRuleInfoForNotice() = %v, want %v", got, tt.want)
			}
		})
	}
}
