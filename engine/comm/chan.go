package comm

import "dxm/siod_sre/auto-scaler/dao"

var (
	ScalerCh *EngineChan
)

// 各个组件间通信管道
type EngineChan struct {
	// 监控事件处理管道
	EventMonitorChan chan dao.MonitorInfo
	// 定时事件处理管道
	EventCornChan chan dao.RuleOnline
	// 外部事件处理管道
	EventOtherChan chan dao.RuleOnline
	// 决策器管道
	DecisionChan chan *ModuleOneTaskParam
	// 执行器处理管道
	ExecutorChan chan *ModuleOneTaskParam
}

func NewEngineChan() *EngineChan {
	NewEngineChanWithSize(1000, 1000, 10)
	return ScalerCh
}

func NewEngineChanWithSize(eventChSize int, decisionChSize int, executorChanChSize int) {
	ScalerCh = &EngineChan{
		EventMonitorChan: make(chan dao.MonitorInfo, eventChSize),
		EventCornChan:    make(chan dao.RuleOnline, 100),
		EventOtherChan:   make(chan dao.RuleOnline, 100),
		DecisionChan:     make(chan *ModuleOneTaskParam, decisionChSize),
		ExecutorChan:     make(chan *ModuleOneTaskParam, executorChanChSize),
	}
}

func init() {
	NewEngineChan()
}
