package comm

const EmailTpl = `<!DOCTYPE html>
<html>
<head>
    <title>弹性伸缩执行通告</title>
</head>
<body>
    <p>
        {模块信息} {{.ModuleInfo.Name}}（<br>
        {机房信息} {{.TaskInfo.IdcTag}}<br>
        {执行阶段} {{.Phase | phase}}
        {执行信息} {{.DecisionInfo | action_info}}<br>
        {触发策略} {{.MonitorInfo | rule_info .EventInfo}}<br>
        {触发时间} {{.EventInfo.CreateTime | formatTime}}<br>
      	{任务模式} {{.TaskInfo.TaskStatus | transfer "task_mode"}}<br>
        {执行状态} {{.DecisionInfo.Status | status}}<br>
    </p>
</body>
</html>`

// {执行详情} <a href="{{.DetailUrl}}">查看故障详情</a><br>

const OtherTpl = `弹性伸缩执行通告
{模块信息} {{.ModuleInfo.Name}}
{机房信息} {{.TaskInfo.IdcTag}}
{执行阶段} {{.Phase | transfer "phase"}}
{任务模式} {{.TaskInfo.TaskStatus | transfer "task_mode"}}
{执行信息} {{. | action_info}}
{触发策略} {{. | rule_info}}
{触发时间} {{.EventInfo.CreateTime | formatTime}}
{执行状态} {{. | status}}`

// {执行详情} {{.DetailUrl}}{{if .TrackerUrl}}{分析详情} {{.TrackerUrl}}{{end}}
