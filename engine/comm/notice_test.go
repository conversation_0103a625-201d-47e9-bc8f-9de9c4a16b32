package comm

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/pkg/service_capacity"
	"encoding/json"
	"testing"
)

func TestNoticeActionInfo(t *testing.T) {
	tests := []struct {
		name     string
		param    ModuleOneTaskParam
		expected string
	}{
		{
			name: "DecisionMakerPhase_Scaling_Up",
			param: ModuleOneTaskParam{
				Phase: DecisionMakerPhase,
				DecisionInfo: dao.DecisionRecord{
					Action:          base.ACTION_UP,
					CurrentNum:      5,
					AdjustmentValue: 3,
				},
			},
			expected: "当前实例5个，需扩容3个，最终实例数为8个",
		},
		{
			name: "DecisionMakerPhase_Scaling_Down",
			param: ModuleOneTaskParam{
				Phase: DecisionMakerPhase,
				DecisionInfo: dao.DecisionRecord{
					Action:          base.ACTION_DOWN,
					CurrentNum:      8,
					AdjustmentValue: 3,
				},
			},
			expected: "当前实例8个，需缩容3个，最终实例数为5个",
		},
		{
			name: "EngineStartPhase",
			param: ModuleOneTaskParam{
				Phase: EngineStartPhase,
				DecisionInfo: dao.DecisionRecord{
					Action: base.ACTION_UP,
				},
				InstanceInfo: dao.InstanceInfo{
					TotalNum: 5,
				},
				EngineInfo: dao.EngineInfo{
					ResponseInfo: func() string {
						resp := service_capacity.ExecuteResponse{
							Data: service_capacity.ExecuteResponeData{
								ActualOperateNum: 3,
								TargetNum:        8,
							},
						}
						data, _ := json.Marshal(resp)
						return string(data)
					}(),
				},
			},
			expected: "当前实例5个，需扩容3个，预期最终实例数为8个",
		},
		{
			name: "EngineFinishPhase",
			param: ModuleOneTaskParam{
				Phase: EngineFinishPhase,
				EngineInfo: dao.EngineInfo{
					Action:     base.ACTION_UP,
					CurrentNum: 5,
					ResponseInfo: func() string {
						resp := service_capacity.ExecuteResponse{
							Data: service_capacity.ExecuteResponeData{
								TargetNum: 8,
							},
						}
						data, _ := json.Marshal(resp)
						return string(data)
					}(),
					CallbackInfo: func() string {
						callback := service_capacity.ScaleCallbackResponse{
							InstanceList: []service_capacity.InstanceInfo{
								{Status: "SUCCESS"},
								{Status: "SUCCESS"},
								{Status: "FAILED"},
							},
						}
						data, _ := json.Marshal(callback)
						return string(data)
					}(),
				},
			},
			expected: "期望实例8个，已扩容2个，最终实例数为7个",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := noticeActionInfo(tt.param)
			if result != tt.expected {
				t.Errorf("noticeActionInfo() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestNoticeTransfer(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		value    string
		expected string
	}{
		{
			name:     "Action_Up",
			key:      "action",
			value:    base.ACTION_UP,
			expected: "扩容",
		},
		{
			name:     "Action_Down",
			key:      "action",
			value:    base.ACTION_DOWN,
			expected: "缩容",
		},
		{
			name:     "TaskMode_Enable",
			key:      "task_mode",
			value:    base.TASK_STATUS_ENABLE,
			expected: "使用中",
		},
		{
			name:     "TaskMode_Disable",
			key:      "task_mode",
			value:    base.TASK_STATUS_DISABLE,
			expected: "已下线",
		},
		{
			name:     "TaskMode_TestRun",
			key:      "task_mode",
			value:    base.TASK_STATUS_TEST_RUN,
			expected: "试运行",
		},
		{
			name:     "Phase_DecisionMaker",
			key:      "phase",
			value:    DecisionMakerPhase,
			expected: "决策生成",
		},
		{
			name:     "Phase_EngineStart",
			key:      "phase",
			value:    EngineStartPhase,
			expected: "执行开始",
		},
		{
			name:     "Phase_EngineFinish",
			key:      "phase",
			value:    EngineFinishPhase,
			expected: "执行结束",
		},
		{
			name:     "Unknown_Key",
			key:      "unknown",
			value:    "any",
			expected: "",
		},
		{
			name:     "Unknown_Value",
			key:      "action",
			value:    "unknown",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := noticeTransfer(tt.key, tt.value)
			if result != tt.expected {
				t.Errorf("noticeTransfer(%q, %q) = %v, want %v", tt.key, tt.value, result, tt.expected)
			}
		})
	}
}

func TestNoticeRuleInfo(t *testing.T) {
	tests := []struct {
		name     string
		param    ModuleOneTaskParam
		expected string
	}{
		{
			name: "AutoTask_CPU_Percent",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_AUTO_TASK,
				},
				EventInfo: dao.EventInfo{
					RuleInfo: `[{"MetricName":"cpuPercent","TargetValue":80,"TargetRateMax":0.2,"TargetRateMin":0.3}]`,
				},
				MonitorInfo: dao.MonitorInfo{
					CPUAvg: 85.0,
				},
			},
			expected: "CPU使用率, 当前值85.00，目标值80.00，目标上限96.00，目标下限56.00",
		},
		{
			name: "ManualTask_Scaling_Up",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_MANUAL,
				},
				RuleInfo: dao.RuleOnline{
					ManualAction: base.ACTION_UP,
					CurrentNum:   5,
					AdjustNum:    3,
				},
			},
			expected: "手动任务，目标实例数8个，当前实例5个，需扩容3个",
		},
		{
			name: "ManualTask_Scaling_Down",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_MANUAL,
				},
				RuleInfo: dao.RuleOnline{
					ManualAction: base.ACTION_DOWN,
					CurrentNum:   8,
					AdjustNum:    3,
				},
			},
			expected: "手动任务，目标实例数5个，当前实例8个，需缩容3个",
		},
		{
			name: "CrontabTask",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_CRONTAB,
				},
			},
			expected: "",
		},
		{
			name: "AutoTask_Empty_RuleInfo",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_AUTO_TASK,
				},
				EventInfo: dao.EventInfo{
					RuleInfo: "",
				},
			},
			expected: "",
		},
		{
			name: "AutoTask_Invalid_RuleInfo",
			param: ModuleOneTaskParam{
				TaskInfo: dao.TaskInfo{
					TaskType: base.TASK_TYPE_AUTO_TASK,
				},
				EventInfo: dao.EventInfo{
					RuleInfo: "invalid json",
				},
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := noticeRuleInfo(tt.param)
			if got != tt.expected {
				t.Errorf("noticeRuleInfo() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestNoticeStatus(t *testing.T) {
	tests := []struct {
		name     string
		param    ModuleOneTaskParam
		expected string
	}{
		{
			name: "DecisionMakerPhase_Success",
			param: ModuleOneTaskParam{
				Phase: DecisionMakerPhase,
				DecisionInfo: dao.DecisionRecord{
					Status: base.ENGINE_TASK_STATUS_SUCCESS,
				},
			},
			expected: "任务运行成功",
		},
		{
			name: "DecisionMakerPhase_Failed",
			param: ModuleOneTaskParam{
				Phase: DecisionMakerPhase,
				DecisionInfo: dao.DecisionRecord{
					Status:       base.ENGINE_TASK_STATUS_FAILED,
					FailedReason: "资源不足",
				},
			},
			expected: "任务运行失败, 资源不足",
		},
		{
			name: "EngineStartPhase_Success",
			param: ModuleOneTaskParam{
				Phase: EngineStartPhase,
				EngineInfo: dao.EngineInfo{
					Status: base.ENGINE_TASK_STATUS_SUCCESS,
				},
			},
			expected: "任务运行成功",
		},
		{
			name: "EngineStartPhase_Failed",
			param: ModuleOneTaskParam{
				Phase: EngineStartPhase,
				EngineInfo: dao.EngineInfo{
					Status:       base.ENGINE_TASK_STATUS_FAILED,
					ActionReason: "执行超时",
				},
			},
			expected: "任务运行失败, 执行超时",
		},
		{
			name: "EngineFinishPhase_Success",
			param: ModuleOneTaskParam{
				Phase: EngineFinishPhase,
				EngineInfo: dao.EngineInfo{
					Status: base.ENGINE_TASK_STATUS_SUCCESS,
				},
			},
			expected: "任务运行成功",
		},
		{
			name: "EngineFinishPhase_Failed",
			param: ModuleOneTaskParam{
				Phase: EngineFinishPhase,
				EngineInfo: dao.EngineInfo{
					Status:       base.ENGINE_TASK_STATUS_FAILED,
					ActionReason: "部分实例失败",
				},
			},
			expected: "任务运行失败, 部分实例失败",
		},
		{
			name: "UnknownPhase",
			param: ModuleOneTaskParam{
				Phase: "UnknownPhase",
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := noticeStatus(tt.param)
			if got != tt.expected {
				t.Errorf("noticeStatus() = %v, want %v", got, tt.expected)
			}
		})
	}
}
