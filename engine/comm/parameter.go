package comm

import (
	"dxm/siod_sre/auto-scaler/dao"
)

type ModuleOneTaskParam struct {
	ModuleInfo   dao.Module
	TaskInfo     dao.TaskInfo
	RuleInfo     dao.RuleOnline
	MonitorInfo  dao.MonitorInfo
	EventInfo    dao.EventInfo
	ProcessInfo  dao.ProcessInfo
	InstanceInfo dao.InstanceInfo
	DecisionInfo dao.DecisionRecord
	EngineInfo   dao.EngineInfo
	Phase        string
	TraceID      string
}

var (
	DecisionMakerPhase = "DecisionMaker"
	EngineStartPhase   = "EngineStart"
	EngineFinishPhase  = "EngineFinish"
)

var ParseMap = map[string]string{
	DecisionMakerPhase: "决策生成",
	EngineStartPhase:   "执行开始",
	EngineFinishPhase:  "执行结束",
}
