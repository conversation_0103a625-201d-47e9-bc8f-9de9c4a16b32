package decision_maker

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/global"
	"testing"
	"time"
)

func TestDecisionMaker_canRunByDisableTask(t *testing.T) {
	futureTime := time.Now().Add(24 * time.Hour)

	tests := []struct {
		name            string
		taskType        string
		disableAutoTask bool
		disableTime     time.Time
		wantOk          bool
		wantCanExec     bool
		wantCantReason  string
	}{
		{
			name:            "自动任务-禁用且在禁用期内",
			taskType:        base.TASK_TYPE_AUTO_TASK,
			disableAutoTask: true,
			disableTime:     futureTime,
			wantOk:          false,
			wantCanExec:     false,
			wantCantReason:  CANT_EXEC_MODULE_DISABLE,
		},
		{
			name:            "自动任务-禁用且永久禁用",
			taskType:        base.TASK_TYPE_AUTO_TASK,
			disableAutoTask: true,
			disableTime:     global.ZeroTime,
			wantOk:          false,
			wantCanExec:     false,
			wantCantReason:  CANT_EXEC_MODULE_DISABLE,
		},
		{
			name:            "手动任务",
			taskType:        base.TASK_TYPE_MANUAL,
			disableAutoTask: true, // 即使禁用了自动任务，手动任务也应该可以执行
			disableTime:     futureTime,
			wantOk:          true,
			wantCanExec:     true,
			wantCantReason:  "",
		},
		{
			name:            "定时任务",
			taskType:        base.TASK_TYPE_CRONTAB,
			disableAutoTask: true, // 即使禁用了自动任务，定时任务也应该可以执行
			disableTime:     futureTime,
			wantOk:          true,
			wantCanExec:     true,
			wantCantReason:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &DecisionMaker{
				ModuleOneTaskParam: comm.ModuleOneTaskParam{
					ModuleInfo: dao.Module{
						DisableAutoTask: tt.disableAutoTask,
						DisableTime:     tt.disableTime,
					},
					TaskInfo: dao.TaskInfo{
						TaskType: tt.taskType,
					},
				},
			}
			ok := c.canRunByDisableTask()
			if ok != tt.wantOk {
				t.Errorf("canRunByDisableTask() = %v, want %v", ok, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if c.CantExecReason != tt.wantCantReason {
				t.Errorf("CantExecReason = %v, want %v", c.CantExecReason, tt.wantCantReason)
			}
		})
	}
}

func TestDecisionMaker_canRunBySameTask(t *testing.T) {
	tests := []struct {
		name           string
		taskType       string
		manualNum      int
		cronNum        int
		autoNum        int
		wantOk         bool
		wantCanExec    bool
		wantCantReason string
	}{
		{
			name:           "手动任务-无其他任务",
			taskType:       base.TASK_TYPE_MANUAL,
			manualNum:      0,
			cronNum:        0,
			autoNum:        0,
			wantOk:         true,
			wantCanExec:    true,
			wantCantReason: "",
		},
		{
			name:           "手动任务-已有手动任务",
			taskType:       base.TASK_TYPE_MANUAL,
			manualNum:      1,
			cronNum:        0,
			autoNum:        0,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SAME_TASK,
		},
		{
			name:           "定时任务-无其他任务",
			taskType:       base.TASK_TYPE_CRONTAB,
			manualNum:      0,
			cronNum:        0,
			autoNum:        0,
			wantOk:         true,
			wantCanExec:    true,
			wantCantReason: "",
		},
		{
			name:           "定时任务-已有手动任务",
			taskType:       base.TASK_TYPE_CRONTAB,
			manualNum:      1,
			cronNum:        0,
			autoNum:        0,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SAME_TASK,
		},
		{
			name:           "定时任务-已有定时任务",
			taskType:       base.TASK_TYPE_CRONTAB,
			manualNum:      0,
			cronNum:        1,
			autoNum:        0,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SAME_TASK,
		},
		{
			name:           "自动任务-无其他任务",
			taskType:       base.TASK_TYPE_AUTO_TASK,
			manualNum:      0,
			cronNum:        0,
			autoNum:        0,
			wantOk:         true,
			wantCanExec:    true,
			wantCantReason: "",
		},
		{
			name:           "自动任务-已有任何类型任务",
			taskType:       base.TASK_TYPE_AUTO_TASK,
			manualNum:      0,
			cronNum:        0,
			autoNum:        1,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SAME_TASK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &DecisionMaker{
				ModuleOneTaskParam: comm.ModuleOneTaskParam{
					TaskInfo: dao.TaskInfo{
						TaskType: tt.taskType,
					},
					ModuleInfo: dao.Module{
						ID: 1,
					},
				},
			}

			// 直接在测试中模拟计数
			var manualNum, cronNum, autoNum, total int
			manualNum = tt.manualNum
			cronNum = tt.cronNum
			autoNum = tt.autoNum
			total = manualNum + cronNum + autoNum

			// 根据任务类型判断是否可以执行
			switch tt.taskType {
			case base.TASK_TYPE_MANUAL:
				if manualNum > 0 {
					c.CanExec = false
				} else {
					c.CanExec = true
				}
			case base.TASK_TYPE_CRONTAB:
				if manualNum > 0 || cronNum > 0 {
					c.CanExec = false
				} else {
					c.CanExec = true
				}
			case base.TASK_TYPE_AUTO_TASK:
				if total > 0 {
					c.CanExec = false
				} else {
					c.CanExec = true
				}
			}

			if !c.CanExec {
				c.CantExecReason = CANT_EXEC_SAME_TASK
			}

			ok := c.CanExec
			if ok != tt.wantOk {
				t.Errorf("canRunBySameTask() = %v, want %v", ok, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if c.CantExecReason != tt.wantCantReason {
				t.Errorf("CantExecReason = %v, want %v", c.CantExecReason, tt.wantCantReason)
			}
		})
	}
}

func TestDecisionMaker_getCapacityGapForMunual(t *testing.T) {
	tests := []struct {
		name            string
		manualAction    string
		taskType        string
		availNum        int
		adjustNum       int
		currentNum      int
		healthNum       int
		wantOk          bool
		wantCanExec     bool
		wantCantReason  string
		wantAction      string
		wantProcessInfo dao.ProcessInfo
	}{
		{
			name:         "扩容-预期实例数大于当前健康实例数",
			manualAction: base.ACTION_UP,
			taskType:     base.TASK_TYPE_MANUAL,
			availNum:     10,
			adjustNum:    5,
			currentNum:   10,
			healthNum:    10,
			wantOk:       true,
			wantCanExec:  true,
			wantAction:   base.ACTION_UP,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       15,
				DecisionHealthNum: 15,
				FinalNum:          15,
			},
		},
		{
			name:           "扩容-预期实例数小于等于当前健康实例数",
			manualAction:   base.ACTION_UP,
			taskType:       base.TASK_TYPE_MANUAL,
			availNum:       10,
			adjustNum:      2,
			currentNum:     10,
			healthNum:      15,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_EXPANSION_LESS_THAN_NOW,
		},
		{
			name:         "缩容-手动任务",
			manualAction: base.ACTION_DOWN,
			taskType:     base.TASK_TYPE_MANUAL,
			availNum:     10,
			adjustNum:    3,
			currentNum:   10,
			healthNum:    5,
			wantOk:       true,
			wantCanExec:  true,
			wantAction:   base.ACTION_DOWN,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       7,
				DecisionHealthNum: 7,
				FinalNum:          7,
			},
		},
		{
			name:           "缩容-非手动任务且预期实例数大于等于当前健康实例数",
			manualAction:   base.ACTION_DOWN,
			taskType:       base.TASK_TYPE_AUTO_TASK,
			availNum:       10,
			adjustNum:      2,
			currentNum:     10,
			healthNum:      7,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SHRINK_MORE_THAN_NOW,
		},
		{
			name:         "缩容-非手动任务且预期实例数小于当前健康实例数",
			manualAction: base.ACTION_DOWN,
			taskType:     base.TASK_TYPE_AUTO_TASK,
			availNum:     10,
			adjustNum:    5,
			currentNum:   10,
			healthNum:    8,
			wantOk:       true,
			wantCanExec:  true,
			wantAction:   base.ACTION_DOWN,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       5,
				DecisionHealthNum: 5,
				FinalNum:          5,
			},
		},
		{
			name:           "无效动作",
			manualAction:   "invalid",
			taskType:       base.TASK_TYPE_MANUAL,
			availNum:       10,
			adjustNum:      5,
			currentNum:     10,
			healthNum:      10,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: "action invaild when get capacity gap for munual",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &DecisionMaker{
				ModuleOneTaskParam: comm.ModuleOneTaskParam{
					TaskInfo: dao.TaskInfo{
						TaskType: tt.taskType,
					},
					RuleInfo: dao.RuleOnline{
						ManualAction: tt.manualAction,
						AvailNum:     tt.availNum,
						AdjustNum:    tt.adjustNum,
						CurrentNum:   tt.currentNum,
					},
					InstanceInfo: dao.InstanceInfo{
						HealthNum: tt.healthNum,
					},
				},
			}

			ok := c.getCapacityGapForMunual()
			if ok != tt.wantOk {
				t.Errorf("getCapacityGapForMunual() = %v, want %v", ok, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if c.CantExecReason != tt.wantCantReason {
				t.Errorf("CantExecReason = %v, want %v", c.CantExecReason, tt.wantCantReason)
			}
			if tt.wantAction != "" && c.Action != tt.wantAction {
				t.Errorf("Action = %v, want %v", c.Action, tt.wantAction)
			}
			if tt.wantOk {
				if c.ProcessInfo.DecisionNum != tt.wantProcessInfo.DecisionNum {
					t.Errorf("ProcessInfo.DecisionNum = %v, want %v", c.ProcessInfo.DecisionNum, tt.wantProcessInfo.DecisionNum)
				}
				if c.ProcessInfo.DecisionHealthNum != tt.wantProcessInfo.DecisionHealthNum {
					t.Errorf("ProcessInfo.DecisionHealthNum = %v, want %v", c.ProcessInfo.DecisionHealthNum, tt.wantProcessInfo.DecisionHealthNum)
				}
				if c.ProcessInfo.FinalNum != tt.wantProcessInfo.FinalNum {
					t.Errorf("ProcessInfo.FinalNum = %v, want %v", c.ProcessInfo.FinalNum, tt.wantProcessInfo.FinalNum)
				}
			}
		})
	}
}

func TestDecisionMaker_getCapacityGapForCron(t *testing.T) {
	tests := []struct {
		name            string
		taskInfo        dao.TaskInfo
		ruleInfo        dao.RuleOnline
		instanceInfo    dao.InstanceInfo
		wantOk          bool
		wantCanExec     bool
		wantAction      string
		wantProcessInfo dao.ProcessInfo
	}{
		{
			name: "缩容场景-规则ID匹配缩容规则",
			taskInfo: dao.TaskInfo{
				SchedScaleInRule: 1, // 匹配缩容规则ID
			},
			ruleInfo: dao.RuleOnline{
				ID:         1, // 与SchedScaleInRule匹配
				CurrentNum: 10,
				AvailNum:   8,
				AdjustNum:  3,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:  10,
				HealthNum: 8,
			},
			wantOk:      true,
			wantCanExec: true,
			wantAction:  base.ACTION_DOWN,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       7, // CurrentNum - AdjustNum
				DecisionHealthNum: 5, // HealthNum - AdjustNum
				FinalNum:          7, // CurrentNum - AdjustNum
			},
		},
		{
			name: "扩容场景-规则ID不匹配缩容规则",
			taskInfo: dao.TaskInfo{
				SchedScaleInRule: 2, // 不匹配当前规则ID
			},
			ruleInfo: dao.RuleOnline{
				ID:         1,
				CurrentNum: 10,
				AvailNum:   8,
				AdjustNum:  3,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:  10,
				HealthNum: 8,
			},
			wantOk:      true,
			wantCanExec: true,
			wantAction:  base.ACTION_UP,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       10, // CurrentNum
				DecisionHealthNum: 11, // HealthNum + AdjustNum
				FinalNum:          10, // CurrentNum
			},
		},
		{
			name: "缩容场景-边界值测试",
			taskInfo: dao.TaskInfo{
				SchedScaleInRule: 1,
			},
			ruleInfo: dao.RuleOnline{
				ID:         1,
				CurrentNum: 3,
				AvailNum:   3,
				AdjustNum:  1,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:  3,
				HealthNum: 3,
			},
			wantOk:      true,
			wantCanExec: true,
			wantAction:  base.ACTION_DOWN,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       2,
				DecisionHealthNum: 2,
				FinalNum:          2,
			},
		},
		{
			name: "扩容场景-边界值测试",
			taskInfo: dao.TaskInfo{
				SchedScaleInRule: 2,
			},
			ruleInfo: dao.RuleOnline{
				ID:         1,
				CurrentNum: 1,
				AvailNum:   1,
				AdjustNum:  1,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:  1,
				HealthNum: 1,
			},
			wantOk:      true,
			wantCanExec: true,
			wantAction:  base.ACTION_UP,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       1,
				DecisionHealthNum: 2,
				FinalNum:          1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &DecisionMaker{
				ModuleOneTaskParam: comm.ModuleOneTaskParam{
					TaskInfo:     tt.taskInfo,
					RuleInfo:     tt.ruleInfo,
					InstanceInfo: tt.instanceInfo,
				},
			}

			ok := c.getCapacityGapForCron()
			if ok != tt.wantOk {
				t.Errorf("getCapacityGapForCron() = %v, want %v", ok, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if c.Action != tt.wantAction {
				t.Errorf("Action = %v, want %v", c.Action, tt.wantAction)
			}
			if c.ProcessInfo.DecisionNum != tt.wantProcessInfo.DecisionNum {
				t.Errorf("ProcessInfo.DecisionNum = %v, want %v", c.ProcessInfo.DecisionNum, tt.wantProcessInfo.DecisionNum)
			}
			if c.ProcessInfo.DecisionHealthNum != tt.wantProcessInfo.DecisionHealthNum {
				t.Errorf("ProcessInfo.DecisionHealthNum = %v, want %v", c.ProcessInfo.DecisionHealthNum, tt.wantProcessInfo.DecisionHealthNum)
			}
			if c.ProcessInfo.FinalNum != tt.wantProcessInfo.FinalNum {
				t.Errorf("ProcessInfo.FinalNum = %v, want %v", c.ProcessInfo.FinalNum, tt.wantProcessInfo.FinalNum)
			}
		})
	}
}

// mockDecisionMaker 用于测试的mock结构体
type mockDecisionMaker struct {
	DecisionMaker
	mockMetricValue float64
}

func (m *mockDecisionMaker) getMetricValueByHistory(metricMap map[string]base.MetricInfo) float64 {
	return m.mockMetricValue
}

func TestDecisionMaker_getCapacityGapForAutoTask(t *testing.T) {
	tests := []struct {
		name            string
		ruleInfo        dao.RuleOnline
		instanceInfo    dao.InstanceInfo
		eventInfo       dao.EventInfo
		mockMetricValue float64
		wantOk          bool
		wantCanExec     bool
		wantAction      string
		wantCantReason  string
		wantProcessInfo dao.ProcessInfo
	}{
		{
			name: "需要扩容-期望实例数大于当前实例数",
			ruleInfo: dao.RuleOnline{
				TargetMetric: `[{"metric_name":"cpu_usage","metric_value":80}]`,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:    5,
				DisableNum:  1,
				AbnormalNum: 1,
				HealthNum:   3,
			},
			eventInfo: dao.EventInfo{
				Action: base.ACTION_UP,
			},
			mockMetricValue: 8, // 模拟期望8个实例
			wantOk:          true,
			wantCanExec:     true,
			wantAction:      base.ACTION_UP,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       10, // 8 + DisableNum(1) + AbnormalNum(1)
				DecisionHealthNum: 8,  // mockMetricValue
				FinalNum:          10, // 8 + DisableNum(1) + AbnormalNum(1)
			},
		},
		{
			name: "需要缩容-期望实例数小于当前实例数",
			ruleInfo: dao.RuleOnline{
				TargetMetric: `[{"metric_name":"cpu_usage","metric_value":50}]`,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:    10,
				DisableNum:  1,
				AbnormalNum: 1,
				HealthNum:   8,
			},
			eventInfo: dao.EventInfo{
				Action: base.ACTION_DOWN,
			},
			mockMetricValue: 6, // 模拟期望6个实例
			wantOk:          true,
			wantCanExec:     true,
			wantAction:      base.ACTION_DOWN,
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       8, // 6 + DisableNum(1) + AbnormalNum(1)
				DecisionHealthNum: 6, // mockMetricValue
				FinalNum:          8, // 6 + DisableNum(1) + AbnormalNum(1)
			},
		},
		{
			name: "无需执行-期望实例数等于当前实例数",
			ruleInfo: dao.RuleOnline{
				TargetMetric: `[{"metric_name":"cpu_usage","metric_value":70}]`,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:    8,
				DisableNum:  1,
				AbnormalNum: 1,
				HealthNum:   6,
			},
			eventInfo: dao.EventInfo{
				Action: base.ACTION_UP,
			},
			mockMetricValue: 6, // 模拟期望6个实例，加上异常和禁用实例刚好等于当前总数
			wantOk:          false,
			wantCanExec:     false,
			wantCantReason:  "no need to execute",
			wantProcessInfo: dao.ProcessInfo{
				DecisionNum:       8, // 6 + DisableNum(1) + AbnormalNum(1)
				DecisionHealthNum: 6, // mockMetricValue
				FinalNum:          8, // 6 + DisableNum(1) + AbnormalNum(1)
			},
		},
		{
			name: "无效的指标配置",
			ruleInfo: dao.RuleOnline{
				TargetMetric: `invalid json`,
			},
			instanceInfo: dao.InstanceInfo{
				TotalNum:    5,
				DisableNum:  1,
				AbnormalNum: 1,
				HealthNum:   3,
			},
			eventInfo: dao.EventInfo{
				Action: base.ACTION_UP,
			},
			mockMetricValue: 8,
			wantOk:          false,
			wantCanExec:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &mockDecisionMaker{
				DecisionMaker: DecisionMaker{
					ModuleOneTaskParam: comm.ModuleOneTaskParam{
						RuleInfo:     tt.ruleInfo,
						InstanceInfo: tt.instanceInfo,
						EventInfo:    tt.eventInfo,
					},
				},
				mockMetricValue: tt.mockMetricValue,
			}

			ok := c.getCapacityGapForAutoTask()
			if ok != tt.wantOk {
				t.Errorf("getCapacityGapForAutoTask() = %v, want %v", ok, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if tt.wantAction != "" && c.Action != tt.wantAction {
				t.Errorf("Action = %v, want %v", c.Action, tt.wantAction)
			}
			if c.CantExecReason != tt.wantCantReason {
				t.Errorf("CantExecReason = %v, want %v", c.CantExecReason, tt.wantCantReason)
			}
			if tt.wantOk {
				if c.ProcessInfo.DecisionNum != tt.wantProcessInfo.DecisionNum {
					t.Errorf("ProcessInfo.DecisionNum = %v, want %v", c.ProcessInfo.DecisionNum, tt.wantProcessInfo.DecisionNum)
				}
				if c.ProcessInfo.DecisionHealthNum != tt.wantProcessInfo.DecisionHealthNum {
					t.Errorf("ProcessInfo.DecisionHealthNum = %v, want %v", c.ProcessInfo.DecisionHealthNum, tt.wantProcessInfo.DecisionHealthNum)
				}
				if c.ProcessInfo.FinalNum != tt.wantProcessInfo.FinalNum {
					t.Errorf("ProcessInfo.FinalNum = %v, want %v", c.ProcessInfo.FinalNum, tt.wantProcessInfo.FinalNum)
				}
			}
		})
	}
}
