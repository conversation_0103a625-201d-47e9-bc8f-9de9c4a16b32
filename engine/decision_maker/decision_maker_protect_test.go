package decision_maker

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"testing"
)

func TestDecisionMaker_canRunByInstanceNum(t *testing.T) {
	tests := []struct {
		name           string
		action         string
		finalNum       int
		totalNum       int
		wantOk         bool
		wantCanExec    bool
		wantCantReason string
	}{
		{
			name:           "空Action",
			action:         "",
			finalNum:       10,
			totalNum:       10,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_ACTION_EMPTY,
		},
		{
			name:           "扩容-实例数少于当前",
			action:         base.ACTION_UP,
			finalNum:       5,
			totalNum:       10,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_EXPANSION_LESS_THAN_NOW,
		},
		{
			name:           "扩容-实例数多于当前",
			action:         base.ACTION_UP,
			finalNum:       15,
			totalNum:       10,
			wantOk:         true,
			wantCanExec:    true,
			wantCantReason: "",
		},
		{
			name:           "缩容-实例数多于当前",
			action:         base.ACTION_DOWN,
			finalNum:       15,
			totalNum:       10,
			wantOk:         false,
			wantCanExec:    false,
			wantCantReason: CANT_EXEC_SHRINK_MORE_THAN_NOW,
		},
		{
			name:           "缩容-实例数少于当前",
			action:         base.ACTION_DOWN,
			finalNum:       5,
			totalNum:       10,
			wantOk:         true,
			wantCanExec:    true,
			wantCantReason: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &DecisionMaker{
				ModuleOneTaskParam: comm.ModuleOneTaskParam{
					EventInfo: dao.EventInfo{
						Action: tt.action,
					},
					ProcessInfo: dao.ProcessInfo{
						FinalNum: tt.finalNum,
					},
					InstanceInfo: dao.InstanceInfo{
						TotalNum: tt.totalNum,
					},
				},
			}

			gotOk := c.canRunByInstanceNum()
			if gotOk != tt.wantOk {
				t.Errorf("canRunByInstanceNum() = %v, want %v", gotOk, tt.wantOk)
			}
			if c.CanExec != tt.wantCanExec {
				t.Errorf("CanExec = %v, want %v", c.CanExec, tt.wantCanExec)
			}
			if c.CantExecReason != tt.wantCantReason {
				t.Errorf("CantExecReason = %v, want %v", c.CantExecReason, tt.wantCantReason)
			}
		})
	}
}
