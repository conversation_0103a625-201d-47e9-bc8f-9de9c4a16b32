package decision_maker

const (
	// 不可执行原因
	CANT_EXEC_MODULE_DISABLE             = "module disable auto task"
	CANT_EXEC_SAME_TASK                  = "the same idc has already run tasks"
	CANT_EXEC_AUTO_TASK_DISABLE          = "disable auto scaling task"
	CANT_EXEC_SHAKE_CYCLE_INVAILD        = "shake cycle invalid for auto scaling task" // 自动任务的抖动周期无效
	CANT_EXEC_SHRINK_MORE_THAN_NOW       = "shrink instances more than now"            // 执行缩容但是执行后比当前实例还多
	CANT_EXEC_EXPANSION_LESS_THAN_NOW    = "expansion instnces less than current"      // 执行扩容但是执行后比当前实例还少
	CANT_EXEC_CRON_NOT_LAST_ACTION       = "last cron task action was not executed"    // 定时任务上一次动作未执行
	CANT_EXEC_ACTION_EMPTY               = "exec action is empty"
	CAT_EXEC_GET_CURRENT_INSTANCE_FAILED = "get current instance failed"
	CANT_EXEC_DB_ERROR                   = "db error"
)

var CantExecReasonMap = map[string]string{
	CANT_EXEC_MODULE_DISABLE:             "该服务禁止自动伸缩任务",
	CANT_EXEC_SAME_TASK:                  "该服务同机房已经存在伸缩任务",
	CANT_EXEC_AUTO_TASK_DISABLE:          "该服务禁止自动缩容任务",
	CANT_EXEC_SHAKE_CYCLE_INVAILD:        "未达到该服务自动伸缩抖动周期",
	CANT_EXEC_SHRINK_MORE_THAN_NOW:       "缩容期望实例数大于当前可用实例数",
	CANT_EXEC_EXPANSION_LESS_THAN_NOW:    "扩容期望实例数小于当前可用实例数",
	CANT_EXEC_CRON_NOT_LAST_ACTION:       "定时扩容任务上次不为缩容任务",
	CANT_EXEC_ACTION_EMPTY:               "执行动作为空",
	CAT_EXEC_GET_CURRENT_INSTANCE_FAILED: "获取当前实例数失败",
	CANT_EXEC_DB_ERROR:                   "数据库错误导致无法执行任务",
}
