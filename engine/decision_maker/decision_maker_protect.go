package decision_maker

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/engine/plog"
)

// 保护逻辑
func (c *DecisionMaker) ProtectLogic() (ok bool) {
	// 最大值最小值保护
	if !c.canRunByInstanceNum() {
		return
	}
	return true
}

// 扩容执行动作
func (c *DecisionMaker) canRunByInstanceNum() (ok bool) {
	if c.EventInfo.Action == "" {
		c.CantExecReason = CANT_EXEC_ACTION_EMPTY
		c.CanExec = false
		plog.EngineLG.WithFields(c.Fields()).Debugf("can run by instance num failed, action:%s", c.EventInfo.Action)
		return
	}
	// 执行扩容但是实例数比目前还少，则判定失败
	if c.EventInfo.Action == base.ACTION_UP {
		if c.ProcessInfo.FinalNum < c.InstanceInfo.TotalNum {
			c.CantExecReason = CANT_EXEC_EXPANSION_LESS_THAN_NOW
			c.CanExec = false
			plog.EngineLG.WithFields(c.Fields()).Debugf("can run by instance num failed, action:%s", c.EventInfo.Action)
			return
		}
	}

	// 执行缩容但是实例数比目前还多，则判定失败
	if c.EventInfo.Action == base.ACTION_DOWN {
		if c.ProcessInfo.FinalNum > c.InstanceInfo.TotalNum {
			c.CantExecReason = CANT_EXEC_SHRINK_MORE_THAN_NOW
			c.CanExec = false
			plog.EngineLG.WithFields(c.Fields()).Debugf("can run by instance num failed, action:%s", c.EventInfo.Action)
			return
		}
	}

	plog.EngineLG.WithFields(c.Fields()).Debugf("can run by instance num success, action:%s", c.EventInfo.Action)
	c.CanExec = true
	return true
}
