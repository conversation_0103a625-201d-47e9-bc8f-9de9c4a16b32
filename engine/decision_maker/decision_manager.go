package decision_maker

import (
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
)

type IDecisionMaker interface {
	EvaluateStatus() bool          // 状态评估
	MakeScalingDecision() bool     // 决策扩缩容
	PredictDemand() bool           // 需求预测
	AnalyzeCapacityGap() bool      // 容量缺口分析
	OptimizeAllocation() bool      // 资源分配优化函数
	AdjustDecisionPolicy() bool    // 策略调整函数
	GenerateRollbackPlan() bool    // 回滚计划生成函数
	HandleOverloadEmergency() bool // 紧急过载处理函数
	ProtectLogic() bool            // 保护逻辑

	Init() bool
	RunOne()
	GenName() string
}

var DecisionManager *DecisionMakerManager

type DecisionMakerManager struct {
	DecisionInfo map[string]IDecisionMaker
}

func NewDecisionManager() {
	DecisionManager = &DecisionMakerManager{
		DecisionInfo: make(map[string]IDecisionMaker, 10),
	}
}

func init() {
	NewDecisionManager()
}

func (t *DecisionMakerManager) Run() {
	// 运行所有组件
	plog.EngineLG.Info("decision maker manager run")
	for dc := range comm.ScalerCh.DecisionChan {
		go func(dc *comm.ModuleOneTaskParam) {
			e := NewDecisionMaker(*dc)
			e.Init()
			(*t).DecisionInfo[e.GenName()] = e
			e.RunOne()
		}(dc)
	}
}

func NewDecisionMaker(p comm.ModuleOneTaskParam) (e IDecisionMaker) {
	return &DecisionMaker{
		ModuleOneTaskParam: p,
	}
}
