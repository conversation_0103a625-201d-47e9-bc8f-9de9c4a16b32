package decision_maker

import (
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"sync"
	"time"
)

type IDecisionMaker interface {
	EvaluateStatus() bool          // 状态评估
	MakeScalingDecision() bool     // 决策扩缩容
	PredictDemand() bool           // 需求预测
	AnalyzeCapacityGap() bool      // 容量缺口分析
	OptimizeAllocation() bool      // 资源分配优化函数
	AdjustDecisionPolicy() bool    // 策略调整函数
	GenerateRollbackPlan() bool    // 回滚计划生成函数
	HandleOverloadEmergency() bool // 紧急过载处理函数
	ProtectLogic() bool            // 保护逻辑

	Init() bool
	RunOne()
	GenName() string
}

var DecisionManager *DecisionMakerManager

type DecisionMakerManager struct {
	DecisionInfo sync.Map // 使用sync.Map替代普通map，支持并发安全
	// 添加清理机制相关字段
	lastCleanup time.Time
	cleanupMu   sync.Mutex
}

func NewDecisionManager() {
	DecisionManager = &DecisionMakerManager{
		lastCleanup: time.Now(),
	}
	// 启动定期清理goroutine
	go DecisionManager.startCleanupRoutine()
}

func init() {
	NewDecisionManager()
}

func (t *DecisionMakerManager) Run() {
	// 运行所有组件
	plog.EngineLG.Info("decision maker manager run")
	for dc := range comm.ScalerCh.DecisionChan {
		go func(dc *comm.ModuleOneTaskParam) {
			e := NewDecisionMaker(*dc)
			if e == nil {
				plog.EngineLG.Error("failed to create decision maker")
				return
			}
			if !e.Init() {
				plog.EngineLG.Error("failed to init decision maker")
				return
			}
			name := e.GenName()
			t.DecisionInfo.Store(name, e)
			e.RunOne()
			// 执行完成后清理
			t.DecisionInfo.Delete(name)
		}(dc)
	}
}

func NewDecisionMaker(p comm.ModuleOneTaskParam) (e IDecisionMaker) {
	return &DecisionMaker{
		ModuleOneTaskParam: p,
	}
}

// startCleanupRoutine 启动定期清理routine
func (t *DecisionMakerManager) startCleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		t.cleanup()
	}
}

// cleanup 清理过期的决策器
func (t *DecisionMakerManager) cleanup() {
	t.cleanupMu.Lock()
	defer t.cleanupMu.Unlock()

	now := time.Now()
	if now.Sub(t.lastCleanup) < time.Minute {
		return // 避免频繁清理
	}

	count := 0
	t.DecisionInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	plog.EngineLG.Debug("DecisionMakerManager cleanup: current active decision makers: %d", count)
	t.lastCleanup = now
}

// GetActiveCount 获取当前活跃的决策器数量
func (t *DecisionMakerManager) GetActiveCount() int {
	count := 0
	t.DecisionInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}
