package plog

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	"dxm/siod-cloud/go-common-lib/olog"

	nested "github.com/antonfisher/nested-logrus-formatter"
	"github.com/astaxie/beego"
	rotatelogs "github.com/lestrrat/go-file-rotatelogs"
	"github.com/sirupsen/logrus"
)

var (
	EngineLG *logrus.Logger
)

func NewLog(name string, level string, file string, maxAge int) *logrus.Logger {

	logDir := filepath.Dir(file)
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		err = os.MkdirAll(logDir, os.ModePerm)
		if err != nil {
			panic(fmt.Sprintf("创建日志目录失败: %v", err))
		}
	}

	if maxAge == 0 {
		maxAge = 24 * 7
	}

	wr, err := rotatelogs.New(
		fmt.Sprintf("%s%s", file, ".%Y%m%d%H"),
		rotatelogs.WithMaxAge(time.Duration(maxAge)*time.Hour),
		rotatelogs.WithRotationTime(time.Duration(1)*time.Hour),
		rotatelogs.WithClock(rotatelogs.Local))
	if err != nil {
		panic(fmt.Sprintf("发生错误NewLog文件相关 %s", err))
	}

	lg := logrus.New()
	lg.SetOutput(wr)
	lgLvl, err := logrus.ParseLevel(level)
	if err != nil {
		panic(fmt.Sprintf("set file %s level %s failed: %v", file, level, err))
	}
	lg.SetLevel(lgLvl)
	lg.SetReportCaller(true)
	customCallerFormatter := func(frame *runtime.Frame) string {
		baseName := filepath.Base(frame.File)
		shortFuncs := strings.Split(frame.Function, ".")
		shortFuncName := frame.Function
		if len(shortFuncs) >= 1 {
			shortFuncName = shortFuncs[len(shortFuncs)-1]
		}
		return fmt.Sprintf(" [file:%s] [line:%d] [func:%s]",
			baseName,
			frame.Line,
			shortFuncName)
	}
	lg.SetFormatter(&nested.Formatter{
		HideKeys:              false,
		FieldsOrder:           []string{"module"},
		TrimMessages:          false,
		TimestampFormat:       "2006-01-02 15:04:05",
		NoColors:              true,
		CallerFirst:           true,
		CustomCallerFormatter: customCallerFormatter,
	})
	return lg
}

func init() {
	EngineLG = NewEngineLog()
	//EngineLG = NewScrapyJobLog(config.PConfig.Scrapy.Log)
}

func NewEngineLog() *logrus.Logger {

	level := beego.AppConfig.String("log_print_level")
	lvl := olog.GetLevelByString(level)
	olog.SetLevel(int(lvl))
	if level == "" {
		level = "info"
	}

	filename := beego.AppConfig.String("log_filename")
	if filename == "" {
		filename = "logs/auto-scaler.log"
	}
	var maxBackupInt int
	var err error
	maxBakeup := beego.AppConfig.String("log_maxbakeup")
	if maxBakeup == "" {
		maxBackupInt, err = strconv.Atoi(maxBakeup)
	}

	if err != nil || maxBakeup == "" {
		maxBackupInt = 24 * 7
	}

	fmt.Printf("log_maxbakeup=(%+v)\n", maxBackupInt)
	return NewLog("engine", level, filename, maxBackupInt)
}
