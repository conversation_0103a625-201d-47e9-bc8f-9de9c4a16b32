package collecter

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/olog"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/base"
	base_tool "dxm/siod_sre/auto-scaler/base/tool"
	"dxm/siod_sre/auto-scaler/dao"
	dao_ocean "dxm/siod_sre/auto-scaler/dao/ocean"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"errors"
	"sync"
	"time"
)

type CollectPod struct {
	ModuleInfo dao.Module
	StartTime  time.Time
	// 停止控制
	stopChan chan struct{}
	stopped  bool
	mu       sync.Mutex
}

var _ ICollecter = &CollectPod{}

var ICollectPod = CollectPod{
	stopChan: make(chan struct{}),
	stopped:  false,
}

func (c *CollectPod) Run() {
	c.mu.Lock()
	c.stopped = false
	c.mu.Unlock()

	plog.EngineLG.Info("CollectPod started")
	// 每分钟获取一次数据
	ticker := time.NewTicker(time.Second) // 每秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			plog.EngineLG.Info("CollectPod received stop signal, exiting...")
			return
		case tc := <-ticker.C:
			c.mu.Lock()
			if c.stopped {
				c.mu.Unlock()
				return
			}
			c.mu.Unlock()

			if tc.Second() == 1 { //每分钟1秒开始发起请求
				olog.Info("collect pod monitor info start, time:[%v]", time.Now())
				c.StartTime = time.Now()

				modelList, r := dao.CreateModulePtr().GetModelByType(base.MODULE_TYPE_POD)
				if !r.IsOk() {
					olog.Error("failed to get pod info by type, err:[%v]", r)
					return
				}

				for _, moudleInfo := range modelList {
					c.ModuleInfo = moudleInfo
					// 记录查询耗时
					selectTime := time.Now()
					res, err := c.GetAllMoniterIterms()
					if err != nil {
						olog.Error("failed to get moniter info for pod service, service:[%s], err:[%v]", c.ModuleInfo.ServiceName, err)
						continue
					}
					olog.Info("selcet pod monitor info, cost:[%v]s", time.Since(selectTime).Seconds())
					olog.Debug("res result:[%v]", res)
					// 存储数据

					insertTime := time.Now()
					c.StoreMoniterData(res)
					olog.Debug("insert pod monitor info, cost:[%v]s", time.Since(insertTime).Seconds())

					// 触发事件时间生成
					eventTime := time.Now()
					c.GenEvent(res)
					olog.Debug("gen event pod monitor info, cost:[%v]s", time.Since(eventTime).Seconds())
				}

				olog.Info("collect pod monitor info finished, cost:[%v]s", time.Since(c.StartTime).Seconds())
			}
		}
	}
}

// Stop 停止CollectPod
func (c *CollectPod) Stop() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.stopped {
		c.stopped = true
		close(c.stopChan)
		plog.EngineLG.Info("CollectPod stopped")
	}
}

func (c *CollectPod) GetAllMoniterIterms() (res []dao.MonitorInfo, err error) {
	// 查询容量平台的数据
	oceanMoudle, r := dao_ocean.CreateNoahModulesPtr().GetOceanModuleList(c.ModuleInfo.ServiceName)
	if !r.IsOk() {
		olog.Error("failed to get service module info from ocean database, err:[%v]", r)
		err = errors.New("failed to get service module info from ocean database")
		return
	}
	olog.Debug("query ocean moudle:[%v]", oceanMoudle)
	if len(oceanMoudle) == 0 {
		return
	}

	// 查询容量数据
	serviceOcean := dao_ocean.OceanDetailSearchLatestBetweenTime(
		oceanMoudle[0].Product,
		oceanMoudle[0].BnsName,
		base_tool.GetLastMinuteByTime(c.StartTime).Format(otool.TIME_FORMAT_STR),
		c.StartTime.Format(otool.TIME_FORMAT_STR))
	olog.Debug("query ocean data:[%v]", serviceOcean)

	// 合并物理机和pod监控
	var idcMap = make(map[string]dao_ocean.OceanDetail, 4)

	for _, v := range serviceOcean {
		if ok, _ := otool.IsInArray(v.LogicIdc, []string{"hba", "hbb", "hba-pod", "hbb-pod"}); !ok {
			res = append(res, dao.MonitorInfo{
				ModuleID:    c.ModuleInfo.ID,
				ServiceName: c.ModuleInfo.ServiceName,
				LogicIDC:    v.LogicIdc,
				InsCount:    v.InsCount,
				CPUAvg:      v.CpuAvg,
				CPUServ:     v.ServiceAppAvg,
				QPSAvg:      v.QpsAvg,
				CostAvg:     v.CostAvg,
				MemAvg:      v.MemAvg,
				Level:       v.Level,
				CollectTime: time.Now().Format(otool.TIME_FORMAT_STR),
			})
		} else {
			idcMap[v.LogicIdc] = v
		}
	}

	for _, v := range []string{"hba", "hbb", "hba-pod", "hbb-pod"} {
		if _, ok := idcMap[v]; !ok {
			idcMap[v] = dao_ocean.OceanDetail{}
		}
	}

	hbaValue := idcMap["hba"]
	hbaPodValue := idcMap["hba-pod"]
	hbbValue := idcMap["hbb"]
	hbbPodValue := idcMap["hbb-pod"]

	res = append(res, dao.MonitorInfo{
		ModuleID:    c.ModuleInfo.ID,
		ServiceName: c.ModuleInfo.ServiceName,
		LogicIDC:    "hba",
		InsCount:    hbaValue.InsCount + hbaPodValue.InsCount,
		CPUAvg: (hbaValue.CpuAvg*float64(hbaValue.InsCount) + hbaPodValue.CpuAvg*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		CPUServ: (hbaValue.ServiceAppAvg*float64(hbaValue.InsCount) + hbaPodValue.ServiceAppAvg*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		QPSAvg: (hbaValue.QpsAvg*float64(hbaValue.InsCount) + hbaPodValue.QpsAvg*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		CostAvg: (hbaValue.CostAvg*float64(hbaValue.InsCount) + hbaPodValue.CostAvg*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		MemAvg: (hbaValue.MemAvg*float64(hbaValue.InsCount) + hbaPodValue.MemAvg*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		Level: (hbaValue.Level*float64(hbaValue.InsCount) + hbaPodValue.Level*float64(hbaPodValue.InsCount)) /
			(float64((hbaValue.InsCount + hbaPodValue.InsCount)) + 0.001),
		CollectTime: time.Now().Format(otool.TIME_FORMAT_STR),
	})

	res = append(res, dao.MonitorInfo{
		ModuleID:    c.ModuleInfo.ID,
		ServiceName: c.ModuleInfo.ServiceName,
		LogicIDC:    "hbb",
		InsCount:    hbbValue.InsCount + hbbPodValue.InsCount,
		CPUAvg: (hbbValue.CpuAvg*float64(hbbValue.InsCount) + hbbPodValue.CpuAvg*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		CPUServ: (hbbValue.ServiceAppAvg*float64(hbbValue.InsCount) + hbbPodValue.ServiceAppAvg*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		QPSAvg: (hbbValue.QpsAvg*float64(hbbValue.InsCount) + hbbPodValue.QpsAvg*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		CostAvg: (hbbValue.CostAvg*float64(hbbValue.InsCount) + hbbPodValue.CostAvg*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		MemAvg: (hbbValue.MemAvg*float64(hbbValue.InsCount) + hbbPodValue.MemAvg*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		Level: (hbbValue.Level*float64(hbbValue.InsCount) + hbbPodValue.Level*float64(hbbPodValue.InsCount)) /
			(float64((hbbValue.InsCount + hbbPodValue.InsCount)) + 0.001),
		CollectTime: time.Now().Format(otool.TIME_FORMAT_STR),
	})

	return res, nil
}

func (c *CollectPod) GenEvent(eventData []dao.MonitorInfo) error {
	for _, v := range eventData {
		// 触发事件
		comm.ScalerCh.EventMonitorChan <- v
	}
	return nil
}

func (c *CollectPod) GetCpuMoniter() (float64, error) {

	return 0, nil
}

func (c *CollectPod) GetQpsMoniter() (float64, error) {

	return 0, nil
}

func (c *CollectPod) GetCostMoniter() (float64, error) {

	return 0, nil
}
func (c *CollectPod) GetLevelMoniter() (float64, error) {

	return 0, nil
}

func (c *CollectPod) StoreMoniterData(dataList []dao.MonitorInfo) {
	var r ocommon.ResultInfo
	for _, v := range dataList {

		_, r = dao.CreateMonitorInfoPtr().Insert(&v)
		if !r.IsOk() {
			olog.Error("failed to store monitor info, service:[%s], err:[%v]", v.ServiceName, r)
			continue
		}
	}
}
