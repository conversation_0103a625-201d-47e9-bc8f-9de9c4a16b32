package collecter

import (
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"dxm/siod_sre/auto-scaler/global"
	"sync"
)

// 接口
type ICollecter interface {
	GetAllMoniterIterms() ([]dao.MonitorInfo, error)
	GetCpuMoniter() (float64, error)
	GetQpsMoniter() (float64, error)
	GetCostMoniter() (float64, error)
	GetLevelMoniter() (float64, error)

	// 触发生成事件
	GenEvent(eventData []dao.MonitorInfo) error

	Run()
	Stop()
}

type CollecterManager struct {
	PodCollectManager   *CollectPod
	ModelCollectManager *CollectModel
	// 控制信号
	startChan chan bool
	stopChan  chan bool
	isRunning bool
	mu        sync.Mutex
}

var (
	CollectEngine *CollecterManager
)

func init() {
	CollectEngine = NewCollectEngine()
}

func NewCollectEngine() *CollecterManager {
	return &CollecterManager{
		PodCollectManager:   &ICollectPod,
		ModelCollectManager: &ICollectModel,
		startChan:           make(chan bool, 1),
		stopChan:            make(chan bool, 1),
		isRunning:           false,
	}
}

func (t *CollecterManager) Run() {
	plog.EngineLG.Info("collect engine run - waiting for master signal")

	for {
		select {
		case <-t.startChan:
			t.mu.Lock()
			if !t.isRunning {
				plog.EngineLG.Info("collect engine received start signal - starting collectors")
				t.isRunning = true
				// 重新初始化停止通道（如果之前被关闭了）
				t.PodCollectManager.mu.Lock()
				if t.PodCollectManager.stopped {
					t.PodCollectManager.stopChan = make(chan struct{})
					t.PodCollectManager.stopped = false
				}
				t.PodCollectManager.mu.Unlock()

				if global.YunEnv == "yun_env_xd" {
					t.ModelCollectManager.mu.Lock()
					if t.ModelCollectManager.stopped {
						t.ModelCollectManager.stopChan = make(chan struct{})
						t.ModelCollectManager.stopped = false
					}
					t.ModelCollectManager.mu.Unlock()
				}

				go t.PodCollectManager.Run()
				if global.YunEnv == "yun_env_xd" {
					go t.ModelCollectManager.Run()
				}
			}
			t.mu.Unlock()

		case <-t.stopChan:
			t.mu.Lock()
			if t.isRunning {
				plog.EngineLG.Info("collect engine received stop signal - stopping collectors")
				t.isRunning = false
				// 停止PodCollectManager和ModelCollectManager
				t.PodCollectManager.Stop()
				if global.YunEnv == "yun_env_xd" {
					t.ModelCollectManager.Stop()
				}
			}
			t.mu.Unlock()
		}
	}
}

// SendStartSignal 发送启动信号
func (t *CollecterManager) SendStartSignal() {
	select {
	case t.startChan <- true:
		plog.EngineLG.Info("sent start signal to collect engine")
	default:
		plog.EngineLG.Debug("start signal already pending for collect engine")
	}
}

// SendStopSignal 发送停止信号
func (t *CollecterManager) SendStopSignal() {
	select {
	case t.stopChan <- true:
		plog.EngineLG.Info("sent stop signal to collect engine")
	default:
		plog.EngineLG.Debug("stop signal already pending for collect engine")
	}
}
