package collecter

import (
	"testing"
	"time"
)

func TestCollectPodStop(t *testing.T) {
	// 创建一个新的CollectPod实例
	collectPod := &CollectPod{
		stopChan: make(chan struct{}),
		stopped:  false,
	}
	
	// 启动CollectPod（在goroutine中运行）
	go collectPod.Run()
	
	// 等待一小段时间让它开始运行
	time.Sleep(100 * time.Millisecond)
	
	// 停止CollectPod
	collectPod.Stop()
	
	// 等待一小段时间确保停止完成
	time.Sleep(100 * time.Millisecond)
	
	// 验证停止状态
	collectPod.mu.Lock()
	if !collectPod.stopped {
		t.Error("CollectPod should be stopped")
	}
	collectPod.mu.Unlock()
	
	t.Log("CollectPod stop test passed")
}

func TestCollectModelStop(t *testing.T) {
	// 创建一个新的CollectModel实例
	collectModel := &CollectModel{
		stopChan: make(chan struct{}),
		stopped:  false,
	}
	
	// 启动CollectModel（在goroutine中运行）
	go collectModel.Run()
	
	// 等待一小段时间让它开始运行
	time.Sleep(100 * time.Millisecond)
	
	// 停止CollectModel
	collectModel.Stop()
	
	// 等待一小段时间确保停止完成
	time.Sleep(100 * time.Millisecond)
	
	// 验证停止状态
	collectModel.mu.Lock()
	if !collectModel.stopped {
		t.Error("CollectModel should be stopped")
	}
	collectModel.mu.Unlock()
	
	t.Log("CollectModel stop test passed")
}

func TestCollecterManagerStopControl(t *testing.T) {
	// 创建CollecterManager
	manager := NewCollectEngine()
	
	// 启动manager（在goroutine中运行）
	go manager.Run()
	
	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)
	
	// 发送启动信号
	manager.SendStartSignal()
	time.Sleep(200 * time.Millisecond)
	
	// 发送停止信号
	manager.SendStopSignal()
	time.Sleep(200 * time.Millisecond)
	
	// 再次发送启动信号
	manager.SendStartSignal()
	time.Sleep(200 * time.Millisecond)
	
	// 再次发送停止信号
	manager.SendStopSignal()
	time.Sleep(200 * time.Millisecond)
	
	t.Log("CollecterManager stop control test completed")
}
