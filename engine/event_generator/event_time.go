package event_generator

import (
	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"fmt"
	"math/rand"
	"time"
)

type EventGeneratorTime struct {
	ModuleInfo dao.Module
	TaskInfo   dao.TaskInfo
	RuleInfo   dao.RuleOnline
	EventInfo  *dao.EventInfo
	TraceID    string
}

var _ IEventGenerator = &EventGeneratorTime{}

var IEventGeneratorTime EventGeneratorTime

func (e *EventGeneratorTime) RunOne() {
	if !e.GenEvent() {
		return
	}

	e.StorageEvent()
	e.TriggerEvent()
}

func (e *EventGeneratorTime) Init() (ok bool) {
	var r ocommon.ResultInfo
	e.ModuleInfo, e.TaskInfo, e.RuleInfo, r = comm.GetMoudleOneConfig(e.RuleInfo.ModuleId, e.RuleInfo.TaskId, e.RuleInfo.ID)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("get data from database failed, err:%v", r)
		return
	}
	e.TraceID = e.GenerateTraceID()
	return true
}

func (e *EventGeneratorTime) GenName() string {
	// 任务类型_模块名_模块ID_任务ID_规则ID
	return fmt.Sprintf("%s_%d_%d_%d", base.TASK_TYPE_CRONTAB, e.ModuleInfo.ID, e.TaskInfo.ID, e.RuleInfo.ID)
}

func (e *EventGeneratorTime) GenEvent() (ok bool) {
	var action string
	if e.TaskInfo.SchedScaleInRule == e.RuleInfo.ID {
		action = base.ACTION_DOWN
	} else if e.TaskInfo.SchedScaleOutRule == e.RuleInfo.ID {
		action = base.ACTION_UP
	}

	e.EventInfo = &dao.EventInfo{
		ModuleId:       e.ModuleInfo.ID,
		ServiceName:    e.ModuleInfo.ServiceName,
		LogicIdc:       e.TaskInfo.IdcTag,
		TaskId:         e.TaskInfo.ID,
		RuleId:         e.RuleInfo.ID,
		Type:           base.EVENT_TYPE_TIME,
		CreateTime:     time.Now(),
		LastModifyTime: time.Now(),
		Action:         action,
	}
	return true
}

func (e *EventGeneratorTime) StorageEvent() (ok bool) {
	eventId, r := dao.CreateEventInfoPtr().Insert(e.EventInfo)
	if !r.IsOk() {
		plog.EngineLG.WithFields(e.Fields()).Errorf("insert data to event_info failed, err:%v", r)
		return
	}
	e.EventInfo.Id = eventId
	return true
}

func (e *EventGeneratorTime) TriggerEvent() (ok bool) {
	comm.ScalerCh.DecisionChan <- &comm.ModuleOneTaskParam{
		ModuleInfo: e.ModuleInfo,
		TaskInfo:   e.TaskInfo,
		RuleInfo:   e.RuleInfo,
		EventInfo:  *e.EventInfo,
		TraceID:    e.GenerateTraceID(),
	}
	return true
}

// func (e *EventGeneratorTime) exitErrorPrint() {
// 	if e.EventErr != nil {
// 		olog.Error("self event generator running failed, module_name:[%s], module_id:[%d], task_id:[%d], rule_id:[%d], err:[%v]",
// 			e.ModuleInfo.Name, e.ModuleInfo.ID, e.RuleInfo.ID, e.RuleInfo.ID, e.EventErr)
// 	}
// }

func (e *EventGeneratorTime) GenerateTraceID() string {
	const hexChars = "0123456789abcdef"
	const traceIDLength = 32 // 16字节的16进制表示
	b := make([]byte, traceIDLength)
	for i := range b {
		b[i] = hexChars[rand.Intn(len(hexChars))]
	}
	return fmt.Sprintf("%s_%s", e.ModuleInfo.Name, string(b))
}

func (e *EventGeneratorTime) Fields() map[string]interface{} {
	return map[string]interface{}{
		"module_id":   e.ModuleInfo.ID,
		"module_name": e.ModuleInfo.Name,
		"task_id":     e.TaskInfo.ID,
		"rule_id":     e.RuleInfo.ID,
		"trace_id":    e.TraceID,
	}
}
