package event_generator

import (
	"encoding/json"
	"testing"
	"time"

	"dxm/siod-cloud/go-common-lib/ocommon"
	"dxm/siod-cloud/go-common-lib/otool"
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"

	"bou.ke/monkey"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockComm struct {
	mock.Mock
}

// comm.ComputeMetricLimit = mockComm.ComputeMetricLimit
func TestGenEvent(t *testing.T) {
	moduleInfo := dao.Module{ID: 1, Name: "module_name"}
	taskInfo := dao.TaskInfo{ID: 2}
	ruleInfo := dao.RuleOnline{ID: 3, TaskId: 2, TargetMetric: `[{"MetricName":"cpu_percent","Operator":"gt","Threshold":80}]`}
	monitorInfo := dao.MonitorInfo{LogicIDC: "logic_idc1", CollectTime: time.Now().Format(otool.TIME_FORMAT_STR), CPUAvg: 90.0}

	mockComm := new(MockComm)

	mockComm.On("ComputeMetricLimit", base.MetricInfo{MetricName: "cpu_percent", TargetValue: 35, TargetRateMax: 0.2, TargetRateMin: 0.3}).Return(85.0, 75.0)

	eventGenerator := &EventGeneratorMonitor{
		ModuleInfo:  moduleInfo,
		TaskInfo:    taskInfo,
		RuleInfo:    ruleInfo,
		MonitorInfo: monitorInfo,
		TraceID:     "trace1",
	}

	ok := eventGenerator.GenEvent()
	assert.True(t, ok)

	expectedEventInfo := &dao.EventInfo{
		ModuleId:       moduleInfo.ID,
		ServiceName:    moduleInfo.Name,
		LogicIdc:       monitorInfo.LogicIDC,
		TaskId:         ruleInfo.TaskId,
		RuleId:         ruleInfo.ID,
		Type:           base.EVENT_TYPE_MONITOR,
		Action:         base.ACTION_UP,
		CreateTime:     eventGenerator.EventInfo.CreateTime,
		LastModifyTime: eventGenerator.EventInfo.LastModifyTime,
	}

	expectedMetricJson, _ := json.Marshal([]base.MetricInfo{{MetricName: "cpu_percent", TargetValue: 35, TargetRateMax: 0.2, TargetRateMin: 0.3}})
	expectedEventInfo.RuleInfo = string(expectedMetricJson)

	assert.Equal(t, expectedEventInfo.ModuleId, eventGenerator.EventInfo.ModuleId)
	assert.Equal(t, expectedEventInfo.ServiceName, eventGenerator.EventInfo.ServiceName)
	assert.Equal(t, expectedEventInfo.LogicIdc, eventGenerator.EventInfo.LogicIdc)
	assert.Equal(t, expectedEventInfo.TaskId, eventGenerator.EventInfo.TaskId)
	assert.Equal(t, expectedEventInfo.RuleId, eventGenerator.EventInfo.RuleId)
	assert.Equal(t, expectedEventInfo.Type, eventGenerator.EventInfo.Type)
	assert.Equal(t, expectedEventInfo.Action, eventGenerator.EventInfo.Action)
	assert.Equal(t, expectedEventInfo.RuleInfo, eventGenerator.EventInfo.RuleInfo)
	assert.WithinDuration(t, expectedEventInfo.CreateTime, eventGenerator.EventInfo.CreateTime, 1*time.Second)
	assert.WithinDuration(t, expectedEventInfo.LastModifyTime, eventGenerator.EventInfo.LastModifyTime, 1*time.Second)
}

type MockDao struct{}

type EventInfoDao interface {
	Insert(info *dao.EventInfo) (int, ocommon.ResultInfo) // 根据实际方法定义
}

func (m *MockDao) Insert(eventInfo *dao.EventInfo) (int, ocommon.ResultInfo) {
	if eventInfo == nil {
		return 0, ocommon.ResultInfo{}
	}
	return 1, ocommon.ResultInfo{}
}

func TestStorageEvent(t *testing.T) {
	// 测试用例1: EventInfo is nil
	t.Run("NilEventInfo", func(t *testing.T) {
		mockDao := &MockDao{}

		// 使用 monkey patching
		patch := monkey.Patch(dao.CreateEventInfoPtr, func() EventInfoDao {
			return mockDao
		})
		defer patch.Unpatch()

		eventGenerator := &EventGeneratorMonitor{}
		result := eventGenerator.StorageEvent()
		assert.False(t, result)
	})

	// 测试用例2: Insertion successful
	t.Run("InsertSuccess", func(t *testing.T) {
		mockDao := &MockDao{}

		// 使用 monkey patching
		patch := monkey.Patch(dao.CreateEventInfoPtr, func() EventInfoDao {
			return mockDao
		})
		defer patch.Unpatch()

		eventGenerator := &EventGeneratorMonitor{
			EventInfo: &dao.EventInfo{
				Id: 0,
			},
		}
		result := eventGenerator.StorageEvent()
		assert.True(t, result)
		assert.Equal(t, 1, eventGenerator.EventInfo.Id)
	})

	// 测试用例3: Insertion fails
	t.Run("InsertFailure", func(t *testing.T) {
		mockDao := &MockDao{}

		// 使用 monkey patching
		patch := monkey.Patch(dao.CreateEventInfoPtr, func() EventInfoDao {
			return mockDao
		})
		defer patch.Unpatch()

		eventGenerator := &EventGeneratorMonitor{
			EventInfo: &dao.EventInfo{
				Id: 0,
			},
		}
		result := eventGenerator.StorageEvent()
		assert.False(t, result)
	})
}

func TestGetCurrentMetricValue(t *testing.T) {
	tests := []struct {
		name       string
		metricName string
		expected   *float64
	}{
		{
			name:       "CPU Percent",
			metricName: base.METRIC_NAME_CPU_PERCENT,
			expected:   floatPtr(80.5),
		},
		{
			name:       "App CPU Percent",
			metricName: base.METRIC_NAME_APP_CPU_PERCENT,
			expected:   floatPtr(75.2),
		},
		{
			name:       "Average Cost",
			metricName: base.METRIC_NAME_AVERAGE_COST,
			expected:   floatPtr(10.3),
		},
		{
			name:       "Single Instance QPS",
			metricName: base.METRIC_NAME_SINGLE_INSTANCE_QPS,
			expected:   floatPtr(120.0),
		},
		{
			name:       "Water Level",
			metricName: base.METRIC_NAME_WATER_LEVEL,
			expected:   floatPtr(20.1),
		},
		{
			name:       "Unknown Metric",
			metricName: "unknown_metric",
			expected:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			monitorInfo := dao.MonitorInfo{
				CPUAvg:  80.5,
				CPUServ: 75.2,
				CostAvg: 10.3,
				QPSAvg:  120.0,
				Level:   20.1,
			}

			egm := &EventGeneratorMonitor{
				MonitorInfo: monitorInfo,
			}

			result := egm.getCurrentMetricValue(tt.metricName)
			if result == nil && tt.expected == nil {
				return
			}
			if result == nil || tt.expected == nil || *result != *tt.expected {
				t.Errorf("getCurrentMetricValue(%s) = %v, want %v", tt.metricName, result, tt.expected)
			}
		})
	}
}

func floatPtr(f float64) *float64 {
	return &f
}
