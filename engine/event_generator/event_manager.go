package event_generator

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
)

type IEventGenerator interface {
	Init() bool
	GenEvent() bool
	StorageEvent() bool
	TriggerEvent() bool
	GenerateTraceID() string

	RunOne()
	GenName() string
}

var EventManager *EventGeneratorManager

type EventGeneratorManager struct {
	EventInfo map[string]IEventGenerator
}

func NewEventManager() {
	EventManager = &EventGeneratorManager{
		EventInfo: make(map[string]IEventGenerator, 10),
	}
}

func init() {
	NewEventManager()
}

func (t *EventGeneratorManager) Run() {
	// 运行所有组件
	plog.EngineLG.Info("event generator engine run")
	for {
		select {
		case me := <-comm.ScalerCh.EventMonitorChan:
			go func() {
				e := NewEventGenerator(base.EVENT_TYPE_MONITOR, me)
				e.Init()
				(*t).EventInfo[e.GenName()] = e
				e.RunOne()
			}()
		case ce := <-comm.ScalerCh.EventCornChan:
			go func() {
				e := NewEventGenerator(base.EVENT_TYPE_TIME, ce)
				e.Init()
				(*t).EventInfo[e.GenName()] = e
				e.RunOne()
			}()

		case oe := <-comm.ScalerCh.EventOtherChan:
			e := NewEventGenerator(base.EVENT_TYPE_OTHER, oe)
			e.Init()
			(*t).EventInfo[e.GenName()] = e
			e.RunOne()
		}
	}
}

func NewEventGenerator(typeT string, p interface{}) (e IEventGenerator) {
	switch typeT {
	case base.EVENT_TYPE_MONITOR:
		return &EventGeneratorMonitor{
			MonitorInfo: p.(dao.MonitorInfo),
		}
	case base.EVENT_TYPE_TIME:
		return &EventGeneratorTime{
			RuleInfo: p.(dao.RuleOnline),
		}
	case base.EVENT_TYPE_OTHER:
		return &EventGeneratorOther{
			RuleInfo: p.(dao.RuleOnline),
		}
	}
	return nil
}
