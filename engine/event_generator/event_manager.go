package event_generator

import (
	"dxm/siod_sre/auto-scaler/base"
	"dxm/siod_sre/auto-scaler/dao"
	"dxm/siod_sre/auto-scaler/engine/comm"
	"dxm/siod_sre/auto-scaler/engine/plog"
	"sync"
	"time"
)

type IEventGenerator interface {
	Init() bool
	GenEvent() bool
	StorageEvent() bool
	TriggerEvent() bool
	GenerateTraceID() string

	RunOne()
	GenName() string
}

var EventManager *EventGeneratorManager

type EventGeneratorManager struct {
	EventInfo sync.Map // 使用sync.Map替代普通map，支持并发安全
	// 添加清理机制相关字段
	lastCleanup time.Time
	cleanupMu   sync.Mutex
}

func NewEventManager() {
	EventManager = &EventGeneratorManager{
		lastCleanup: time.Now(),
	}
	// 启动定期清理goroutine
	go EventManager.startCleanupRoutine()
}

func init() {
	NewEventManager()
}

func (t *EventGeneratorManager) Run() {
	// 运行所有组件
	plog.EngineLG.Info("event generator engine run")
	for {
		select {
		case me := <-comm.ScalerCh.EventMonitorChan:
			go func(monitorInfo dao.MonitorInfo) {
				e := NewEventGenerator(base.EVENT_TYPE_MONITOR, monitorInfo)
				if e == nil {
					plog.EngineLG.Error("failed to create monitor event generator")
					return
				}
				if !e.Init() {
					plog.EngineLG.Error("failed to init monitor event generator")
					return
				}
				name := e.GenName()
				t.EventInfo.Store(name, e)
				e.RunOne()
				// 执行完成后清理
				t.EventInfo.Delete(name)
			}(me)
		case ce := <-comm.ScalerCh.EventCornChan:
			go func(ruleInfo dao.RuleOnline) {
				e := NewEventGenerator(base.EVENT_TYPE_TIME, ruleInfo)
				if e == nil {
					plog.EngineLG.Error("failed to create time event generator")
					return
				}
				if !e.Init() {
					plog.EngineLG.Error("failed to init time event generator")
					return
				}
				name := e.GenName()
				t.EventInfo.Store(name, e)
				e.RunOne()
				// 执行完成后清理
				t.EventInfo.Delete(name)
			}(ce)

		case oe := <-comm.ScalerCh.EventOtherChan:
			go func(ruleInfo dao.RuleOnline) {
				e := NewEventGenerator(base.EVENT_TYPE_OTHER, ruleInfo)
				if e == nil {
					plog.EngineLG.Error("failed to create other event generator")
					return
				}
				if !e.Init() {
					plog.EngineLG.Error("failed to init other event generator")
					return
				}
				name := e.GenName()
				t.EventInfo.Store(name, e)
				e.RunOne()
				// 执行完成后清理
				t.EventInfo.Delete(name)
			}(oe)
		}
	}
}

func NewEventGenerator(typeT string, p interface{}) (e IEventGenerator) {
	switch typeT {
	case base.EVENT_TYPE_MONITOR:
		return &EventGeneratorMonitor{
			MonitorInfo: p.(dao.MonitorInfo),
		}
	case base.EVENT_TYPE_TIME:
		return &EventGeneratorTime{
			RuleInfo: p.(dao.RuleOnline),
		}
	case base.EVENT_TYPE_OTHER:
		return &EventGeneratorOther{
			RuleInfo: p.(dao.RuleOnline),
		}
	}
	return nil
}

// startCleanupRoutine 启动定期清理routine
func (t *EventGeneratorManager) startCleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		t.cleanup()
	}
}

// cleanup 清理过期的事件生成器
func (t *EventGeneratorManager) cleanup() {
	t.cleanupMu.Lock()
	defer t.cleanupMu.Unlock()

	now := time.Now()
	if now.Sub(t.lastCleanup) < time.Minute {
		return // 避免频繁清理
	}

	count := 0
	t.EventInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	plog.EngineLG.Debug("EventGeneratorManager cleanup: current active generators: %d", count)
	t.lastCleanup = now
}

// GetActiveCount 获取当前活跃的事件生成器数量
func (t *EventGeneratorManager) GetActiveCount() int {
	count := 0
	t.EventInfo.Range(func(key, value interface{}) bool {
		count++
		return true
	})
	return count
}
